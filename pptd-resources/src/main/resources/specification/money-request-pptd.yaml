openapi: 3.0.3
info:
  title: Domestic E-Transfer Payment Event Publisher Streaming Message
  description: 'This is a description for Kafka messages sent to the publisher by the Domestic ETransfer Payment business capacity to be historised in PPTD (pptd etransfer pmt trx).'
  version: 0.0.6
  x-version-history:
    '0.0.1': 2024-04-04 - Creation first draft
    '0.0.2': 2024-06-04 - Change field clientType to be optional
    '0.0.3': 2024-06-04 - Change feilds headers in the payload
      - Restructuring of raw data
      - Adding the object FitoFiStatusReport
      - Adding other fiels to be aligned with Yves' model
    '0.0.4': 2024-10-07 - Modify enum for field eventType and others like creditorId.
    '0.0.5': 2024-10-29 - Add creationDateTime in the rawData headers.
    '0.0.6': 2025-01-17 - Put fields, instructing, instructed agent and settlement_information optionnals.
tags:
  - name: domesticETransferEventPublisher
    description: Message definitions used to publish a Domestic ETransfer Payment event

paths: { }

components:
  schemas:
    DomesticETransferEvent:
      description: Kafka message for the Domestic ETransfer Payment Event
      type: object
      required:
        - version
        - entity
        - eventType
        - eventTime
        - instructionIdentification
        - endToEndBusinessIdentification
        - channelId
        - channelType
        - rawData
      properties:
        version:
          $ref: '#/components/schemas/Version'
        entity:
          $ref: '#/components/schemas/Entity'
        eventType:
          $ref: '#/components/schemas/EventType'
        eventTime:
          $ref: '#/components/schemas/EventTime'
        instructionIdentification:
          $ref: '#/components/schemas/InstructionIdentification'
        endToEndBusinessIdentification:
          $ref: '#/components/schemas/EndToEndBusinessIdentification'
        channelId:
          $ref: '#/components/schemas/ChannelId'
        channelType:
          $ref: '#/components/schemas/ChannelType'
        partnerId:
          $ref: '#/components/schemas/PartnerId'
        clientId:
          $ref: '#/components/schemas/ClientId'
        clientType:
          $ref: '#/components/schemas/ClientType'
        clientAgentId:
          $ref: '#/components/schemas/ClientAgentId'
        agentId:
          $ref: '#/components/schemas/AgentId'
        paymentTypeCode:
          $ref: '#/components/schemas/PaymentTypeCode'
        msgDefIdr:
          $ref: '#/components/schemas/MsgDefIdr'
        rawData:
          $ref: '#/components/schemas/DomesticETransferEventRawData'
    DomesticETransferEventRawData:
      title: DomesticETransferEventRawData
      type: object
      required:
        - instructionIdentification
        - endToEndBusinessIdentification
        - approvalRequired
        - messageDefinitionIdentifier
        - creationDatetime
      properties:
        instructionIdentification:
          $ref: '#/components/schemas/InstructionIdentification'
        endToEndBusinessIdentification:
          $ref: '#/components/schemas/EndToEndBusinessIdentification'
        approvalRequired:
          $ref: '#/components/schemas/ApprovalRequired'
        messageDefinitionIdentifier:
          $ref: '#/components/schemas/MsgDefIdr'
        clientId:
          $ref: '#/components/schemas/ClientId'
        clientAgentId:
          $ref: '#/components/schemas/ClientAgentId'
        agentId:
          $ref: '#/components/schemas/AgentId'
        creationDatetime: # CreDtTm
          $ref: '#/components/schemas/ISODateTime'
        messageData:
          type: object
          properties:
            fiToFiCustomerCreditTransfer:
              $ref: '#/components/schemas/FIToFICustomerCreditTransfer'

        supplementaryData:
          $ref: '#/components/schemas/SupplementaryData'


    EventTime:
      type: string
      format: date-time
      description: >-
        Represents the "User Action Date" (time of the event not the publication date).
      example: 2021-08-28T09:24:30.000Z

    Entity:
      type: string
      description: >-
        Represents the asset (CRUD) or the business capacity (Payment transaction) of the event.
      enum:
        - DOMESTIC_ETRANSFER_PAYMENT

    EventType:
      type: string
      enum:
        - PAYMENT-TRANSACTION-DATA-EXPORTED
      description: >-
        Type of business event.
      example: "PAYMENT-TRANSACTION-DATA-EXPORTED"

    ChannelType:
      description: >-
        Indicates the channel type.
      type: string
      enum: [ 'MOBILE','WEB','INTERAC' ]
      example: 'MOBILE'

    ChannelId:
      type: string
      description: >-
        The identification of the asset at the origin of the publication.
      example: "6256"

    ClientId:
      type: string
      description: >-
        The identification number of the client within the FI system  (ex.: bncId) who initiated the event.
      example: "2C811E20AEE80A2860764937595FFE76DBF72788941F0C7726CC626949350900"

    ClientType:
      type: string
      description: >-
        The type of client who initiated the payment instruction. Retail/Individual transactions vs Commercial/Organisation transaction (I, O)
      enum:
        - I
        - O
      example: 'I'

    PartnerId:
      type: string
      description: >-
        Identifier of the partnerId using NBC services.
      example: "PARTNERSHIP"
    ClientAgentId:
      description: |
        The bncId of the authenticated employee performing an action on behalf of his employer, who is a commercial client of the bank.
      type: string
      minLength: 1
      maxLength: 128
      example : "30BD2E5746C6013778F4CD84D4533FAE6CF80C8C6F52C686691562F452D5FAZ0"
    AgentId:
      description: |
        The shortid of the agent that initiate the payment instruction.
      type: string
      example: 'abcd123'
      minLength: 1

    EndToEndBusinessIdentification:
      type: string
      maxLength: 36
      description: >-
        Business transaction identifier used across the FI systems (end-to-end chain).
      example: 'ADA78940643E4D70BE143BBB36992095'

    InstructionIdentification:
      type: string
      description: >-
        InstructionIdentification is a unique transaction identifier auto generated by the producer. One InstructionId per request. Recommended a uuid without dashes.
      example: "95D2E46DDC124510BFEAB5C268A206B2"
      minLength: 1
      maxLength: 35

    MsgDefIdr:
      type: string
      description: >-
        Specifies the rawData content. For example, it can contain the MessageIdentifier which defines the Business message(PIM)
      example: "pacs.008.001.08"

    Version:
      description: >-
        Version of the microservice that originally created the event represented by a standard versioning format [Major].[Minor].[Patch].
      type: string
      example: '1.0.3'

    PaymentTypeCode:
      description: >-
        Payment transaction code
      type: string
      enum: [ 'AD','RAD', 'ANR', 'INS', 'QA', 'FUL', 'CANC' ]
      example: 'AD'

    AccountIdentification4Choice:
      type: object
      properties:
        other:
          $ref: '#/components/schemas/GenericAccountIdentification1'
      required:
        - other
    ActiveCurrencyAndAmount:
      type: object
      required:
        - amount
        - currency
      properties:
        amount:
          $ref: '#/components/schemas/ActiveCurrencyAndAmount_SimpleType'
        currency:
          $ref: '#/components/schemas/ActiveCurrencyCode'
    ActiveCurrencyAndAmount_SimpleType:
      type: number
      #format: decimal  # fractionDigits=5, totalDigits=18, minInclusive=0
      minimum: 0.01
      maximum: ***********999
      multipleOf: 0.01
      x-multipleOf: 0.01
      x-precision:
        decimal: 2
        total-digits: 14
      example: 44.44
      x-decimal-digits:
        code: 5000
        fraction-digits: 2
        total-digits: 14
    ActiveCurrencyCode:
      type: string
      x-example: CAD
      x-enum-examples: ['CAD', 'USD']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['CAD', 'USD']
    ActiveOrHistoricCurrencyAndAmount:
      type: object
      required:
        - amount
        - currency
      properties:
        amount:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount_SimpleType'
        currency:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyCode'
    ActiveOrHistoricCurrencyAndAmount_SimpleType:
      type: number
      #format: decimal  # fractionDigits=5, totalDigits=18, minInclusive=0
      minimum: 0
      maximum: ***********9999999
      multipleOf: 0.00001
      x-multipleOf: 0.00001
      example: 55.00055
      x-precision:
        decimal: 5
        total-digits: 18
      x-decimal-digits:
        code: 5000
        fraction-digits: 5
        total-digits: 18
    ActiveOrHistoricCurrencyCode:
      type: string
      x-example: CAD
      x-enum-examples: ['CAD', 'USD']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['CAD', 'USD']
    AddressType2Code:
      type: string
      enum: ['ADDR', 'PBOX', 'HOME', 'BIZZ', 'MLTO', 'DLVY']
      description: Identifies the nature of the postal address. Type of address expressed
        as a code. Valid values - ADDR, PBOX, HOME, BIZZ, MLTO, DLVY. Conditional
        - either the Code element or (exclusive or) the Proprietary  section can be
        present
      x-code-enum: 2031
      x-example: 'ADDR'
    AddressType3Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/AddressType2Code'
        - type: object
          properties:
            proprietary:
              $ref: '#/components/schemas/GenericIdentification30'
      description: Wrapper element for the address type information, Either the Code
        element or (exclusive or) the Proprietary  section can be present This AddressType
        section is available and can be used only for the PreviousInstructingAgent1.
        It is not present for the other parties such as Debtor, UltimateDebtor, InititatingParty,
        Creditor, UltimateCreditor, Invoicer, Invoicee, etc.
    # Remittance #
    AnyBICDec2014Identifier:
      type: string
      pattern: '[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}'
      description: >
        Business identification code of the organisation.
        Follows the format of the ISO 9362 Registration Authority, as described in
        ISO 9362:2014 - "Banking - Banking telecommunication messages - Business identifier
        code (BIC)"
      example: AAAADEBBXXX
    BICFIDec2014Identifier:
      type: string
      pattern: '[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}'
    BaseOneRate:
      type: number
      #format: decimal  # fractionDigits=10, totalDigits=11
      minimum: 0
      maximum: ***********
      multipleOf: 0.**********
      x-multipleOf: 0.**********
      example: 5.**********
      x-precision:
        decimal: 10
        total-digits: 11
    BranchAndFinancialInstitutionIdentification6:
      type: object
      required:
        - financial_institution_identification
      properties:
        financial_institution_identification:  # FinInstnId
          $ref: '#/components/schemas/FinancialInstitutionIdentification18'
    CashAccount38:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          $ref: '#/components/schemas/AccountIdentification4Choice'
        proxy: # Prxy
          $ref: '#/components/schemas/ProxyAccountIdentification1'
    CategoryPurpose1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalCategoryPurpose1Code'
      required:
        - code
    ChargeBearerType1Code:
      type: string
      description: This identifies which party(ies) will pay changer due for processing
        this transaction. Value is set to 'SLEV', which means Following Service Level
      enum: ['SLEV']
      x-example: SLEV
    ClearingSystemIdentification2Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalClearingSystemIdentification1Code'
        - type: object
          properties:
            proprietary:
              description: >-
                Identification code for a clearing system (in a proprietary format),
                that has not yet been identified in the list of clearing systems.
                Exclusivity Rule: either the Code or (exclusive or) the Proprietary
                element can be present
              type: string
              minLength: 1
              maxLength: 35
    ClearingSystemIdentification3Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              description: >-
                The value RTR is used to indicate that this payment has traveled / has
                been routed through the RTR Exchange system.
                Exclusivity Rule: Either the Code or (exclusive or) Proprietary element
                must be present, not both.
              type: string
              enum: ['RTR']
              x-example: RTR
        - type: object
          properties:
            proprietary:
              description: >-
                The proprietary value ETR (meaning Interac e-Transfer) is used to indicate
                that this payment has not traveled / has not been routed through the
                RTR Exchange system.
                Exclusivity Rule: Either the Code or (exclusive or) Proprietary element
                must be present, not both.
              type: string
              enum: ['ETR']
              x-example: ETR
      x-mutually-exclusive:
        code: 400
        notNull: 5000
        allowed:
          - 'code'
          - 'proprietary'
    ClearingSystemMemberIdentification2:
      type: object
      required:
        - member_identification
      properties:
        clearing_system_identification:  # ClrSysId
          $ref: '#/components/schemas/ClearingSystemIdentification2Choice'
        member_identification: # MmbId
          description: >-
            Identification of the Instructed/Instructing/Debtor/Creditor Agent.
            The value 'NOTPROVIDED' in the MemberIdentification element can be used
            when this information is not provided or is not available.
          type: string
          minLength: 1
          maxLength: 35
    ClearingSystemReference:
      description: The Interac-generated payment or request for payment reference
        numbers, also known as the clearing system reference in ISO 20022.
      type: string
      minLength: 8
      maxLength: 35
      example: AW45632177376543
    Contact4:
      type: object
      properties:
        name: # Nm
          type: string
          minLength: 1
          maxLength: 140
          x-secure-logging:
            mask: true
            mask-type: name
        phone_number: # PhneNb
          $ref: '#/components/schemas/PhoneNumber'
        mobile_number: # MobNb
          $ref: '#/components/schemas/PhoneNumber'
        fax_number: # FaxNb
          $ref: '#/components/schemas/PhoneNumber'
        email_address: # EmailAdr
          type: string
          minLength: 1
          maxLength: 2048
          format: email
          example: <EMAIL>
          x-secure-logging:
            mask: true
            mask-type: email_address
        department: # Dept
          $ref: '#/components/schemas/Max70Text'
    CountryCode:
      type: string
      description: Only ISO 3166 Alpha-2 codes are allowed.
      x-example: CA
      x-enum-examples: ['AA', 'AB', 'AD', 'AE', 'AF', 'AG', 'AI', 'AL', 'AM', 'AN',
                        'AO', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AW', 'AX', 'AZ', 'BA', 'BB', 'BD', 'BE',
                        'BF', 'BG', 'BH', 'BI', 'BJ', 'BL', 'BM', 'BN', 'BO', 'BQ', 'BR', 'BS', 'BT',
                        'BV', 'BW', 'BY', 'BZ', 'C2', 'CA', 'CC', 'CD', 'CF', 'CG', 'CH', 'CI', 'CK',
                        'CL', 'CM', 'CN', 'CO', 'CR', 'CU', 'CV', 'CW', 'CX', 'CY', 'CZ', 'DE', 'DJ',
                        'DK', 'DM', 'DO', 'DZ', 'EC', 'EE', 'EG', 'EH', 'ER', 'ES', 'ET', 'FI', 'FJ',
                        'FK', 'FM', 'FO', 'FR', 'GA', 'GB', 'GD', 'GE', 'GF', 'GG', 'GH', 'GI', 'GL',
                        'GM', 'GN', 'GP', 'GQ', 'GR', 'GS', 'GT', 'GU', 'GW', 'GY', 'HK', 'HM', 'HN',
                        'HR', 'HT', 'HU', 'ID', 'IE', 'IL', 'IM', 'IN', 'IO', 'IQ', 'IR', 'IS', 'IT',
                        'JE', 'JM', 'JO', 'JP', 'K1', 'KE', 'KG', 'KH', 'KI', 'KM', 'KN', 'KP', 'KR',
                        'KW', 'KY', 'KZ', 'LA', 'LB', 'LC', 'LI', 'LK', 'LR', 'LS', 'LT', 'LU', 'LV',
                        'LY', 'MA', 'MC', 'MD', 'ME', 'MF', 'MG', 'MH', 'MK', 'ML', 'MM', 'MN', 'MO',
                        'MP', 'MQ', 'MR', 'MS', 'MT', 'MU', 'MV', 'MW', 'MX', 'MY', 'MZ', 'NA', 'NC',
                        'NE', 'NF', 'NG', 'NI', 'NL', 'NO', 'NP', 'NR', 'NU', 'NZ', 'OM', 'PA', 'PE',
                        'PF', 'PG', 'PH', 'PK', 'PL', 'PM', 'PN', 'PR', 'PS', 'PT', 'PW', 'PY', 'QA',
                        'QM', 'QN', 'QO', 'QP', 'QQ', 'QR', 'QS', 'QT', 'QU', 'QV', 'QW', 'QX', 'QY',
                        'QZ', 'RE', 'RO', 'RS', 'RU', 'RW', 'S1', 'SA', 'SB', 'SC', 'SD', 'SE', 'SG',
                        'SH', 'SI', 'SJ', 'SK', 'SL', 'SM', 'SN', 'SO', 'SR', 'SS', 'ST', 'SV', 'SX',
                        'SY', 'SZ', 'TC', 'TD', 'TF', 'TG', 'TH', 'TJ', 'TK', 'TL', 'TM', 'TN', 'TO',
                        'TP', 'TR', 'TT', 'TV', 'TW', 'TZ', 'UA', 'UG', 'UM', 'US', 'UY', 'UZ', 'VA',
                        'VC', 'VE', 'VG', 'VI', 'VN', 'VU', 'WF', 'WS', 'XA', 'XB', 'XD', 'XE', 'XF',
                        'XG', 'XN', 'XP', 'XQ', 'XR', 'XS', 'XT', 'XU', 'XV', 'XW', 'XY', 'XZ', 'YE',
                        'YT', 'YU', 'ZA', 'ZM', 'ZW']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['AA', 'AB', 'AD', 'AE', 'AF', 'AG', 'AI', 'AL', 'AM', 'AN', 'AO',
                  'AQ', 'AR', 'AS', 'AT', 'AU', 'AW', 'AX', 'AZ', 'BA', 'BB', 'BD', 'BE',
                  'BF', 'BG', 'BH', 'BI', 'BJ', 'BL', 'BM', 'BN', 'BO', 'BQ', 'BR', 'BS',
                  'BT', 'BV', 'BW', 'BY', 'BZ', 'C2', 'CA', 'CC', 'CD', 'CF', 'CG', 'CH',
                  'CI', 'CK', 'CL', 'CM', 'CN', 'CO', 'CR', 'CU', 'CV', 'CW', 'CX', 'CY',
                  'CZ', 'DE', 'DJ', 'DK', 'DM', 'DO', 'DZ', 'EC', 'EE', 'EG', 'EH', 'ER',
                  'ES', 'ET', 'FI', 'FJ', 'FK', 'FM', 'FO', 'FR', 'GA', 'GB', 'GD', 'GE',
                  'GF', 'GG', 'GH', 'GI', 'GL', 'GM', 'GN', 'GP', 'GQ', 'GR', 'GS', 'GT',
                  'GU', 'GW', 'GY', 'HK', 'HM', 'HN', 'HR', 'HT', 'HU', 'ID', 'IE', 'IL',
                  'IM', 'IN', 'IO', 'IQ', 'IR', 'IS', 'IT', 'JE', 'JM', 'JO', 'JP', 'K1',
                  'KE', 'KG', 'KH', 'KI', 'KM', 'KN', 'KP', 'KR', 'KW', 'KY', 'KZ', 'LA',
                  'LB', 'LC', 'LI', 'LK', 'LR', 'LS', 'LT', 'LU', 'LV', 'LY', 'MA', 'MC',
                  'MD', 'ME', 'MF', 'MG', 'MH', 'MK', 'ML', 'MM', 'MN', 'MO', 'MP', 'MQ',
                  'MR', 'MS', 'MT', 'MU', 'MV', 'MW', 'MX', 'MY', 'MZ', 'NA', 'NC', 'NE',
                  'NF', 'NG', 'NI', 'NL', 'NO', 'NP', 'NR', 'NU', 'NZ', 'OM', 'PA', 'PE',
                  'PF', 'PG', 'PH', 'PK', 'PL', 'PM', 'PN', 'PR', 'PS', 'PT', 'PW', 'PY',
                  'QA', 'QM', 'QN', 'QO', 'QP', 'QQ', 'QR', 'QS', 'QT', 'QU', 'QV', 'QW',
                  'QX', 'QY', 'QZ', 'RE', 'RO', 'RS', 'RU', 'RW', 'S1', 'SA', 'SB', 'SC',
                  'SD', 'SE', 'SG', 'SH', 'SI', 'SJ', 'SK', 'SL', 'SM', 'SN', 'SO', 'SR',
                  'SS', 'ST', 'SV', 'SX', 'SY', 'SZ', 'TC', 'TD', 'TF', 'TG', 'TH', 'TJ',
                  'TK', 'TL', 'TM', 'TN', 'TO', 'TP', 'TR', 'TT', 'TV', 'TW', 'TZ', 'UA',
                  'UG', 'UM', 'US', 'UY', 'UZ', 'VA', 'VC', 'VE', 'VG', 'VI', 'VN', 'VU',
                  'WF', 'WS', 'XA', 'XB', 'XD', 'XE', 'XF', 'XG', 'XN', 'XP', 'XQ', 'XR',
                  'XS', 'XT', 'XU', 'XV', 'XW', 'XY', 'XZ', 'YE', 'YT', 'YU', 'ZA', 'ZM',
                  'ZW']
    CreditDebitCode:
      type: string
      enum: ['CRDT', 'DBIT']
      x-example: 'CRDT'
    CreditTransferTransaction39:
      type: object
      properties:
        payment_identification:  # PmtId
          $ref: '#/components/schemas/PaymentIdentification7'
        payment_type_information: # PmtTpInf
          $ref: '#/components/schemas/PaymentTypeInformation28'
        interbank_settlement_amount: # IntrBkSttlmAmt
          $ref: '#/components/schemas/ActiveCurrencyAndAmount'
        interbank_settlement_date: # IntrBkSttlmDt
          $ref: '#/components/schemas/ISODate'
        acceptance_datetime: # AccptncDtTm
          $ref: '#/components/schemas/ISODateTime'
        instructed_amount: # InstdAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        exchange_rate: # XchgRate
          $ref: '#/components/schemas/BaseOneRate'
        charge_bearer: # ChrgBr
          $ref: '#/components/schemas/ChargeBearerType1Code'
        previous_instructing_agent_1: # PrvsInstgAgt1
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        ultimate_debtor: # UltmtDbtr
          $ref: '#/components/schemas/PartyIdentification135'
        initiating_party: # InitgPty
          $ref: '#/components/schemas/PartyIdentification135'
        debtor: # Dbtr
          $ref: '#/components/schemas/PartyIdentification135'
        debtor_account: # DbtrAcct
          $ref: '#/components/schemas/CashAccount38'
        debtor_agent: # DbtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor_agent: # CdtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor: # Cdtr
          $ref: '#/components/schemas/PartyIdentification135'
        creditor_account: # CdtrAcct
          $ref: '#/components/schemas/CashAccount38'
        ultimate_creditor: # UltmtCdtr
          $ref: '#/components/schemas/PartyIdentification135'
        purpose: # Purp
          $ref: '#/components/schemas/Purpose2Choice'
        related_remittance_information: # RltdRmtInf
          description: >-
            Elements in this data block can be used to specify details related to
            the handling of the remittance information such as remittance location
            and/or
            the unique identification of the remittance information if it was sent
            separately from the payment instruction.
          type: array
          items:
            $ref: "#/components/schemas/RemittanceLocation7"
          maxItems: 1
        remittance_information: # RmtInf
          $ref: '#/components/schemas/RemittanceInformation16'
      required:
        - payment_identification
        - payment_type_information
        - interbank_settlement_amount
        - interbank_settlement_date
        - charge_bearer
        - debtor
        - debtor_agent
        - creditor_agent
        - creditor
        - creditor_account

    CreditorReferenceInformation2:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/CreditorReferenceType2'
        reference: # Ref
          $ref: '#/components/schemas/Max35Text'
    CreditorReferenceType1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/DocumentType3Code'
        - type: object
          properties:
            proprietary:
              $ref: '#/components/schemas/Max35Text'
      description: >-
        Coded or proprietary format creditor reference type.
    CreditorReferenceType2:
      type: object
      required:
        - code_or_proprietary
      properties:
        code_or_proprietary:  # CdOrPrtry
          $ref: '#/components/schemas/CreditorReferenceType1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
      description: >-
        Reference information provided by the creditor to allow the identification
        of the underlying documents.
    DateAndPlaceOfBirth1:
      type: object
      required:
        - birth_date
        - city_of_birth
        - country_of_birth
      properties:
        birth_date:  # BirthDt
          $ref: '#/components/schemas/ISODate'
        province_of_birth: # PrvcOfBirth
          $ref: '#/components/schemas/Max35Text'
        city_of_birth: # CityOfBirth
          $ref: '#/components/schemas/Max35Text'
        country_of_birth: # CtryOfBirth
          $ref: '#/components/schemas/CountryCode'
    DatePeriod2:
      type: object
      required:
        - from_date
        - to_date
      properties:
        from_date:  # FrDt
          $ref: '#/components/schemas/ISODate'
        to_date: # ToDt
          $ref: '#/components/schemas/ISODate'
    DiscountAmountAndType1:
      type: object
      required:
        - amount
      properties:
        type:  # Tp
          $ref: '#/components/schemas/DiscountAmountType1Choice'
        amount: # Amt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
    DiscountAmountType1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalDiscountAmountType1Code'
        - type: object
          properties:
            proprietary:
              $ref: '#/components/schemas/Max35Text'
    DocumentAdjustment1:
      type: object
      required:
        - amount
      properties:
        amount:  # Amt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        credit_debit_indicator: # CdtDbtInd
          $ref: '#/components/schemas/CreditDebitCode'
        reason: # Rsn
          $ref: '#/components/schemas/Max4Text'
        additional_information: # AddtlInf
          $ref: '#/components/schemas/Max140Text'
    DocumentLineIdentification1:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/DocumentLineType1'
        number: # Nb
          $ref: '#/components/schemas/Max35Text'
        related_date: # RltdDt
          $ref: '#/components/schemas/ISODate'
    DocumentLineInformation1:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          type: array
          items:
            $ref: '#/components/schemas/DocumentLineIdentification1'
          minItems: 1
        description: # Desc
          $ref: '#/components/schemas/Max2048Text'
        amount: # Amt
          $ref: '#/components/schemas/RemittanceAmount3'
    DocumentLineType1:
      type: object
      required:
        - code_or_proprietary
      properties:
        code_or_proprietary:  # CdOrPrtry
          $ref: '#/components/schemas/DocumentLineType1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
    DocumentLineType1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalDocumentLineType1Code'
        - type: object
          properties:
            proprietary:
              $ref: '#/components/schemas/Max35Text'
    DocumentType3Code:
      type: string
      enum: ['RADM', 'RPIN', 'FXDR', 'DISP', 'PUOR', 'SCOR']
      x-example: 'RADM'
    DocumentType6Code:
      type: string
      enum: ['MSIN', 'CNFA', 'DNFA', 'CINV', 'CREN', 'DEBN', 'HIRI', 'SBIN', 'CMCN',
             'SOAC', 'DISP', 'BOLD', 'VCHR', 'AROI', 'TSUT', 'PUOR']
      x-example: 'MSIN'
    Exact4AlphaNumericText:
      type: string
      pattern: '[a-zA-Z0-9]{4}'
    ExternalCategoryPurpose1Code:
      type: string
      description: This includes iso codes which are 4 letter charecters and custom
        which are 3 digit numbers.
      x-example: BONU
      x-enum-examples: ['BONU', 'CASH', 'CBLK', 'CCRD', 'CORT', 'DCRD', 'DIVI', 'DVPM',
                        'EPAY', 'FCOL', 'GOVT', 'HEDG', 'ICCP', 'IDCP', 'INTC', 'INTE', 'LOAN', 'MP2B',
                        'MP2P', 'OTHR', 'PENS', 'RPRE', 'RRCT', 'RVPM', 'SALA', 'SECU', 'SSBE', 'SUPP',
                        'TAXS', 'TRAD', 'TREA', 'VATX', 'WHLD', '240', '260', '330', '370', '400',
                        '430', '460', '480', '452', '308', '311', '318']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['BONU', 'CASH', 'CBLK', 'CCRD', 'CORT', 'DCRD', 'DIVI', 'DVPM',
                  'EPAY', 'FCOL', 'GOVT', 'HEDG', 'ICCP', 'IDCP', 'INTC', 'INTE', 'LOAN',
                  'MP2B', 'MP2P', 'OTHR', 'PENS', 'RPRE', 'RRCT', 'RVPM', 'SALA', 'SECU',
                  'SSBE', 'SUPP', 'TAXS', 'TRAD', 'TREA', 'VATX', 'WHLD', '240', '260', '330',
                  '370', '400', '430', '460', '480', '452', '308', '311', '318']
    ExternalClearingSystemIdentification1Code:
      type: string
      description: >-
        Identification of a clearing system, in a coded form as published in the ISO
        20022 external code list.
        Exclusivity Rule: either the Code or (exclusive or) the Proprietary element
        can be present.
      x-enum-examples: ['ATBLZ', 'AUBSB', 'CACPA', 'CHBCC', 'CHSIC', 'CNAPS', 'DEBLZ',
                        'ESNCC', 'GBDSC', 'GRBIC', 'HKNCC', 'IENCC', 'INFSC', 'ITNCC', 'JPZGN', 'NZNCC',
                        'PLKNR', 'PTNCC', 'RUCBC', 'SESBA', 'SGIBG', 'THCBC', 'TWNCC', 'USABA', 'USPID',
                        'ZANCC']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['ATBLZ', 'AUBSB', 'CACPA', 'CHBCC', 'CHSIC', 'CNAPS', 'DEBLZ', 'ESNCC',
                  'GBDSC', 'GRBIC', 'HKNCC', 'IENCC', 'INFSC', 'ITNCC', 'JPZGN', 'NZNCC',
                  'PLKNR', 'PTNCC', 'RUCBC', 'SESBA', 'SGIBG', 'THCBC', 'TWNCC', 'USABA',
                  'USPID', 'ZANCC']
    ExternalDiscountAmountType1Code:
      type: string
      x-enum-examples: ['APDS', 'STDS', 'TMDS']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['APDS', 'STDS', 'TMDS']
    ExternalDocumentLineType1Code:
      type: string
      x-enum-examples: ['ADPI', 'AISB', 'ASNB', 'CTNB', 'DBSP', 'EANN', 'EINB', 'GSNB',
                        'HIBC', 'ISBN', 'LTNB', 'MDNB', 'PRNB', 'PTCD', 'SKNB', 'STNB', 'TONB', 'UPCC',
                        'UPNB']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['ADPI', 'AISB', 'ASNB', 'CTNB', 'DBSP', 'EANN', 'EINB', 'GSNB',
                  'HIBC', 'ISBN', 'LTNB', 'MDNB', 'PRNB', 'PTCD', 'SKNB', 'STNB', 'TONB',
                  'UPCC', 'UPNB']
    ExternalFinancialInstitutionIdentification1Code:
      type: string
      minLength: 1
      maxLength: 4
      description: >-
        Name of the identification scheme, in a coded form as published in the ISO
        20022 external code list. Exclusivity Rule - either the Code or (exclusive
        or) the Proprietary element can be present
      example: CUST
    ExternalGarnishmentType1Code:
      type: string
      x-enum-examples: ['GNCS', 'GNDP', 'GTPP']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['GNCS', 'GNDP', 'GTPP']
    ExternalOrganisationIdentification1Code:
      type: string
      description: Name of the identification scheme, in a coded form as published
        in the ISO 20022 external code list. Conditional - either the Code or (exclusive
        or) the Proprietary element can be present
      x-code-enum: 2024
      x-example: CUST
      x-enum-examples: ['BANK', 'CBID', 'CHID', 'CINC', 'COID', 'CUST', 'DUNS', 'EMPL',
                        'GS1G', 'SREN', 'SRET', 'TXID']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['BANK', 'CBID', 'CHID', 'CINC', 'COID', 'CUST', 'DUNS', 'EMPL',
                  'GS1G', 'SREN', 'SRET', 'TXID']
    ExternalPaymentTransactionStatus1Code:
      type: string
      enum: ['ACTC', 'PDNG', 'ACSP', 'RJCT', 'CANC', 'RCVD', 'ACPD', 'BLCK']
      description: >-
        This indicates the transaction processing ISO status. Acceptable codes
        ACTC - AcceptedTechnicalValidation
        PDNG - Pending
        ACSP - Accepted Settlement In progress - indicates transaction was processed
        successfully,
        RJCT - indicates transaction processing has been cancelled
        CANC - Cancelled
        RCVD - Received
        ACPD - AcceptedClearingProcessed
        BLCK - Blocked
      x-example: ACSP
    ExternalPersonIdentification1Code:
      type: string
      description: Name of the identification scheme, in a coded form as published
        in the ISO 20022 external code list. Conditional - either the Code or (exclusive
        or) the Proprietary element can be present
      x-example: CUST
      x-enum-examples: ['ARNU', 'CCPT', 'CUST', 'DRLC', 'EMPL', 'NIDN', 'SOSE', 'TXID']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['ARNU', 'CCPT', 'CUST', 'DRLC', 'EMPL', 'NIDN', 'SOSE', 'TXID']
    ExternalProxyAccountType1Code:
      type: string
      enum: ['TELE', 'EMAL', 'DNAM']
      x-example: 'TELE'
    ExternalPurpose1Code:
      type: string
      x-enum-examples: ['BKDF', 'BKFE', 'BKFM', 'BKIP', 'BKPP', 'CBLK', 'CDCB', 'CDCD',
                        'CDCS', 'CDDP', 'CDOC', 'CDQC', 'ETUP', 'FCOL', 'MTUP', 'ACCT', 'CASH', 'COLL',
                        'CSDB', 'DEPT', 'INTC', 'LIMA', 'NETT', 'BFWD', 'CCIR', 'CCPC', 'CCPM', 'CCSM',
                        'CRDS', 'CRPR', 'CRSP', 'CRTL', 'EQPT', 'EQUS', 'EXPT', 'EXTD', 'FIXI', 'FWBC',
                        'FWCC', 'FWSB', 'FWSC', 'MARG', 'MBSB', 'MBSC', 'MGCC', 'MGSC', 'OCCC', 'OPBC',
                        'OPCC', 'OPSB', 'OPSC', 'OPTN', 'OTCD', 'REPO', 'RPBC', 'RPCC', 'RPSB', 'RPSC',
                        'RVPO', 'SBSC', 'SCIE', 'SCIR', 'SCRP', 'SHBC', 'SHCC', 'SHSL', 'SLEB', 'SLOA',
                        'SWBC', 'SWCC', 'SWPT', 'SWSB', 'SWSC', 'TBAS', 'TBBC', 'TBCC', 'TRCP', 'AGRT',
                        'AREN', 'BEXP', 'BOCE', 'COMC', 'CPYR', 'GDDS', 'GDSV', 'GSCB', 'LICF', 'MP2B',
                        'POPE', 'ROYA', 'SCVE', 'SERV', 'SUBS', 'SUPP', 'TRAD', 'CHAR', 'COMT', 'MP2P',
                        'ECPG', 'ECPR', 'ECPU', 'EPAY', 'CLPR', 'COMP', 'DBTC', 'GOVI', 'HLRP', 'HLST',
                        'INPC', 'INPR', 'INSC', 'INSU', 'INTE', 'LBRI', 'LIFI', 'LOAN', 'LOAR', 'PENO',
                        'PPTI', 'RELG', 'RINP', 'TRFD', 'FORW', 'FXNT', 'ADMG', 'ADVA', 'BCDM', 'BCFG',
                        'BLDM', 'BNET', 'CBFF', 'CBFR', 'CCRD', 'CDBL', 'CFEE', 'CGDD', 'CORT', 'COST',
                        'CPKC', 'DCRD', 'DSMT', 'DVPM', 'EDUC', 'FACT', 'FAND', 'FCPM', 'FEES', 'GOVT',
                        'ICCP', 'IDCP', 'IHRP', 'INSM', 'IVPT', 'MCDM', 'MCFG', 'MSVC', 'NOWS', 'OCDM',
                        'OCFG', 'OFEE', 'OTHR', 'PADD', 'PTSP', 'RCKE', 'RCPT', 'REBT', 'REFU', 'RENT',
                        'REOD', 'RIMB', 'RPNT', 'RRBN', 'RRCT', 'RVPM', 'SLPI', 'SPLT', 'STDY', 'TBAN',
                        'TBIL', 'TCSC', 'TELI', 'TMPG', 'TPRI', 'TPRP', 'TRNC', 'TRVC', 'WEBI', 'IPAY',
                        'IPCA', 'IPDO', 'IPEA', 'IPEC', 'IPEW', 'IPPS', 'IPRT', 'IPU2', 'IPUW', 'ANNI',
                        'CAFI', 'CFDI', 'CMDT', 'DERI', 'DIVD', 'FREX', 'HEDG', 'INVS', 'PRME', 'SAVG',
                        'SECU', 'SEPI', 'TREA', 'UNIT', 'FNET', 'FUTR', 'ANTS', 'CVCF', 'DMEQ', 'DNTS',
                        'HLTC', 'HLTI', 'HSPC', 'ICRF', 'LTCF', 'MAFC', 'MARF', 'MDCS', 'VIEW', 'CDEP',
                        'SWFP', 'SWPP', 'SWRS', 'SWUF', 'ADCS', 'AEMP', 'ALLW', 'ALMY', 'BBSC', 'BECH',
                        'BENE', 'BONU', 'CCHD', 'COMM', 'CSLP', 'GFRP', 'GVEA', 'GVEB', 'GVEC', 'GVED',
                        'GWLT', 'HREC', 'PAYR', 'PEFC', 'PENS', 'PRCP', 'RHBS', 'SALA', 'SPSP', 'SSBE',
                        'LBIN', 'LCOL', 'LFEE', 'LMEQ', 'LMFI', 'LMRK', 'LREB', 'LREV', 'LSFL', 'ESTX',
                        'FWLV', 'GSTX', 'HSTX', 'INTX', 'NITX', 'PTXP', 'RDTX', 'TAXS', 'VATX', 'WHLD',
                        'TAXR', 'B112', 'BR12', 'TLRF', 'TLRR', 'AIRB', 'BUSB', 'FERB', 'RLWY', 'TRPT',
                        'CBTV', 'ELEC', 'ENRG', 'GASB', 'NWCH', 'NWCM', 'OTLC', 'PHON', 'UBIL', 'WTER']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['BKDF', 'BKFE', 'BKFM', 'BKIP', 'BKPP', 'CBLK', 'CDCB', 'CDCD',
                  'CDCS', 'CDDP', 'CDOC', 'CDQC', 'ETUP', 'FCOL', 'MTUP', 'ACCT', 'CASH',
                  'COLL', 'CSDB', 'DEPT', 'INTC', 'LIMA', 'NETT', 'BFWD', 'CCIR', 'CCPC',
                  'CCPM', 'CCSM', 'CRDS', 'CRPR', 'CRSP', 'CRTL', 'EQPT', 'EQUS', 'EXPT',
                  'EXTD', 'FIXI', 'FWBC', 'FWCC', 'FWSB', 'FWSC', 'MARG', 'MBSB', 'MBSC',
                  'MGCC', 'MGSC', 'OCCC', 'OPBC', 'OPCC', 'OPSB', 'OPSC', 'OPTN', 'OTCD',
                  'REPO', 'RPBC', 'RPCC', 'RPSB', 'RPSC', 'RVPO', 'SBSC', 'SCIE', 'SCIR',
                  'SCRP', 'SHBC', 'SHCC', 'SHSL', 'SLEB', 'SLOA', 'SWBC', 'SWCC', 'SWPT',
                  'SWSB', 'SWSC', 'TBAS', 'TBBC', 'TBCC', 'TRCP', 'AGRT', 'AREN', 'BEXP',
                  'BOCE', 'COMC', 'CPYR', 'GDDS', 'GDSV', 'GSCB', 'LICF', 'MP2B', 'POPE',
                  'ROYA', 'SCVE', 'SERV', 'SUBS', 'SUPP', 'TRAD', 'CHAR', 'COMT', 'MP2P',
                  'ECPG', 'ECPR', 'ECPU', 'EPAY', 'CLPR', 'COMP', 'DBTC', 'GOVI', 'HLRP',
                  'HLST', 'INPC', 'INPR', 'INSC', 'INSU', 'INTE', 'LBRI', 'LIFI', 'LOAN',
                  'LOAR', 'PENO', 'PPTI', 'RELG', 'RINP', 'TRFD', 'FORW', 'FXNT', 'ADMG',
                  'ADVA', 'BCDM', 'BCFG', 'BLDM', 'BNET', 'CBFF', 'CBFR', 'CCRD', 'CDBL',
                  'CFEE', 'CGDD', 'CORT', 'COST', 'CPKC', 'DCRD', 'DSMT', 'DVPM', 'EDUC',
                  'FACT', 'FAND', 'FCPM', 'FEES', 'GOVT', 'ICCP', 'IDCP', 'IHRP', 'INSM',
                  'IVPT', 'MCDM', 'MCFG', 'MSVC', 'NOWS', 'OCDM', 'OCFG', 'OFEE', 'OTHR',
                  'PADD', 'PTSP', 'RCKE', 'RCPT', 'REBT', 'REFU', 'RENT', 'REOD', 'RIMB',
                  'RPNT', 'RRBN', 'RRCT', 'RVPM', 'SLPI', 'SPLT', 'STDY', 'TBAN', 'TBIL',
                  'TCSC', 'TELI', 'TMPG', 'TPRI', 'TPRP', 'TRNC', 'TRVC', 'WEBI', 'IPAY',
                  'IPCA', 'IPDO', 'IPEA', 'IPEC', 'IPEW', 'IPPS', 'IPRT', 'IPU2', 'IPUW',
                  'ANNI', 'CAFI', 'CFDI', 'CMDT', 'DERI', 'DIVD', 'FREX', 'HEDG', 'INVS',
                  'PRME', 'SAVG', 'SECU', 'SEPI', 'TREA', 'UNIT', 'FNET', 'FUTR', 'ANTS',
                  'CVCF', 'DMEQ', 'DNTS', 'HLTC', 'HLTI', 'HSPC', 'ICRF', 'LTCF', 'MAFC',
                  'MARF', 'MDCS', 'VIEW', 'CDEP', 'SWFP', 'SWPP', 'SWRS', 'SWUF', 'ADCS',
                  'AEMP', 'ALLW', 'ALMY', 'BBSC', 'BECH', 'BENE', 'BONU', 'CCHD', 'COMM',
                  'CSLP', 'GFRP', 'GVEA', 'GVEB', 'GVEC', 'GVED', 'GWLT', 'HREC', 'PAYR',
                  'PEFC', 'PENS', 'PRCP', 'RHBS', 'SALA', 'SPSP', 'SSBE', 'LBIN', 'LCOL',
                  'LFEE', 'LMEQ', 'LMFI', 'LMRK', 'LREB', 'LREV', 'LSFL', 'ESTX', 'FWLV',
                  'GSTX', 'HSTX', 'INTX', 'NITX', 'PTXP', 'RDTX', 'TAXS', 'VATX', 'WHLD',
                  'TAXR', 'B112', 'BR12', 'TLRF', 'TLRR', 'AIRB', 'BUSB', 'FERB', 'RLWY',
                  'TRPT', 'CBTV', 'ELEC', 'ENRG', 'GASB', 'NWCH', 'NWCM', 'OTLC', 'PHON',
                  'UBIL', 'WTER']
    ExternalServiceLevel1Code:
      type: string
      description: >-
        Specifies a pre-agreed service or level of service between the parties, as
        published in the ISO 20022 external service level code list.
        Exclusivity Rule - either the Code or (exclusive or) the Proprietary element
        can be present.
      x-enum-examples: ['BKTR', 'G001', 'G002', 'G003', 'G004', 'NUGP', 'NURG', 'PRPT',
                        'SDVA', 'SEPA', 'SVDE', 'URGP', 'URNS']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['BKTR', 'G001', 'G002', 'G003', 'G004', 'NUGP', 'NURG', 'PRPT',
                  'SDVA', 'SEPA', 'SVDE', 'URGP', 'URNS']
    ExternalStatusReason1Code:
      type: string
      x-enum-examples: ['AB01', 'AB02', 'AB03', 'AB04', 'AB05', 'AB06', 'AB07', 'AB08',
                        'AB09', 'AB10', 'AC01', 'AC02', 'AC03', 'AC04', 'AC05', 'AC06', 'AC07', 'AC08',
                        'AC09', 'AC10', 'AC11', 'AC12', 'AC13', 'AC14', 'AC15', 'AG01', 'AG02', 'AG03',
                        'AG04', 'AG05', 'AG06', 'AG07', 'AG08', 'AG09', 'AG10', 'AG11', 'AGNT', 'AM01',
                        'AM02', 'AM03', 'AM04', 'AM05', 'AM06', 'AM07', 'AM09', 'AM10', 'AM11', 'AM12',
                        'AM13', 'AM14', 'AM15', 'AM16', 'AM17', 'AM18', 'AM19', 'AM20', 'AM21', 'AM22',
                        'AM23', 'BE01', 'BE04', 'BE05', 'BE06', 'BE07', 'BE08', 'BE09', 'BE10', 'BE11',
                        'BE12', 'BE13', 'BE14', 'BE15', 'BE16', 'BE17', 'BE18', 'BE19', 'BE20', 'BE21',
                        'BE22', 'CERI', 'CH03', 'CH04', 'CH07', 'CH09', 'CH10', 'CH11', 'CH12', 'CH13',
                        'CH14', 'CH15', 'CH16', 'CH17', 'CH19', 'CH20', 'CH21', 'CH22', 'CNOR', 'CURR',
                        'CUST', 'DNOR', 'DS01', 'DS02', 'DS03', 'DS04', 'DS05', 'DS06', 'DS07', 'DS08',
                        'DS09', 'DS0A', 'DS0B', 'DS0C', 'DS0D', 'DS0E', 'DS0F', 'DS0G', 'DS0H', 'DS0K',
                        'DS10', 'DS11', 'DS12', 'DS13', 'DS14', 'DS15', 'DS16', 'DS17', 'DS18', 'DS19',
                        'DS20', 'DS21', 'DS22', 'DS23', 'DS24', 'DS25', 'DS26', 'DS27', 'DT01', 'DT02',
                        'DT03', 'DT04', 'DT05', 'DT06', 'DU01', 'DU02', 'DU03', 'DU04', 'DU05', 'DUPL',
                        'ED01', 'ED03', 'ED05', 'ED06', 'ERIN', 'FF01', 'FF02', 'FF03', 'FF04', 'FF05',
                        'FF06', 'FF07', 'FF08', 'FF09', 'FF10', 'FF11', 'G000', 'G001', 'G002', 'G003',
                        'G004', 'G005', 'G006', 'ID01', 'MD01', 'MD02', 'MD05', 'MD06', 'MD07', 'MS02',
                        'MS03', 'NARR', 'NERI', 'RC01', 'RC02', 'RC03', 'RC04', 'RC05', 'RC06', 'RC07',
                        'RC08', 'RC09', 'RC10', 'RC11', 'RC12', 'RCON', 'RF01', 'RR01', 'RR02', 'RR03',
                        'RR04', 'RR05', 'RR06', 'RR07', 'RR08', 'RR09', 'RR10', 'RR11', 'RR12', 'S000',
                        'S001', 'S002', 'S003', 'S004', 'SL01', 'SL02', 'SL11', 'SL12', 'SL13', 'SL14',
                        'TA01', 'TD01', 'TD02', 'TD03', 'TM01', 'TS01', 'TS04']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['AB01', 'AB02', 'AB03', 'AB04', 'AB05', 'AB06', 'AB07', 'AB08',
                  'AB09', 'AB10', 'AC01', 'AC02', 'AC03', 'AC04', 'AC05', 'AC06', 'AC07',
                  'AC08', 'AC09', 'AC10', 'AC11', 'AC12', 'AC13', 'AC14', 'AC15', 'AG01',
                  'AG02', 'AG03', 'AG04', 'AG05', 'AG06', 'AG07', 'AG08', 'AG09', 'AG10',
                  'AG11', 'AGNT', 'AM01', 'AM02', 'AM03', 'AM04', 'AM05', 'AM06', 'AM07',
                  'AM09', 'AM10', 'AM11', 'AM12', 'AM13', 'AM14', 'AM15', 'AM16', 'AM17',
                  'AM18', 'AM19', 'AM20', 'AM21', 'AM22', 'AM23', 'BE01', 'BE04', 'BE05',
                  'BE06', 'BE07', 'BE08', 'BE09', 'BE10', 'BE11', 'BE12', 'BE13', 'BE14',
                  'BE15', 'BE16', 'BE17', 'BE18', 'BE19', 'BE20', 'BE21', 'BE22', 'CERI',
                  'CH03', 'CH04', 'CH07', 'CH09', 'CH10', 'CH11', 'CH12', 'CH13', 'CH14',
                  'CH15', 'CH16', 'CH17', 'CH19', 'CH20', 'CH21', 'CH22', 'CNOR', 'CURR',
                  'CUST', 'DNOR', 'DS01', 'DS02', 'DS03', 'DS04', 'DS05', 'DS06', 'DS07',
                  'DS08', 'DS09', 'DS0A', 'DS0B', 'DS0C', 'DS0D', 'DS0E', 'DS0F', 'DS0G',
                  'DS0H', 'DS0K', 'DS10', 'DS11', 'DS12', 'DS13', 'DS14', 'DS15', 'DS16',
                  'DS17', 'DS18', 'DS19', 'DS20', 'DS21', 'DS22', 'DS23', 'DS24', 'DS25',
                  'DS26', 'DS27', 'DT01', 'DT02', 'DT03', 'DT04', 'DT05', 'DT06', 'DU01',
                  'DU02', 'DU03', 'DU04', 'DU05', 'DUPL', 'ED01', 'ED03', 'ED05', 'ED06',
                  'ERIN', 'FF01', 'FF02', 'FF03', 'FF04', 'FF05', 'FF06', 'FF07', 'FF08',
                  'FF09', 'FF10', 'FF11', 'G000', 'G001', 'G002', 'G003', 'G004', 'G005',
                  'G006', 'ID01', 'MD01', 'MD02', 'MD05', 'MD06', 'MD07', 'MS02', 'MS03',
                  'NARR', 'NERI', 'RC01', 'RC02', 'RC03', 'RC04', 'RC05', 'RC06', 'RC07',
                  'RC08', 'RC09', 'RC10', 'RC11', 'RC12', 'RCON', 'RF01', 'RR01', 'RR02',
                  'RR03', 'RR04', 'RR05', 'RR06', 'RR07', 'RR08', 'RR09', 'RR10', 'RR11',
                  'RR12', 'S000', 'S001', 'S002', 'S003', 'S004', 'SL01', 'SL02', 'SL11',
                  'SL12', 'SL13', 'SL14', 'TA01', 'TD01', 'TD02', 'TD03', 'TM01', 'TS01',
                  'TS04']
    ExternalTaxAmountType1Code:
      type: string
      x-enum-examples: ['CITY', 'CNTY', 'LOCL', 'PROV', 'STAT']
      x-allowed-values:
        fail-on-empty: true
        configurable: true
        allowed: ['CITY', 'CNTY', 'LOCL', 'PROV', 'STAT']
    FIToFICustomerCreditTransfer:
      type: object
      required:
        - group_header
        - credit_transfer_transaction_information
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader93'
        credit_transfer_transaction_information: # CdtTrfTxInf
          type: array
          items:
            $ref: '#/components/schemas/CreditTransferTransaction39'
          minItems: 1
          description: >-
            Elements used to provide information about payments.
          maxItems: 1
    FinancialIdentificationSchemeName1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalFinancialInstitutionIdentification1Code'
        - type: object
          properties:
            proprietary:
              $ref: '#/components/schemas/Max35Text'
      x-mutually-exclusive:
        code: 400
        notNull: 5000
        allowed:
          - 'code'
          - 'proprietary'
    FinancialInstitutionIdentification18:
      type: object
      properties:
        bicfi:  # BICFI
          $ref: '#/components/schemas/BICFIDec2014Identifier'
        clearing_system_member_identification: # ClrSysMmbId
          $ref: '#/components/schemas/ClearingSystemMemberIdentification2'
        lei: # LEI
          $ref: '#/components/schemas/LEIIdentifier'
        name: # Nm
          $ref: '#/components/schemas/Max140Text'
        postal_address: # PstlAdr
          $ref: '#/components/schemas/PostalAddress24'
        other: # Othr
          $ref: '#/components/schemas/GenericFinancialIdentification1'
    FraudCheckResult:
      description: Fraud check results
      type: object
      required:
        - action
      properties:
        score:
          description: Numeric value between 0 and 999 qualifying Fraud Detection
            System assessment of fraud risk.
          type: integer
          minimum: 0
          maximum: 999
          example: 999
        reason:
          description: Fraud Reason from Fraud Detection System
          type: string
          minLength: 1
          maxLength: 256
          example: Good
        action:
          description: Description of action to be performed to mitigate fraud.  (Allow
            Transfer, Block Transfer, Delay Transfer, User Input Ruired, No Check
            Performed)
          type: string
          enum: ['ALLOW', 'BLOCK', 'DELAY', 'USER_INPUT_REQUIRED', 'NO_CHECK_PERFORMED']
          example: ALLOW
    Garnishment3:
      type: object
      required:
        - type
      properties:
        type:  # Tp
          $ref: '#/components/schemas/GarnishmentType1'
        garnishee: # Grnshee
          $ref: '#/components/schemas/PartyIdentification135'
        garnishment_administrator: # GrnshmtAdmstr
          $ref: '#/components/schemas/PartyIdentification135'
        reference_number: # RefNb
          $ref: '#/components/schemas/Max140Text'
        date: # Dt
          $ref: '#/components/schemas/ISODate'
        remitted_amount: # RmtdAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        family_medical_insurance_indicator: # FmlyMdclInsrncInd
          $ref: '#/components/schemas/TrueFalseIndicator'
        employee_termination_indicator: # MplyeeTermntnInd
          $ref: '#/components/schemas/TrueFalseIndicator'
    GarnishmentType1:
      type: object
      required:
        - code_or_proprietary
      properties:
        code_or_proprietary:  # CdOrPrtry
          $ref: '#/components/schemas/GarnishmentType1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
    GarnishmentType1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalGarnishmentType1Code'
        - type: object
          properties:
            proprietary:
              $ref: '#/components/schemas/Max35Text'
    GenericAccountIdentification1:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          description: >-
            Account identifier according to the BANK_ACCT_NO proprietary scheme: This
            element must indicate the actual creditor’s bank account number.
              For payments initiated by an e-transfer Participant and received by
            an e-Transfer Participant, the format of the account is -
              if scheme name is BANK_ACCT_NO then this element must contain the actual
            bank account number. Valid format aaa-bbbbb-cccccccccccccccccccccccc where
                aaa is the Institution Id (fixed length 3 digits)
                bbbbb is the Transit Number (fixed length 5 digits)
                cccccccccccccccccccccccc is the bank account number (up to max 24
            digits)
              For payments initiated by an non e-transfer Participant, this contains
            the actual account number that unambiguously identifies the account for
            the creditor, which may include a 5-character branch number for account
            identification purposes.
              If applicable, this branch number must precede the account number
          type: string
          minLength: 1
          maxLength: 34
          example: 123-12345-12345678901234567890
          x-secure-logging:
            mask: true
            mask-type: account
    GenericFinancialIdentification1:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          $ref: '#/components/schemas/Max35Text'
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/FinancialIdentificationSchemeName1Choice'
        issuer: # Issr
          description: >-
            Entity that assigns the identification.
          type: string
          minLength: 1
          maxLength: 35
    GenericIdentification30:
      type: object
      required:
        - identification
        - issuer
      properties:
        identification:  # Id
          $ref: '#/components/schemas/Exact4AlphaNumericText'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/Max35Text'
      description: >
        Wrapper element for the type of address expressed as a proprietary code.
        Conditional -  either the Code element or (exclusive or) the Proprietary  section
        can be present.
    GenericOrganisationIdentification1:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          description: participant user ID at their financial institution (creditor
            / debtor/ initiating party ) Or Contact Id if sent to sender.
          type: string
          minLength: 1
          maxLength: 35
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/OrganisationIdentificationSchemeName1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
    GenericPersonIdentification1:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          description: Unique and unambiguous identification of a person.
          type: string
          minLength: 1
          maxLength: 35
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/PersonIdentificationSchemeName1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
    GroupHeader93:
      type: object
      properties:
        message_identification:  # MsgId
          description: >-
            The reference number for this request message. It must be unique for every
            request.
            This is the Interac generated initiate (begin) transaction ID to identify
            this initiate direct deposit transaction.
          type: string
          minLength: 1
          maxLength: 35
        creation_datetime: # CreDtTm
          $ref: '#/components/schemas/ISODateTime'
        number_of_transactions: # NbOfTxs
          $ref: '#/components/schemas/Max15NumericText'
        settlement_information: # SttlmInf
          $ref: '#/components/schemas/SettlementInstruction7'
        instructing_agent: # InstgAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        instructed_agent: # InstdAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
      required:
        - message_identification
        - creation_datetime
        - number_of_transactions
    ISODate:
      type: string
      format: date
      description: Requested execution date and time for payment request. A particular
        point in the progression of time in a calendar year expressed in the YYYY-MM-DD
        format. This representation is defined in XML Schema Part 2 Datatypes Second
        Edition - W3C Recommendation 28 October 2004 which is aligned with ISO 8601.
      example: '2020-01-23'
    ISODateTime:
      type: string
      description: >-
        A particular point in the progression of time defined and expressed in either
        UTC time format (YYYY-MM-DDThh:mm:ss.sssZ), local time with UTC offset format
        (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm), or local time format (YYYY-MM- DDThh:mm:ss.sss).
        These representations are defined in XML Schema Part 2: Datatypes Second Edition
        - W3C Recommendation 28 October 2004 which is aligned with ISO 8601.
      format: date-time
      example: '2020-01-23T12:34:56.000Z'
    LEIIdentifier:
      type: string
      pattern: '[A-Z0-9]{18,18}[0-9]{2,2}'
      description: >-
        Legal entity identifier of the financial institution.
        Follows the format described in ISO 17442 "Financial Services - Legal Entity
        Identifier (LEI)".
      example: LEILEILEILEILEILEI99
    LocalInstrument2Choice:
      type: object
      properties:
        proprietary:
          description: |-
            It can have EITHER one of the following Interac proprietary values -
              FULFILL_REQUEST_FOR_PAYMENT      - Fulfill Money Request payment </br>
              ACCOUNT_ALIAS_PAYMENT            - Auto-Deposit payment </br>
              REALTIME_ACCOUNT_ALIAS_PAYMENT   - Real-Time "Auto-Deposit" deposit transaction. Max response time 2s </br>
              ACCOUNT_DEPOSIT_PAYMENT          - Account-Deposit payment </br>
              REALTIME_ACCOUNT_DEPOSIT_PAYMENT - Real-time "Account-Deposit" deposit transaction. Max response time 2s </br>
            OR it can contain a competitive service code id value as defined in the RTR Exchange system
          type: string
          minLength: 1
          maxLength: 35
          # enum: ["FULFILL_REQUEST_FOR_PAYMENT", "ACCOUNT_ALIAS_PAYMENT", "REALTIME_ACCOUNT_ALIAS_PAYMENT","ACCOUNT_DEPOSIT_PAYMENT","REALTIME_ACCOUNT_DEPOSIT_PAYMENT"]
          x-example: FULFILL_REQUEST_FOR_PAYMENT
      required:
        - proprietary
    Max105Text:
      type: string
      minLength: 1
      maxLength: 105
    Max140Text:
      type: string
      minLength: 1
      maxLength: 140
    Max15NumericText:
      type: string
      pattern: '[0-9]{1,15}'
    Max16Text:
      type: string
      minLength: 1
      maxLength: 16
    Max2048Text:
      type: string
      minLength: 1
      maxLength: 2048
    Max35Text:
      type: string
      minLength: 1
      maxLength: 35
    Max4Text:
      type: string
      minLength: 1
      maxLength: 4
    Max70Text:
      type: string
      minLength: 1
      maxLength: 70
    NameAndAddress16:
      type: object
      required:
        - name
        - address
      properties:
        name:  # Nm
          $ref: '#/components/schemas/Max140Text'
        address: # Adr
          $ref: '#/components/schemas/PostalAddress24'
    OrganisationIdentification29:
      type: object
      properties:
        any_bic:  # AnyBIC
          $ref: '#/components/schemas/AnyBICDec2014Identifier'
        lei: # LEI
          $ref: '#/components/schemas/LEIIdentifier'
        other: # Othr
          description: >-
            Wrapper element for the details regarding the agreement under which or
            rules under which the transaction should be processed.
          type: array
          items:
            $ref: '#/components/schemas/GenericOrganisationIdentification1'
          maxItems: 2
    OrganisationIdentificationSchemeName1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalOrganisationIdentification1Code'
        - type: object
          properties:
            proprietary:
              description: >-
                Name of the identification scheme, in a free text form. Conditional
                - either the Code or (exclusive or) the Proprietary element can be present.
              type: string
              minLength: 1
              maxLength: 35
      x-mutually-exclusive:
        code: 400
        allowed:
          - 'code'
          - 'proprietary'

    Party38Choice:
      type: object
      oneOf:
        - type: object
          properties:
            organisation_identification:
              $ref: '#/components/schemas/OrganisationIdentification29'
        - type: object
          properties:
            private_identification:
              $ref: '#/components/schemas/PersonIdentification13'
      x-mutually-exclusive:
        notNull: 400
        code: 5000
        allowed:
          - 'organisation_identification'
          - 'private_identification'

    PartyIdentification135:
      type: object
      properties:
        name:  # Nm
          $ref: '#/components/schemas/Max140Text'
        postal_address: # PstlAdr
          $ref: '#/components/schemas/PostalAddress24'
        identification: # Id
          $ref: '#/components/schemas/Party38Choice'
        country_of_residence: # CtryOfRes
          $ref: '#/components/schemas/CountryCode'
        contact_details: # CtctDtls
          $ref: '#/components/schemas/Contact4'
    PaymentIdentification7:
      type: object
      properties:
        instruction_identification:  # InstrId
          description: >-
            If provided, this element must match the Instruction Identification reference
            for the transaction that is being deposited,
            using one of the values as applicable, instruction_identification from
            pacs.008 send payment, or otherwise the element is empty
          type: string
          minLength: 1
          maxLength: 35
        end_to_end_identification: # EndToEndId
          description: >-
            this element is mandatory. Must match the End To End Identification reference
            for the transaction that is being deposited,
            using one of the values as applicable, end_to_end_identification from
            pacs.008 send payment, or'NOTPROVIDED' for the non-ISO send transfer
          type: string
          minLength: 1
          maxLength: 35
        transaction_identification: # TxId
          description: >-
            Transaction identification set by the Debtor Agent
            Conditional - Present if it was set by the Debtor Agent and the Debtor
            Agent did not provide a UETR for this transaction
            Exclusivity Rule - Either the TransactionIdentification or (exclusive
            or) UETR is present, not both.
          type: string
          minLength: 1
          maxLength: 35
        uetr: # UETR
          $ref: '#/components/schemas/UUIDv4Identifier'
        clearing_system_reference: # ClrSysRef
          description: >-
            This element is mandatory. Must match the Interac generated reference
            for the transaction that is being deposited, using one of the values as
            applicable,
            clearing_system_reference from pacs.002 send payment, or payment_reference
            from the non-ISO send transfer.
          type: string
          minLength: 1
          maxLength: 35
      required:
        - end_to_end_identification
        - clearing_system_reference
      x-mutually-exclusive:
        code: 400
        notNull: 5000
        allowed:
          - 'transaction_identification'
          - 'uetr'

    PaymentTypeInformation28:
      type: object
      properties:
        service_level: # SvcLvl
          description: >-
            Wrapper element for the details regarding the agreement under which or
            rules under which the transaction should be processed.
          type: array
          items:
            $ref: '#/components/schemas/ServiceLevel8Choice'
          maxItems: 3
        local_instrument: # LclInstrm
          $ref: '#/components/schemas/LocalInstrument2Choice'
        category_purpose: # CtgyPurp
          $ref: '#/components/schemas/CategoryPurpose1Choice'
      required:
        - local_instrument
      x-allowed-nested:
        - path:
            - path: 'category_purpose.code'
          other:
            - path: 'category_purpose'
          code: '5000'
    PercentageRate:
      type: number
      #format: decimal  # fractionDigits=10, totalDigits=11
      minimum: 0
      maximum: ***********
      multipleOf: 0.**********
      x-multipleOf: 0.**********
      example: 5.**********
      x-precision:
        decimal: 10
        total-digits: 11
    PersonIdentification13:
      type: object
      properties:
        date_and_place_of_birth:  # DtAndPlcOfBirth
          $ref: '#/components/schemas/DateAndPlaceOfBirth1'
        other: # Othr
          type: array
          items:
            $ref: '#/components/schemas/GenericPersonIdentification1'
      description: >
        Wrapper element. See the PersonIdentification13 structure for the full details
        of this data section.
        Exclusivity Rule - either the OrganisationIdentification or (exclusive or)
        the PrivateIdentification section can be present
    PersonIdentificationSchemeName1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalPersonIdentification1Code'
        - type: object
          properties:
            proprietary:
              description: >-
                Name of the identification scheme, in a free text form. Conditional
                - either the Code or (exclusive or) the Proprietary element can be present.
              type: string
              minLength: 1
              maxLength: 35
      x-mutually-exclusive:
        code: 5000
        notNull: 5000
        allowed:
          - 'code'
          - 'proprietary'
    PhoneNumber:
      type: string
      pattern: \+[0-9]{1,3}-[0-9()+\-]{1,30}
      description: >-
        The collection of information which identifies a specific phone or FAX number
        as defined by telecom services.
        It consists of a "+" followed by the country code (from 1 to 3 characters)
        then a "-" and finally, any combination of numbers, "(", ")", "+" and "-"
        (up to 30 characters).
      minLength: 1
      maxLength: 30
      example: ******-555-1212
      x-secure-logging:
        mask: true
        mask-type: phone_number
    PostalAddress24:
      type: object
      properties:
        address_type:  # AdrTp
          $ref: '#/components/schemas/AddressType3Choice'
        department: # Dept
          $ref: '#/components/schemas/Max70Text'
        sub_department: # SubDept
          $ref: '#/components/schemas/Max70Text'
        street_name: # StrtNm
          type: string
          minLength: 1
          maxLength: 70
          x-secure-logging:
            mask: true
            mask-type: name
        building_number: # BldgNb
          type: string
          minLength: 1
          maxLength: 16
          x-secure-logging:
            mask: true
            mask-type: name
        building_name: # BldgNm
          $ref: '#/components/schemas/Max35Text'
        floor: # Flr
          $ref: '#/components/schemas/Max70Text'
        post_box: # PstBx
          $ref: '#/components/schemas/Max16Text'
        room: # Room
          $ref: '#/components/schemas/Max70Text'
        post_code: # PstCd
          type: string
          minLength: 1
          maxLength: 16
          x-secure-logging:
            mask: true
            mask-type: postal_code
        town_name: # TwnNm
          type: string
          minLength: 1
          maxLength: 35
          x-secure-logging:
            mask: true
            mask-type: name
        town_location_name: # TwnLctnNm
          $ref: '#/components/schemas/Max35Text'
        district_name: # DstrctNm
          $ref: '#/components/schemas/Max35Text'
        country_sub_division: # CtrySubDvsn
          $ref: '#/components/schemas/Max35Text'
        country: # Ctry
          $ref: '#/components/schemas/CountryCode'
        address_line: # AdrLine
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 70
            x-secure-logging:
              mask: true
              mask-type: name
          maxItems: 7
          x-code-notNull: 319
          x-code-invalid: 324

    ProxyAccountIdentification1:
      type: object
      required:
        - identification
      properties:
        type:  # Tp
          $ref: '#/components/schemas/ProxyAccountType1Choice'
        identification: # Id
          description: >-
            Identification used to indicate the account identification under another
            specified name
          type: string
          minLength: 1
          maxLength: 2048
          x-secure-logging:
            mask: true
            mask-type: name
      description: >-
        Section that contains details regarding an alternate assumed name for the
        identification of the account

    ProxyAccountType1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalProxyAccountType1Code'
        - type: object
          properties:
            proprietary:
              description: >-
                Name of the identification scheme, in a free text form. Exclusivity
                Rule: either the Code or (exclusive or) the Proprietary element can
                be present.
              type: string
              minLength: 1
              maxLength: 35
              x-example: EMAIL
      description: >-
        Section that contains details regarding an alternate assumed name for the
        identification of the account
      x-mutually-exclusive:
        code: 400
        notNull: 5000
        allowed:
          - 'code'
          - 'proprietary'

    Purpose2Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalPurpose1Code'
        - type: object
          properties:
            proprietary:
              $ref: '#/components/schemas/Max35Text'

    ## Common Elements ###
    ReferredDocumentInformation7:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/ReferredDocumentType4'
        number: # Nb
          $ref: '#/components/schemas/Max35Text'
        related_date: # RltdDt
          $ref: '#/components/schemas/ISODate'
        line_details: # LineDtls
          type: array
          items:
            $ref: '#/components/schemas/DocumentLineInformation1'
      description: >-
        Provides the identification and the content of the referred document.

    ReferredDocumentType3Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/DocumentType6Code'
        - type: object
          properties:
            proprietary:
              $ref: '#/components/schemas/Max35Text'
      description: >-
        Provides the type details of the referred document.

    ReferredDocumentType4:
      type: object
      required:
        - code_or_proprietary
      properties:
        code_or_proprietary:  # CdOrPrtry
          $ref: '#/components/schemas/ReferredDocumentType3Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
      description: >-
        Specifies the type of referred document.

    RemittanceAmount2:
      type: object
      properties:
        due_payable_amount:  # DuePyblAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        discount_applied_amount: # DscntApldAmt
          type: array
          items:
            $ref: '#/components/schemas/DiscountAmountAndType1'
        credit_note_amount: # CdtNoteAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        tax_amount: # TaxAmt
          type: array
          items:
            $ref: '#/components/schemas/TaxAmountAndType1'
        adjustment_amount_and_reason: # AdjstmntAmtAndRsn
          type: array
          items:
            $ref: '#/components/schemas/DocumentAdjustment1'
        remitted_amount: # RmtdAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
      description: >-
        Provides details on the amounts of the referred document.

    RemittanceAmount3:
      type: object
      properties:
        due_payable_amount:  # DuePyblAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        discount_applied_amount: # DscntApldAmt
          type: array
          items:
            $ref: '#/components/schemas/DiscountAmountAndType1'
        credit_note_amount: # CdtNoteAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        tax_amount: # TaxAmt
          type: array
          items:
            $ref: '#/components/schemas/TaxAmountAndType1'
        adjustment_amount_and_reason: # AdjstmntAmtAndRsn
          type: array
          items:
            $ref: '#/components/schemas/DocumentAdjustment1'
        remitted_amount: # RmtdAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'

    RemittanceInformation16:
      type: object
      properties:
        unstructured:  # Ustrd
          type: array
          items:
            $ref: '#/components/schemas/Max140Text'
          description: Remittance information in an unstructured form. Up to 3 elements
            allowed for a total of 420 characters.
          maxItems: 3
          x-secure-logging:
            mask: true
            mask-type: name
        structured: # Strd
          type: array
          items:
            $ref: '#/components/schemas/StructuredRemittanceInformation16'
          description: Remittance information in a structured form. Up to 5 block
            allowed.
          x-code-size: 2002

    RemittanceLocation7:
      type: object
      properties:
        remittance_identification:  # RmtId
          description: >-
            Unique identification, assigned by the originator, to unambiguously identify
            the remittance information within the message.
          type: string
          minLength: 1
          maxLength: 35
        remittance_location_details: # RmtLctnDtls
          type: array
          items:
            $ref: '#/components/schemas/RemittanceLocationData1'
          description: Set of elements used to provide information on the location
            and/or delivery of the remittance information.
          minItems: 1
          maxItems: 2

    RemittanceLocationData1:
      type: object
      properties:
        method:  # Mtd
          $ref: '#/components/schemas/RemittanceLocationMethod2Code'
        electronic_address: # ElctrncAdr
          $ref: '#/components/schemas/Max2048Text'
        postal_address: # PstlAdr
          $ref: '#/components/schemas/NameAndAddress16'
      required:
        - method
        - electronic_address

    RemittanceLocationMethod2Code:
      type: string
      enum: ['FAXI', 'EDIC', 'URID', 'EMAL', 'POST', 'SMSM']
      description: >-
        Method used to deliver the remittance advice information:
        FAXI - meaning Fax
        EDIC- meaning  Electronic Data Interchange
        URID - meaning Uniform Resource Identifier
        EMAL - meaning e-mail
        POST - meaning POST
        SMSM - meaning SMS
        Important Note - Restriction only value URID is accepted until R1D3 date (RTR
        Release 1 Deployment 3)
      x-example: URID

    ServiceLevel8Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalServiceLevel1Code'
        - type: object
          properties:
            proprietary:
              description: |-
                Specifies a pre-agreed service or level of service between the parties, as a proprietary code. Exclusivity Rule: either the Code or (exclusive or) the Proprietary element can be present.
                Draft rule - If the credit transfer originated from another payment jurisdiction (i.e. outside the borders of Canada), this element must be populated with the 4-letter code INTL.
              type: string
              minLength: 1
              maxLength: 35
      x-mutually-exclusive:
        code: 5000
        notNull: 5000
        allowed:
          - 'code'
          - 'proprietary'

    SettlementInstruction7:
      type: object
      properties:
        settlement_method:  # SttlmMtd
          $ref: '#/components/schemas/SettlementMethod1Code'
        clearing_system: # ClrSys
          $ref: '#/components/schemas/ClearingSystemIdentification3Choice'
      required:
        - settlement_method
        - clearing_system

    SettlementMethod1Code:
      type: string
      enum: ['CLRG']
      x-example: CLRG


    StructuredRemittanceInformation16:
      type: object
      properties:
        referred_document_information:  # RfrdDocInf
          type: array
          items:
            $ref: '#/components/schemas/ReferredDocumentInformation7'
          description: >-
            Provides detailed information on the cancellation status reason.
          maxItems: 1
        referred_document_amount: # RfrdDocAmt
          $ref: '#/components/schemas/RemittanceAmount2'
        creditor_reference_information: # CdtrRefInf
          $ref: '#/components/schemas/CreditorReferenceInformation2'
        invoicer: # Invcr
          $ref: '#/components/schemas/PartyIdentification135'
        invoicee: # Invcee
          $ref: '#/components/schemas/PartyIdentification135'
        tax_remittance: # TaxRmt
          $ref: '#/components/schemas/TaxInformation7'
        garnishment_remittance: # GrnshmtRmt
          $ref: '#/components/schemas/Garnishment3'
        additional_remittance_information: # AddtlRmtInf
          description: >-
            This element is used to provide additional information, in free text form.
            (e.g. invoice description. etc.).
          type: array
          items:
            $ref: '#/components/schemas/Max140Text'
          maxItems: 3
          x-secure-logging:
            mask: true
            mask-type: name

    SupplementaryData:
      type: object
      properties:
        paymentDirection:
          description: >-
            The payment direction (In or Out).
          type: string
          enum: ['IN', 'OUT']
        transactionStatus: # TxSts
          $ref: '#/components/schemas/ExternalPaymentTransactionStatus1Code'
        statusReasonInformation:
          type: object
          description: Provides detailed information on the status reason.
          properties:
            code:
              type: string
              description: Reason for the status.
              minLength: 1
              maxLength: 50
              example: "AC04" # Closed Account Number
            proprietary:
              type: string
              description: Reason for the status, in a proprietary form.
              minLength: 1
              maxLength: 100
              example: "ACCNT_CLOSED" # Account number specified has been closed
        eTransferId:
          $ref: '#/components/schemas/ClearingSystemReference'
        fraudCheckResult:
          $ref: '#/components/schemas/FraudCheckResult'
        fraudSupplementaryInfo:
          $ref: '#/components/schemas/FraudSupplementaryInfo'
        accountHolderName:
          description: Account Holder Name.
          type: string
          minLength: 1
          maxLength: 80
        creditorId:
          description: Creditor ressource ID to retreive informations about recipients if needed.
          type: string
          minLength: 1
          maxLength: 36
          example: uuid-CreditRessourceID
        interacUserId:
          description:  Customer ID for the client in interac side, and Customer must be registered in the e-Transfer system.
          type: string
          minLength: 1
          maxLength: 36
          example: "InteracUserIdMaxLength36"
        interacMoneyRequestId:
          description:  Only for payment of money request Interac Money Request ID.
          type: string
          minLength: 1
          maxLength: 35
          example: "InteracMoneyReqMaxLength35"
        creditorAutoDepositRegNumber:
          description:  Creditor Account alias Registration reference number generated by Interac, for using at payment initiation for the Auto deposit Send. It's mandatory if payment type is 'ACCOUNT_ALIAS_PAYMENT' or 'REALTIME_ACCOUNT_ALIAS_PAYMENT'.
          type: string
          minLength: 1
          maxLength: 35
          example: "CreditorADRegNumMaxLength35"
        paymentAuthentication:
          $ref: '#/components/schemas/PaymentAuthentication'
        mandateRelatedInformation:
          $ref: '#/components/schemas/MandateRelatedInformation'

    MandateRelatedInformation:
      type: object
      required:
        - mandateIdentification
        - frequencyTypePeriod
        - countPerPeriod
        - numberOfRemaining
        - currentOccurrence
      properties:
        mandateIdentification:
          type: string
          description: Unique deferred identifier
          minLength: 1
          maxLength: 36
          example: "Ressource-deferredID"
        frequencyTypePeriod:
          type: string
          description: >-
            Period of the recurring transfer.
            DAIL = Daily - Event takes place every day.
            MNTH = Monthly - Event takes place every month or once a month.
            WEEK = Weekly - Event takes place once a week.
            TWMN = TwiceAMonth - Event takes place two times a month.
            TOWK = EveryTwoWeeks - Event takes place every two weeks.
            ONCE = Once - Event takes place once.
            EOFM = End Of Month - Event takes place on the last day of every months.
            Enum: [ MNTH, WEEK, DAIL, TWMN, TOWK, ONCE, EOFM ]
          enum:
            - MNTH
            - WEEK
            - DAIL
            - TWMN
            - TOWK
            - ONCE
            - EOFM
        countPerPeriod:
          type: integer
          format: int32
          description: >-
            Number of payments total
            Example : 1 for a single deferred and more than 1 for a series of deferred
          minimum: 1
          example: 17
        numberOfRemaining:
          type: integer
          format: int32
          description: >-
            Number of deferred remaining.
          minimum: 0
          example: 7
        currentOccurrence:
          type: integer
          format: int32
          description: >-
            Current payment number in process.
          minimum: 1
          example: 10

    PaymentAuthentication:
      description: >-
        Required only if paymentType is REGULAR_PAYMENT. </br>
        Data block containing information regarding the authentication that will be required to complete this transfer.
      type: object
      required:
        - securityQuestion
        - securityAnswer
        - hashType
        - hashSalt
      properties:
        authenticationType:
          $ref: '#/components/schemas/AuthenticationType'
        securityQuestion:
          description: >-
            Security question text.
          type: string
          minLength: 1
          maxLength: 40
        securityAnswer:
          description: >-
            Security answer encrypted. Answer to the security question (as provided by the customer) with leading and trailing whitespace trimmed, uppercased, postfixed with hashSalt if present,
            hashed using the alghoritm identified by hashType and then Base64 encoded. ISO-8859-1 encoding to be used when the hash is generated.
            This will only be present if the authenticationRequired field (received from the get operation) indicates that validation is required.
          type: string
          minLength: 3
          maxLength: 64
        hashType:
          description: >-
            Alghorithm used to hash the security answer. It has to be one of values supported by the system. Required if authenticationRequired is true.
          type: string
          minLength: 1
          maxLength: 15
        hashSalt:
          description: >-
            Salt used to hash the security answer. Required if authenticationRequired is true. Hash salt to strengthen encryption.
          type: string
          minLength: 1
          maxLength: 44

    AuthenticationType:
      description: >-
        'Flag indicating the type of authentication required to complete the Transfer.
        <br/>
          CONTACT_LEVEL - Security question and answer defined at contact level. <br/>
          PAYMENT_LEVEL - Security question and answer defined at payment level. <br/>
          NOT_REQUIRED - No authentication. </br> '
      type: string
      enum: ['CONTACT_LEVEL', 'PAYMENT_LEVEL', 'NOT_REQUIRED']
      x-example: CONTACT_LEVEL

    TaxAmount2:
      type: object
      properties:
        rate:  # Rate
          $ref: '#/components/schemas/PercentageRate'
        taxable_base_amount: # TaxblBaseAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        total_amount: # TtlAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        details: # Dtls
          type: array
          items:
            $ref: '#/components/schemas/TaxRecordDetails2'

    TaxAmountAndType1:
      type: object
      required:
        - amount
      properties:
        type:  # Tp
          $ref: '#/components/schemas/TaxAmountType1Choice'
        amount: # Amt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'

    TaxAmountType1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalTaxAmountType1Code'
        - type: object
          properties:
            proprietary:
              $ref: '#/components/schemas/Max35Text'

    TaxAuthorisation1:
      type: object
      properties:
        title:  # Titl
          $ref: '#/components/schemas/Max35Text'
        name: # Nm
          $ref: '#/components/schemas/Max140Text'

    TaxInformation7:
      type: object
      properties:
        creditor:  # Cdtr
          $ref: '#/components/schemas/TaxParty1'
        debtor: # Dbtr
          $ref: '#/components/schemas/TaxParty2'
        ultimate_debtor: # UltmtDbtr
          $ref: '#/components/schemas/TaxParty2'
        administration_zone: # AdmstnZone
          $ref: '#/components/schemas/Max35Text'
        reference_number: # RefNb
          $ref: '#/components/schemas/Max140Text'
        method: # Mtd
          $ref: '#/components/schemas/Max35Text'
        total_taxable_base_amount: # TtlTaxblBaseAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        total_tax_amount: # TtlTaxAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        date: # Dt
          $ref: '#/components/schemas/ISODate'
        sequence_number: # SeqNb
          type: number
          minimum: 1
          maximum: ***********9999999
          multipleOf: 1
          x-multipleOf: 1
          example: 5500055
        record: # Rcrd
          type: array
          items:
            $ref: '#/components/schemas/TaxRecord2'
    TaxParty1:
      type: object
      properties:
        tax_identification:  # TaxId
          $ref: '#/components/schemas/Max35Text'
        registration_identification: # RegnId
          $ref: '#/components/schemas/Max35Text'
        tax_type: # TaxTp
          $ref: '#/components/schemas/Max35Text'
    TaxParty2:
      type: object
      properties:
        tax_identification:  # TaxId
          $ref: '#/components/schemas/Max35Text'
        registration_identification: # RegnId
          $ref: '#/components/schemas/Max35Text'
        tax_type: # TaxTp
          $ref: '#/components/schemas/Max35Text'
        authorisation: # Authstn
          $ref: '#/components/schemas/TaxAuthorisation1'
    TaxPeriod2:
      type: object
      properties:
        year:  # Yr
          $ref: '#/components/schemas/ISODate'
        type: # Tp
          $ref: '#/components/schemas/TaxRecordPeriod1Code'
        from_to_date: # FrToDt
          $ref: '#/components/schemas/DatePeriod2'
    TaxRecord2:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/Max35Text'
        category: # Ctgy
          $ref: '#/components/schemas/Max35Text'
        category_details: # CtgyDtls
          $ref: '#/components/schemas/Max35Text'
        debtor_status: # DbtrSts
          $ref: '#/components/schemas/Max35Text'
        certificate_identification: # CertId
          $ref: '#/components/schemas/Max35Text'
        forms_code: # FrmsCd
          $ref: '#/components/schemas/Max35Text'
        period: # Prd
          $ref: '#/components/schemas/TaxPeriod2'
        tax_amount: # TaxAmt
          $ref: '#/components/schemas/TaxAmount2'
        additional_information: # AddtlInf
          $ref: '#/components/schemas/Max140Text'
    TaxRecordDetails2:
      type: object
      required:
        - amount
      properties:
        period:  # Prd
          $ref: '#/components/schemas/TaxPeriod2'
        amount: # Amt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
    TaxRecordPeriod1Code:
      type: string
      enum: ['MM01', 'MM02', 'MM03', 'MM04', 'MM05', 'MM06', 'MM07', 'MM08', 'MM09',
             'MM10', 'MM11', 'MM12', 'QTR1', 'QTR2', 'QTR3', 'QTR4', 'HLF1', 'HLF2']
      x-example: 'MM01'
      properties:
        authorization_type:
          description: >-
            Type of authentication for this direct deposit transaction. The following
            are defined <br/>
            REQUEST_FOR_PAYMENT  -  Participant initiated money request
            ACCOUNT_ALIAS_PAYMENT - Auto-deposit service registration
            Not applicable for account deposits.
          type: string
          enum: ['REQUEST_FOR_PAYMENT', 'ACCOUNT_ALIAS_PAYMENT']
          x-example: REQUEST_FOR_PAYMENT
        participant_authorization_token:
          description: >-
            Authorization token issued by the FI when the direct deposit was set up
            (transfer request or service registration time). Value depends on authorizationType
            <br/>
            REQUEST_FOR_PAYMENT - participantRequestForPaymentReferenceNumber  received
            from Participant and associated with the transfer request  <br/>
            ACCOUNT_ALIAS_PAYMENT - participantAccountAliasReferenceNumber received
            from Participant and associated with the service registration
          type: string
          minLength: 1
          maxLength: 64
        interac_authorization_token:
          description: |-
            Authorization token issued by Interac when the direct deposit was set up (transfer money request, service registration time, transfer request). Value depends on authorizationType
            <br/>
            REQUEST_FOR_PAYMENT - RequestReferenceNumber Acxsys Request Reference Number <br/>
            ACCOUNT_ALIAS_PAYMENT - ServiceId Service Registration ID at Acxsys
          type: string
          minLength: 1
          maxLength: 35
    TrueFalseIndicator:
      type: boolean
    ApprovalRequired:
      type: boolean
    UUIDv4Identifier:
      type: string
      pattern: '[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}'
      description: >-
        UETR the Universally unique identifier reference of the payment transaction
        Conditional -  Present, if it was provided in the credit transfer instruction
        (pacs.008) from the Debtor Agent OR
        if UETR was not provided in the credit transfer instruction (pacs.008) from
        the Debtor Agent AND Interac had to assign a UETR value on behalf of the Debtor
        Agent.
        Exclusivity Rule - Either the TransactionIdentification or (exclusive or)
        UETR is present, not both.
      example: 123e4567-e89b-42d3-a456-************
    FraudSupplementaryInfo:
      type: object
      description: >-
        Additional data block required for Fraud or Compliance reasons.
      properties:
        clientIpAddress:
          description: >-
            Public IP Address used by the Customer during payment initiation.
          type: string
          minLength: 2
          maxLength: 64
          example: *******
        clientDeviceFingerPrint:
          description: >-
            Unique device fingerprint (the session id). The following options/types are supported in this preferred priority. They must start with a prefix (ITM or FTM or UID or CID) followed by the value of the ************* session id/device id/cookie id Interac ThreatMetrix profiling session Id - ITM************* FI ThreatMetrix profiling Session Id - FTM************* Unique Device Identifier of device - UID************* Cookie Id Placed at customers computer or device - CID**************
          type: string
          minLength: 1
          maxLength: 256
          example: ITM1234567890123
        clientAuthenticationMethod:
          description: >-
            Authentication method option used to authenticate the customer (sender) prior to payment initiation. The following values are currently supported.
          type: string
          enum:
            [ PASSWORD, PVQ, FINGERPRINT, BIO_METRICS, OTP, TOKEN, MAKER_CHECKER, NONE, OTHER ]
          example: PASSWORD
