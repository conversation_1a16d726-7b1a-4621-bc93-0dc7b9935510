<component name="ProjectRunConfigurationManager">
    <configuration default="false" name="Money Request OONA" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
        <option name="ACTIVE_PROFILES" value="local-oona"/>
        <envs>
            <env name="SPRING_PROFILE_INCLUDE" value="logging-full,okta-default"/>
        </envs>
        <module name="pmt-incoming-money-request-api-service"/>
        <option name="SPRING_BOOT_MAIN_CLASS" value="ca.bnc.payment.moneyrequest.Application"/>
        <method v="2">
            <option name="Make" enabled="true"/>
        </method>
    </configuration>
</component>