<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pmt-incoming-money-request-api-service</artifactId>
    <name>pmt-incoming-money-request-api (service)</name>
    <packaging>jar</packaging>

    <parent>
        <groupId>ca.bnc.payment</groupId>
        <artifactId>pmt-incoming-money-request-api</artifactId>
        <version>0.1.13-SNAPSHOT</version>
    </parent>

    <properties>
        <test.coverage.inclusions>ca/bnc/payment/**</test.coverage.inclusions>
        <test.coverage.exclusions>
            **/Application*,
            **/generated/**,
            **/config/**,
            **/constant/**,
        </test.coverage.exclusions>

        <sonar.sources>src/main/java</sonar.sources>
        <sonar.tests>src/test/java</sonar.tests>

        <sonar.exclusions>${test.coverage.exclusions}</sonar.exclusions>
    </properties>
    <dependencies>

        <!-- main -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>com.datadoghq</groupId>
            <artifactId>dd-trace-api</artifactId>
            <version>${dd-java-agent.version}</version>
        </dependency>

        <!-- resources -->
        <dependency>
            <groupId>ca.bnc.payment</groupId>
            <artifactId>pmt-incoming-money-request-api-resources</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>ca.bnc.payment</groupId>
            <artifactId>pmt-incoming-money-request-api-interac-money-request-resources</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>ca.bnc.payment</groupId>
            <artifactId>pmt-incoming-money-request-api-pptd-resources</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!-- lib -->
        <dependency>
            <groupId>ca.nbc.payment</groupId>
            <artifactId>pmt-partners-party-contracts</artifactId>
            <version>${pmt-partners-party-contracts.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.cxf</groupId>
                    <artifactId>cxf-codegen-plugin</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ca.bnc.payment.lib</groupId>
            <artifactId>pmt-dynamodb-library</artifactId>
            <version>${pmt-dynamodb-library.version}</version>
        </dependency>
        <dependency>
            <groupId>ca.bnc.payment.lib</groupId>
            <artifactId>pmt-selfhealing-indicators-lib</artifactId>
            <version>${pmt-selfhealing-indicators-lib.version}</version>
        </dependency>
        <dependency>
            <groupId>ca.bnc.payment.lib</groupId>
            <artifactId>pmt-health-logger-lib</artifactId>
            <version>${pmt-health-logger-lib.version}</version>
        </dependency>
        <dependency>
            <groupId>ca.bnc.payment.lib</groupId>
            <artifactId>pmt-logging-library</artifactId>
            <version>${pmt-logging-library.version}</version>
        </dependency>
        <dependency>
            <groupId>ca.nbc.payment</groupId>
            <artifactId>pmt-security-library-v2</artifactId>
            <version>${pmt-security-library-v2.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
        </dependency>
        <dependency>
            <groupId>ca.bnc.payment</groupId>
            <artifactId>pmt_dom_et-pmt-kafka-lib</artifactId>
            <version>0.0.3</version>
        </dependency>
        <dependency>
            <groupId>ca.bnc.payment.lib</groupId>
            <artifactId>pmt-providers-errors-normalization-lib</artifactId>
            <version>${pmt-providers-errors-normalization-lib.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-core</artifactId>
        </dependency>

        <!-- test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.instancio</groupId>
            <artifactId>instancio-junit</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Work around for spring-boot-starter-test forcing scope to test -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <finalName>pmt-incoming-money-request-api</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
                <configuration>
                    <layers>
                        <includeLayerTools>false</includeLayerTools>
                    </layers>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <configuration>
                            <configLocation>checkstyle.xml</configLocation>
                            <failsOnError>true</failsOnError>
                            <consoleOutput>true</consoleOutput>
                            <excludes>${test.coverage.exclusions}</excludes>
                        </configuration>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <configuration>
                    <includes>
                        <include>${test.coverage.inclusions}</include>
                    </includes>
                    <excludes>
                        <exclude>${test.coverage.exclusions}</exclude>
                    </excludes>
                    <rules>
                        <rule>
                            <element>CLASS</element>
                            <limits>
                                <limit>
                                    <counter>BRANCH</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>1</minimum>
                                </limit>
                                <limit>
                                    <counter>LINE</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>1</minimum>
                                </limit>
                            </limits>
                        </rule>
                    </rules>
                </configuration>
                <executions>
                    <execution>
                        <!-- Necessary step to initialize Sonar coverage -->
                        <id>jacoco-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <!-- Save Jacoco coverage report for use in Sonar. Since only package is called, create report in pre-package so it's available for Sonar -->
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <!-- Fail if coverage not satisfactory -->
                        <id>default-check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.pitest</groupId>
                <artifactId>pitest-maven</artifactId>
                <version>${pitest-maven-plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>org.pitest</groupId>
                        <artifactId>pitest-junit5-plugin</artifactId>
                        <version>${pitest-junit5-plugin.version}</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <timestampedReports>false</timestampedReports>
                    <mutationThreshold>100</mutationThreshold>
                    <coverageThreshold>100</coverageThreshold>
                    <excludedClasses>
                        <param>${test.coverage.exclusions}</param>
                        <param>${default-package}.moneyrequest.Application*</param>
                        <param>${default-package}.moneyrequest.config.*</param>
                    </excludedClasses>
                    <outputFormats>
                        <outputFormat>HTML</outputFormat>
                        <outputFormat>XML</outputFormat>
                    </outputFormats>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib-maven-plugin.version}</version>
                <configuration>
                    <skip>false</skip>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                    <extraDirectories>
                        <paths>
                            <path>${agent-extraction-root}</path>
                            <path>src/main/jib</path>
                        </paths>
                        <permissions>
                            <permission>
                                <file>/startScript.sh</file>
                                <mode>755</mode>
                            </permission>
                            <permission>
                                <file>/startScript-bnc.sh</file>
                                <mode>755</mode>
                            </permission>
                            <permission>
                                <file>/startScript-oona.sh</file>
                                <mode>755</mode>
                            </permission>
                        </permissions>
                    </extraDirectories>
                    <container>
                        <entrypoint>
                            <arg>/bin/sh</arg>
                            <arg>/startScript.sh</arg>
                        </entrypoint>
                    </container>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.googlecode.maven-download-plugin</groupId>
                <artifactId>download-maven-plugin</artifactId>
                <version>${download-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>install-datadog-agent</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>wget</goal>
                        </goals>
                        <configuration>
                            <url>${datadog-agent-url}</url>
                            <outputFileName>dd-java-agent.jar</outputFileName>
                            <outputDirectory>${agent-extraction-root}/${datadog-agent-location}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
