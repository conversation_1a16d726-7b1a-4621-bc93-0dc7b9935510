package ca.bnc.payment.moneyrequest;

import ca.nbc.payment.pmt_logging_library.annotations.EnableFeignLogging;
import ca.nbc.payment.pmt_logging_library.annotations.EnableHttpLogging;
import ca.nbc.payment.pmt_security_library.annotations.EnableOktaClient;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(
        scanBasePackages = {"ca.bnc.payment", "ca.nbc.payment"}
)
@EnableAsync
@EnableFeignClients(basePackages = {"ca.nbc.payment.pmt_security_library.okta", "ca.bnc.payment.moneyrequest.client"})
@EnableFeignLogging
@EnableHttpLogging
@EnableOktaClient
@EnableRetry
public class Application {

    public static void main(final String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
