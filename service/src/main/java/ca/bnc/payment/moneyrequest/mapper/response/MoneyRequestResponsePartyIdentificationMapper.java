package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.GenericOrganisationIdentification1;
import ca.bnc.payment.moneyrequest.generated.model.OrganisationIdentification29;
import ca.bnc.payment.moneyrequest.generated.model.OrganisationIdentificationSchemeName1Choice;
import ca.bnc.payment.moneyrequest.generated.model.Party38Choice;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class MoneyRequestResponsePartyIdentificationMapper {

    public Party38Choice map(final ca.bnc.payment.interac_money_request.generated.model.Party38Choice party) {
        return new Party38Choice()
                .organisationIdentification(mapOrganisationIdentification(party.getOrganisationIdentification()));
    }

    private OrganisationIdentification29 mapOrganisationIdentification(
            final ca.bnc.payment.interac_money_request.generated.model.OrganisationIdentification29 organisationIdentification
    ) {
        return new OrganisationIdentification29()
                .other(
                        Optional.ofNullable(organisationIdentification.getOther())
                                .filter(list -> !list.isEmpty())
                                .map(list -> list.get(0))
                                .map(this::mapGenericOrganisationIdentification)
                                .orElseGet(GenericOrganisationIdentification1::new)
                );
    }

    private GenericOrganisationIdentification1 mapGenericOrganisationIdentification(
            final ca.bnc.payment.interac_money_request.generated.model.GenericOrganisationIdentification1 genericOrganisationIdentification
    ) {
        return new GenericOrganisationIdentification1()
                .identification(genericOrganisationIdentification.getIdentification())
                .schemeName(
                        Optional.ofNullable(genericOrganisationIdentification.getSchemeName())
                                .map(schemeName ->
                                        new OrganisationIdentificationSchemeName1Choice().code(
                                                OrganisationIdentificationSchemeName1Choice.CodeEnum.fromValue(
                                                        schemeName.getCode().getValue()
                                                )
                                        )
                                )
                                .orElse(null)
                );
    }

}
