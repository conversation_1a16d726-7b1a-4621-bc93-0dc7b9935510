package ca.bnc.payment.moneyrequest.util;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
public class ParticipantIdUtil {

    private static final String BNC_DIRECT_PARTICIPANT_ID = "CA000006";
    private static final String OS_DIRECT_PARTICIPANT_ID = "CA000612";

    private final String directParticipantIdentifier;
    private final String indirectConnectorIdentifier;

    public ParticipantIdUtil(@Value("${application.directParticipantIdentifier}") final String directParticipantIdentifier,
                             @Value("${application.indirectConnectorId}") final String indirectConnectorIdentifier) {
        this.directParticipantIdentifier = directParticipantIdentifier;
        this.indirectConnectorIdentifier = indirectConnectorIdentifier;
    }

    public boolean isBncParticipant() {
        return BNC_DIRECT_PARTICIPANT_ID.equals(directParticipantIdentifier);
    }

    public boolean isOsParticipant() {
        return OS_DIRECT_PARTICIPANT_ID.equals(directParticipantIdentifier);
    }

}
