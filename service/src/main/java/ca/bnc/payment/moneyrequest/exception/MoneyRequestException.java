package ca.bnc.payment.moneyrequest.exception;

import ca.bnc.payment.moneyrequest.generated.model.Error;
import lombok.Getter;

import java.util.Optional;

import static ca.bnc.payment.moneyrequest.constant.Constant.NO_REASON_PROVIDED;


@Getter
public sealed class MoneyRequestException extends RuntimeException permits RetryableMoneyRequestException {

    private final ErrorInfo errorInfo;

    protected MoneyRequestException(final ErrorInfo errorInfo) {
        super(Optional.ofNullable(errorInfo.error())
                .map(Error::getText)
                .orElse(NO_REASON_PROVIDED));
        this.errorInfo = errorInfo;
    }

    public static MoneyRequestException badRequest(final Error error) {
        return new MoneyRequestException(ErrorInfo.badRequest(error));
    }

    public static MoneyRequestException notFound(final Error error) {
        return new MoneyRequestException(ErrorInfo.notFound(error));
    }

    public static MoneyRequestException internalServerError(final Error error) {
        return new MoneyRequestException(ErrorInfo.internalServerError(error));
    }

    public static MoneyRequestException badRequest(final String code,
                                                   final String text,
                                                   final String origin,
                                                   final String rule) {
        return badRequest(new Error().code(code).text(text).origin(origin).rule(rule));
    }

    public static MoneyRequestException internalServerError(final String code,
                                                            final String text,
                                                            final String origin,
                                                            final String rule) {
        return internalServerError(new Error().code(code).text(text).origin(origin).rule(rule));
    }
}
