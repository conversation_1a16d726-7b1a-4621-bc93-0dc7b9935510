package ca.bnc.payment.moneyrequest.controller;


import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.generated.model.DomesticFulfillmentMoneyRequest;
import ca.bnc.payment.moneyrequest.generated.rest.EtPmtProcApi;
import ca.bnc.payment.moneyrequest.service.MoneyRequestService;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequiredArgsConstructor
@Validated
public class MoneyRequestController implements EtPmtProcApi {

    private final MoneyRequestService moneyRequestService;
    private final LogContextHolder logContextHolder;
    private final LogContextHelper logContextHelper;

    @Override
    public ResponseEntity<DomesticFulfillmentMoneyRequest> getIncomingMoneyRequest(final String interacMoneyRequestId,
                                                                                   final String channelId,
                                                                                   final ChannelType channelType,
                                                                                   final UUID requestId,
                                                                                   final String clientId,
                                                                                   final String acceptVersion,
                                                                                   final String traceparent,
                                                                                   final String tracestate,
                                                                                   final UUID bncBusinessTraceId,
                                                                                   final String clientAgentId,
                                                                                   final String agentId) {
        return logContextHolder.runWithContextNoReset(
                () -> ResponseEntity
                        .status(HttpStatus.OK)
                        .body(
                                moneyRequestService.processGetIncomingMoneyRequest(
                                        channelId,
                                        channelType,
                                        requestId,
                                        clientId,
                                        interacMoneyRequestId
                                )
                        ),
                logContextHelper.contextFor(requestId, clientId, bncBusinessTraceId, interacMoneyRequestId)
        );
    }

    @Override
    public ResponseEntity<Void> decline(final String interacMoneyRequestId,
                                        final String channelId,
                                        final ChannelType channelType,
                                        final String clientId,
                                        final String acceptVersion,
                                        final UUID requestId,
                                        final String traceparent,
                                        final String tracestate,
                                        final UUID bncBusinessTraceId,
                                        final String clientAgentId,
                                        final String agentId,
                                        final DeclineRequest declineRequest) {

        return logContextHolder.runWithContextNoReset(
                () -> {
                    moneyRequestService.processDeclineIncomingMoneyRequest(
                            channelId,
                            channelType,
                            requestId,
                            clientId,
                            interacMoneyRequestId,
                            declineRequest
                    );
                    return ResponseEntity.status(HttpStatus.NO_CONTENT).body(null);
                },
                logContextHelper.contextFor(requestId, clientId, bncBusinessTraceId, interacMoneyRequestId)
        );
    }
}
