package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.StructuredRemittanceInformation16;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class MoneyRequestResponseRemittanceInformationStructuredMapper {

    private final MoneyRequestResponseReferredDocumentInformationMapper moneyRequestResponseReferredDocumentInformationMapper;
    private final MoneyRequestResponseReferredDocumentAmountMapper moneyRequestResponseReferredDocumentAmountMapper;
    private final MoneyRequestResponseCreditorReferenceInformationMapper moneyRequestResponseCreditorReferenceInformationMapper;
    private final MoneyRequestResponseInvoicerMapper moneyRequestResponseInvoicerMapper;
    private final MoneyRequestResponseInvoiceeMapper moneyRequestResponseInvoiceeMapper;

    public StructuredRemittanceInformation16 map(
            final ca.bnc.payment.interac_money_request.generated.model.StructuredRemittanceInformation16 structuredRemittanceInformation
    ) {
        final List<ca.bnc.payment.interac_money_request.generated.model.ReferredDocumentInformation7> referredDocumentInformation
                = structuredRemittanceInformation.getReferredDocumentInformation();

        return new StructuredRemittanceInformation16()
                .referredDocumentInformation(
                        Optional.ofNullable(referredDocumentInformation)
                                .filter(list -> !list.isEmpty())
                                .map(list ->
                                        moneyRequestResponseReferredDocumentInformationMapper.map(list.get(0)))
                                .orElse(null)
                )
                .referredDocumentAmount(
                        Optional.ofNullable(structuredRemittanceInformation.getReferredDocumentAmount())
                                .map(moneyRequestResponseReferredDocumentAmountMapper::map)
                                .orElse(null)
                )
                .creditorReferenceInformation(
                        Optional.ofNullable(structuredRemittanceInformation.getCreditorReferenceInformation())
                                .map(moneyRequestResponseCreditorReferenceInformationMapper::map)
                                .orElse(null)
                )
                .invoicer(
                        Optional.ofNullable(structuredRemittanceInformation.getInvoicer())
                                .map(moneyRequestResponseInvoicerMapper::map)
                                .orElse(null)
                )
                .invoicee(
                        Optional.ofNullable(structuredRemittanceInformation.getInvoicee())
                                .map(moneyRequestResponseInvoiceeMapper::map)
                                .orElse(null)
                )
                .additionalRemittanceInformation(
                        Optional.ofNullable(structuredRemittanceInformation.getAdditionalRemittanceInformation())
                                .filter(list -> !list.isEmpty())
                                .map(list -> list.get(0))
                                .orElse(null)
                );
    }

}
