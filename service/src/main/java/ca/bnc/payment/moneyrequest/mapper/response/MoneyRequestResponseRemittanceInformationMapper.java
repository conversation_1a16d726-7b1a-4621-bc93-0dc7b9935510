package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.RemittanceInformation16;
import ca.bnc.payment.moneyrequest.generated.model.RemittanceInformation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class MoneyRequestResponseRemittanceInformationMapper {

    private final MoneyRequestResponseRemittanceInformationStructuredMapper moneyRequestResponseRemittanceInformationStructuredMapper;

    public RemittanceInformation map(final RemittanceInformation16 remittanceInformation) {
        return new RemittanceInformation()
                .unstructured(remittanceInformation.getUnstructured())
                .structured(Optional.ofNullable(remittanceInformation.getStructured())
                        .map(list -> list
                                        .stream()
                                        .map(moneyRequestResponseRemittanceInformationStructuredMapper::map)
                                        .toList()
                        )
                        .orElse(null));
    }
}
