package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEventRawData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;

@RequiredArgsConstructor
@Component
public class PptdRawDataMapper {

    private final PptdMessageDataMapper pptdMessageDataMapper;
    private final PptdSupplementaryDataMapper pptdSupplementaryDataMapper;
    private final TimeService timeService;

    private static final Boolean APPROVAL_REQUIRED = Boolean.FALSE;
    private static final String MESSAGE_DEFINITION_IDENTIFIER = "pacs.008.001.08";

    public DomesticETransferEventRawData map(
            final MoneyRequestContext moneyRequestContext,
            final String instructionIdentification,
            final String endToEndBusinessIdentification,
            final DeclineRequest declineRequest,
            final IncomingMoneyRequestEntity incomingMoneyRequestEntity
    ) {
        return new DomesticETransferEventRawData()
                .instructionIdentification(instructionIdentification)
                .endToEndBusinessIdentification(endToEndBusinessIdentification)
                .approvalRequired(APPROVAL_REQUIRED)
                .messageDefinitionIdentifier(MESSAGE_DEFINITION_IDENTIFIER)
                .clientId(moneyRequestContext.clientId())
                .creationDatetime(parseCreationDatetime(incomingMoneyRequestEntity.getSysCreatedDate()))
                .messageData(pptdMessageDataMapper.map(declineRequest, incomingMoneyRequestEntity))
                .supplementaryData(pptdSupplementaryDataMapper.map(moneyRequestContext));
    }

    private OffsetDateTime parseCreationDatetime(final String sysCreatedDate) {
        try {
            return timeService.parseToOffsetDateTime(sysCreatedDate);
        } catch (DateTimeParseException dateTimeParseException) {
            throw new IllegalArgumentException("Error building Kafka message for PPTD: invalid creation date time '%s'"
                    .formatted(sysCreatedDate));
        }
    }

}
