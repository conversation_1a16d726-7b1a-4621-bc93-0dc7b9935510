package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135;
import ca.bnc.payment.moneyrequest.generated.model.Debtor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class MoneyRequestResponseDebtorMapper {

    private static final String DEBTOR_NAME_NOT_PROVIDED = "NOTPROVIDED";

    public Debtor map(final PartyIdentification135 partyIdentification) {
        return Optional.ofNullable(partyIdentification.getName())
                .map(debtorName -> new Debtor().name(debtorName))
                .orElseGet(() -> new Debtor().name(DEBTOR_NAME_NOT_PROVIDED));
    }
}
