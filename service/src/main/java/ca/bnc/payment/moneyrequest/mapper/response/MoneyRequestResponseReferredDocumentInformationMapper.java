package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.ReferredDocumentInformation7;
import ca.bnc.payment.moneyrequest.generated.model.ReferredDocumentType3Choice;
import ca.bnc.payment.moneyrequest.generated.model.ReferredDocumentType4;
import org.springframework.stereotype.Component;

@Component
public class MoneyRequestResponseReferredDocumentInformationMapper {

    public ReferredDocumentInformation7 map(
            final ca.bnc.payment.interac_money_request.generated.model.ReferredDocumentInformation7 referredDocumentInformation
    ) {
        return new ReferredDocumentInformation7()
                .number(referredDocumentInformation.getNumber())
                .type(
                        new ReferredDocumentType4().codeOrProprietary(
                                new ReferredDocumentType3Choice().code(
                                        mapType(referredDocumentInformation.getType())
                                )
                        )
                );
    }

    private ReferredDocumentType3Choice.CodeEnum mapType(
            final ca.bnc.payment.interac_money_request.generated.model.ReferredDocumentType4 referredDocumentType
    ) {
        if (referredDocumentType == null) {
            return null;
        }
        return ReferredDocumentType3Choice.CodeEnum.fromValue(referredDocumentType.getCodeOrProprietary().getCode().getValue());
    }
}
