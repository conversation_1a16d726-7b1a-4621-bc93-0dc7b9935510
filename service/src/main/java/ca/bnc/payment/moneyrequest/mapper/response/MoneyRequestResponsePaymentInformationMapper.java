package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.interac_money_request.generated.model.PaymentInstruction31;
import ca.bnc.payment.moneyrequest.generated.model.PaymentInformation;
import ca.bnc.payment.moneyrequest.mapper.common.MoneyRequestStatusMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MoneyRequestResponsePaymentInformationMapper {

    private final MoneyRequestResponseCategoryPurposeMapper moneyRequestResponseCategoryPurposeMapper;
    private final MoneyRequestStatusMapper moneyRequestStatusMapper;
    private final MoneyRequestResponsePaymentConditionMapper moneyRequestResponsePaymentConditionMapper;
    private final MoneyRequestResponseDebtorMapper moneyRequestResponseDebtorMapper;
    private final MoneyRequestResponseCreditTransferTransactionMapper moneyRequestResponseCreditTransferTransactionMapper;

    public PaymentInformation map(final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse) {
        final PaymentInstruction31 paymentInstruction =
                incomingRequestForPaymentResponse.getCreditorPaymentActivationRequest().getPaymentInformation().get(0);
        return new PaymentInformation()
                .categoryPurpose(moneyRequestResponseCategoryPurposeMapper.map(paymentInstruction))
                .expiryDate(paymentInstruction.getExpiryDate())
                .moneyRequestStatus(moneyRequestStatusMapper.map(incomingRequestForPaymentResponse.getRequestForPaymentStatus()))
                .paymentCondition(moneyRequestResponsePaymentConditionMapper.map(paymentInstruction.getPaymentCondition()))
                .debtor(moneyRequestResponseDebtorMapper.map(paymentInstruction.getDebtor()))
                .creditTransferTransaction(moneyRequestResponseCreditTransferTransactionMapper
                        .map(paymentInstruction.getCreditTransferTransaction().get(0)));
    }

}
