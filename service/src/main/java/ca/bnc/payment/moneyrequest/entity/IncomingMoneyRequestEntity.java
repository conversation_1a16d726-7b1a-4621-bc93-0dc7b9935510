package ca.bnc.payment.moneyrequest.entity;

import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

import java.math.BigDecimal;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@DynamoDbBean
public class IncomingMoneyRequestEntity {

    @Getter(onMethod_ = {@DynamoDbPartitionKey, @DynamoDbAttribute("interac_money_request_id")})
    @Setter(onMethod_ = {@JsonSetter("interac_money_request_id")})
    private String interacMoneyRequestId;

    @Getter(onMethod_ = {@DynamoDbAttribute("end_to_end_identification")})
    @Setter(onMethod_ = {@JsonSetter("end_to_end_identification")})
    private String endToEndIdentification;

    @Getter(onMethod_ = {@DynamoDbAttribute("debtor_name")})
    @Setter(onMethod_ = {@JsonSetter("debtor_name")})
    private String debtorName;

    @Getter(onMethod_ = {@DynamoDbAttribute("debtor_client_id")})
    @Setter(onMethod_ = {@JsonSetter("debtor_client_id")})
    private String debtorClientId;

    @Getter(onMethod_ = {@DynamoDbAttribute("debtor_interac_user_id")})
    @Setter(onMethod_ = {@JsonSetter("debtor_interac_user_id")})
    private String debtorInteracUserId;

    @Getter(onMethod_ = {@DynamoDbAttribute("amount")})
    @Setter(onMethod_ = {@JsonSetter("amount")})
    private BigDecimal amount;

    @Getter(onMethod_ = {@DynamoDbAttribute("currency")})
    @Setter(onMethod_ = {@JsonSetter("currency")})
    private String currency;

    @Getter(onMethod_ = {@DynamoDbAttribute("creditor_institution_id")})
    @Setter(onMethod_ = {@JsonSetter("creditor_institution_id")})
    private String creditorInstitutionId;

    @Getter(onMethod_ = {@DynamoDbAttribute("creditor_name")})
    @Setter(onMethod_ = {@JsonSetter("creditor_name")})
    private String creditorName;

    @Getter(onMethod_ = {@DynamoDbAttribute("creditor_mobile_no")})
    @Setter(onMethod_ = {@JsonSetter("creditor_mobile_no")})
    private String creditorMobileNumber;

    @Getter(onMethod_ = {@DynamoDbAttribute("creditor_email_address")})
    @Setter(onMethod_ = {@JsonSetter("creditor_email_address")})
    private String creditorEmailAddress;

    @Getter(onMethod_ = {@DynamoDbAttribute("remittance_information")})
    @Setter(onMethod_ = {@JsonSetter("remittance_information")})
    private String remittanceInformation;

    @Getter(onMethod_ = {@DynamoDbAttribute("invoice_number")})
    @Setter(onMethod_ = {@JsonSetter("invoice_number")})
    private String invoiceNumber;

    @Getter(onMethod_ = {@DynamoDbAttribute("invoice_issue_date")})
    @Setter(onMethod_ = {@JsonSetter("invoice_issue_date")})
    private String invoiceIssueDate;

    @Getter(onMethod_ = {@DynamoDbAttribute("money_request_status")})
    @Setter(onMethod_ = {@JsonSetter("money_request_status")})
    private String moneyRequestStatus;

    @Getter(onMethod_ = {@DynamoDbAttribute("expiry_date")})
    @Setter(onMethod_ = {@JsonSetter("expiry_date")})
    private String expiryDate;

    @Getter(onMethod_ = {@DynamoDbAttribute("sys_created_date")})
    @Setter(onMethod_ = {@JsonSetter("sys_created_date")})
    private String sysCreatedDate;

    @Getter(onMethod_ = {@DynamoDbAttribute("sys_modified_date")})
    @Setter(onMethod_ = {@JsonSetter("sys_modified_date")})
    private String sysModifiedDate;

    @Getter(onMethod_ = {@DynamoDbAttribute("sys_expiration_date")})
    @Setter(onMethod_ = {@JsonSetter("sys_expiration_date")})
    private Long sysExpirationDate;

    @Getter(onMethod_ = {@DynamoDbAttribute("fraud_check_result_action")})
    @Setter(onMethod_ = {@JsonSetter("fraud_check_result_action")})
    private String fraudCheckResultAction;

    @Getter(onMethod_ = {@DynamoDbAttribute("fraud_check_result_score")})
    @Setter(onMethod_ = {@JsonSetter("fraud_check_result_score")})
    private String fraudCheckResultScore;

    @Getter(onMethod_ = {@DynamoDbAttribute("fraud_check_result_reason")})
    @Setter(onMethod_ = {@JsonSetter("fraud_check_result_reason")})
    private String fraudCheckResultReason;

    @Getter(onMethod_ = {@DynamoDbAttribute("channel_type")})
    @Setter(onMethod_ = {@JsonSetter("channel_type")})
    private String channelType;

    @Getter(onMethod_ = {@DynamoDbAttribute("channel_id")})
    @Setter(onMethod_ = {@JsonSetter("channel_id")})
    private String channelId;

    @Getter(onMethod_ = {@DynamoDbAttribute("decline_reason")})
    @Setter(onMethod_ = {@JsonSetter("decline_reason")})
    private String declineReason;
}
