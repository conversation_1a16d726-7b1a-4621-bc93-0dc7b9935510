package ca.bnc.payment.moneyrequest.config;

import ca.bnc.payment.normalization.lib.service.NormalizationService;
import ca.bnc.payment.normalization.lib.service.impl.NormalizationServiceImpl;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.datatype.jsr310.ser.OffsetDateTimeSerializer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Clock;
import java.time.format.DateTimeFormatter;

@Configuration
public class AppConfig {
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");


    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jsonCustomizer() {
        return builder -> builder.serializers(new OffsetDateTimeSerializer(
                OffsetDateTimeSerializer.INSTANCE,
                false,
                FORMATTER,
                JsonFormat.Shape.STRING
        ));
    }

    @Bean
    public Clock clock() {
        return Clock.systemUTC();
    }

    @Bean
    public NormalizationService normalizationService(){
        return new NormalizationServiceImpl();
    }

}
