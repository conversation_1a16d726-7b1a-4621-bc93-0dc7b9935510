package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEventRawDataMessageData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PptdMessageDataMapper {

    private final PptdFIToFICustomerCreditTransferMapper pptdFIToFICustomerCreditTransferMapper;

    public DomesticETransferEventRawDataMessageData map(
            final DeclineRequest declineRequest,
            final IncomingMoneyRequestEntity incomingMoneyRequestEntity
            ) {
        return new DomesticETransferEventRawDataMessageData()
                .fiToFiCustomerCreditTransfer(pptdFIToFICustomerCreditTransferMapper.map(declineRequest, incomingMoneyRequestEntity));
    }
}
