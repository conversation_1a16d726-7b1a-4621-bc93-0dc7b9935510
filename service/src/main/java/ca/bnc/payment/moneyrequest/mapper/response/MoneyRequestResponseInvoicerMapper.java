package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.PartyIdentification135;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class MoneyRequestResponseInvoicerMapper {

    private final MoneyRequestResponsePostalAddressMapper moneyRequestResponsePostalAddressMapper;
    private final MoneyRequestResponsePartyIdentificationMapper moneyRequestResponsePartyIdentificationMapper;
    private final MoneyRequestResponseContactDetailsMapper moneyRequestResponseContactDetailsMapper;

    public PartyIdentification135 map(
            final ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135 invoicer
    ) {
        return new PartyIdentification135()
                .name(invoicer.getName())
                .postalAddress(
                        Optional.ofNullable(invoicer.getPostalAddress())
                                .map(moneyRequestResponsePostalAddressMapper::map)
                                .orElse(null)
                )
                .identification(
                        Optional.ofNullable(invoicer.getIdentification())
                                .map(moneyRequestResponsePartyIdentificationMapper::map)
                                .orElse(null)
                )
                .contactDetails(
                        Optional.ofNullable(invoicer.getContactDetails())
                                .map(moneyRequestResponseContactDetailsMapper::map)
                                .orElse(null)
                );
    }

}
