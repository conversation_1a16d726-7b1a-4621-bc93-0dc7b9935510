package ca.bnc.payment.moneyrequest.repository;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.core.exception.ApiCallTimeoutException;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.http.HttpStatusCode;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;

import static ca.bnc.payment.moneyrequest.constant.Constant.DYNAMODB_BAD_REQUEST;
import static ca.bnc.payment.moneyrequest.constant.Constant.DYNAMODB_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.DYNAMODB_UNEXPECTED_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.DYNAMODB_UNREACHABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;

@Repository
@Slf4j
public class IncomingMoneyRequestRepository {

    private final DynamoDbTable<IncomingMoneyRequestEntity> incomingMoneyRequestTable;

    public IncomingMoneyRequestRepository(final DynamoDbTable<IncomingMoneyRequestEntity> incomingMoneyRequestTable) {
        this.incomingMoneyRequestTable = incomingMoneyRequestTable;
    }

    public IncomingMoneyRequestEntity save(final IncomingMoneyRequestEntity userEntity) {
        try {
            incomingMoneyRequestTable.putItem(userEntity);
        } catch (final ApiCallTimeoutException exception) {
            throw createDynamoDBException(DYNAMODB_UNREACHABLE);
        } catch (final DynamoDbException exception) {
            throw handleDynamoDbException(exception);
        } catch (final Exception exception) {
            throw createDynamoDBException(DYNAMODB_UNEXPECTED_ERROR);
        }
        return userEntity;
    }

    public IncomingMoneyRequestEntity retrieve(final String interacMoneyRequestId) {
        try {
            return incomingMoneyRequestTable.getItem(buildKey(interacMoneyRequestId));
        } catch (final ApiCallTimeoutException exception) {
            log.error("ApiCallTimeoutException Error while calling dynamo DB to retrieve incoming money request", exception);
            throw createDynamoDBException(DYNAMODB_UNREACHABLE);
        } catch (final DynamoDbException exception) {
            log.error("DynamoDbException Error while calling dynamo DB to retrieve incoming money request", exception);
            throw handleDynamoDbException(exception);
        } catch (final Exception exception) {
            log.error("Global Exception Error while calling dynamo DB to retrieve incoming money request", exception);
            throw createDynamoDBException(DYNAMODB_UNEXPECTED_ERROR);
        }
    }

    private Key buildKey(final String tableKey) {
        return Key.builder()
                .partitionValue(tableKey)
                .build();
    }

    private MoneyRequestException createDynamoDBException(final String errorMessage) {
        return  MoneyRequestException.internalServerError(TECHNICAL_ERROR_CODE, errorMessage, SERVICE_ORIGIN, NA);
    }

    private RuntimeException handleDynamoDbException(final DynamoDbException exception) {
        return switch (exception.statusCode()) {
            case HttpStatusCode.BAD_REQUEST -> createDynamoDBException(DYNAMODB_BAD_REQUEST);
            case HttpStatusCode.INTERNAL_SERVER_ERROR -> createDynamoDBException(DYNAMODB_INTERNAL_SERVER_ERROR);
            default -> exception;
        };
    }
}

