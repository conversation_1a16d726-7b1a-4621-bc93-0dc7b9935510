package ca.bnc.payment.moneyrequest.exception;

import ca.bnc.payment.moneyrequest.generated.model.Error;
import org.springframework.http.HttpStatus;

public record ErrorInfo(Error error, HttpStatus status) {

    public static ErrorInfo badRequest(final Error error) {
        return new ErrorInfo(error, HttpStatus.BAD_REQUEST);
    }

    public static ErrorInfo notFound(final Error error) {
        return new ErrorInfo(error, HttpStatus.NOT_FOUND);
    }

    public static ErrorInfo internalServerError(final Error error) {
        return new ErrorInfo(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
