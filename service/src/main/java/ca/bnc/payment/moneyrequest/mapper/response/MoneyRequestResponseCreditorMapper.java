package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.Contact4;
import ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135;
import ca.bnc.payment.moneyrequest.generated.model.ContactDetails;
import ca.bnc.payment.moneyrequest.generated.model.Creditor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class MoneyRequestResponseCreditorMapper {

    private static final String CREDITOR_NAME_NOT_PROVIDED = "NOTPROVIDED";

    public Creditor map(final PartyIdentification135 creditor) {
        return new Creditor()
                .name(Optional.ofNullable(creditor.getName()).orElse(CREDITOR_NAME_NOT_PROVIDED))
                .contactDetails(Optional.ofNullable(creditor.getContactDetails())
                        .map(this::mapContactDetails)
                        .orElse(null));
    }

    private ContactDetails mapContactDetails(final Contact4 contactDetails) {
        return new ContactDetails()
                .mobileNumber(contactDetails.getMobileNumber())
                .emailAddress(contactDetails.getEmailAddress());
    }
}
