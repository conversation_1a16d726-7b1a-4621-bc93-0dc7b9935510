package ca.bnc.payment.moneyrequest.mapper.entity;

import ca.bnc.payment.interac_money_request.generated.model.Contact4;
import ca.bnc.payment.interac_money_request.generated.model.CreditTransferTransaction35;
import ca.bnc.payment.interac_money_request.generated.model.Document12;
import ca.bnc.payment.interac_money_request.generated.model.FraudCheckResult;
import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.interac_money_request.generated.model.PaymentInstruction31;
import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.generated.model.DeclineSupplementaryData;
import ca.bnc.payment.moneyrequest.generated.model.MoneyRequestStatus;
import ca.bnc.payment.moneyrequest.mapper.common.MoneyRequestStatusMapper;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.util.TimeService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static java.util.Objects.nonNull;
import static org.springframework.util.CollectionUtils.isEmpty;

@Component
@RequiredArgsConstructor
public class MoneyRequestEntityMapper {

    private static final long EXPIRATION_TIME_IN_DAYS = 60;

    private final TimeService timeService;
    private final ObjectMapper objectMapper;
    private final MoneyRequestStatusMapper moneyRequestStatusMapper;

    public IncomingMoneyRequestEntity mapForGetMoneyRequest(
            final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse,
            final MoneyRequestContext moneyRequestContext) {
        final PaymentInstruction31 paymentInformation = getPaymentInstruction(incomingRequestForPaymentResponse);
        final CreditTransferTransaction35 creditTransferTransaction = getCreditTransferTransaction(paymentInformation);

        final long expirationDate = timeService.getNowLocalDateTime().plusDays(EXPIRATION_TIME_IN_DAYS).toEpochSecond(ZoneOffset.UTC);
        final String localCreatedDateTime = timeService.getFormattedNowLocalDateTime();
        final InvoiceData invoiceData = initInvoiceData(creditTransferTransaction.getEnclosedFile());

        return IncomingMoneyRequestEntity.builder()
                .interacMoneyRequestId(moneyRequestContext.interacMoneyRequestId())
                .endToEndIdentification(creditTransferTransaction.getPaymentIdentification().getEndToEndIdentification())
                .debtorName(paymentInformation.getDebtor().getName())
                .debtorClientId(moneyRequestContext.clientId())
                .debtorInteracUserId(moneyRequestContext.activePartyIdentifier().interacUserId())
                .amount(creditTransferTransaction.getAmount().getInstructedAmount().getAmount())
                .currency(creditTransferTransaction.getAmount().getInstructedAmount().getCurrency().getValue())
                .creditorInstitutionId(
                        creditTransferTransaction
                                .getCreditorAgent()
                                .getFinancialInstitutionIdentification()
                                .getClearingSystemMemberIdentification()
                                .getMemberIdentification()
                )
                .creditorName(creditTransferTransaction.getCreditor().getName())
                .creditorMobileNumber(getCreditorMobileNumber(creditTransferTransaction))
                .creditorEmailAddress(getCreditorEmailAddress(creditTransferTransaction))
                .remittanceInformation(getRemittanceInformation(creditTransferTransaction))
                .invoiceNumber(invoiceData.invoiceNumber())
                .invoiceIssueDate(invoiceData.issueDate())
                .moneyRequestStatus(moneyRequestStatusMapper.map(incomingRequestForPaymentResponse.getRequestForPaymentStatus()).getValue())
                .expiryDate(paymentInformation.getExpiryDate().toString())
                .sysCreatedDate(localCreatedDateTime)
                .sysModifiedDate(localCreatedDateTime)
                .sysExpirationDate(expirationDate)
                .fraudCheckResultAction(getFraudCheckResult(incomingRequestForPaymentResponse).getAction().getValue())
                .fraudCheckResultScore(getFraudCheckResultScore(incomingRequestForPaymentResponse))
                .fraudCheckResultReason(getFraudCheckResultReason(incomingRequestForPaymentResponse))
                .channelId(moneyRequestContext.channelId())
                .channelType(moneyRequestContext.channelType().getValue())
                .build();
    }

    public IncomingMoneyRequestEntity updateForDeclineMoneyRequest(
            final DeclineRequest declineRequest,
            final IncomingMoneyRequestEntity incomingMoneyRequestEntity
    ) {
        return incomingMoneyRequestEntity.toBuilder()
                .declineReason(
                        Optional.ofNullable(
                                        declineRequest
                                                .getFiToFICustomerCreditTransferV08()
                                                .getCreditTransferTransactionInformation()
                                                .getSupplementaryData()
                                )
                                .map(DeclineSupplementaryData::getDeclineReason)
                                .orElse(null)
                )
                .sysModifiedDate(timeService.getFormattedNowLocalDateTime())
                .moneyRequestStatus(MoneyRequestStatus.DECLINED.getValue())
                .build();
    }

    private String getFraudCheckResultReason(final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse) {
        return Optional.ofNullable(getFraudCheckResult(incomingRequestForPaymentResponse).getReason())
                .map(String::valueOf)
                .orElse(null);
    }

    private String getFraudCheckResultScore(final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse) {
        return Optional.ofNullable(getFraudCheckResult(incomingRequestForPaymentResponse)
                        .getScore())
                .map(String::valueOf)
                .orElse(null);
    }

    private FraudCheckResult getFraudCheckResult(final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse) {
        return incomingRequestForPaymentResponse.getFraudCheckResult();
    }

    private InvoiceData initInvoiceData(final List<Document12> enclosedFiles) {
        if (isEmpty(enclosedFiles)) {
            return InvoiceData.empty();
        }
        return enclosedFiles
                .stream()
                .filter(Objects::nonNull)
                .findFirst()
                .map(enclose -> new InvoiceData(enclose.getIssueDate().toString(), enclose.getIdentification()))
                .orElseGet(InvoiceData::empty);
    }

    private record InvoiceData(String issueDate, String invoiceNumber) {
        public static InvoiceData empty() {
            return new InvoiceData(null, null);
        }
    }

    private String getRemittanceInformation(final CreditTransferTransaction35 creditTransferTransaction) {
        try {
            if (nonNull(creditTransferTransaction.getRemittanceInformation())) {
                return objectMapper.writeValueAsString(creditTransferTransaction.getRemittanceInformation());
            }
            return null;

        } catch (JsonProcessingException e) {
            throw MoneyRequestException.internalServerError(TECHNICAL_ERROR_CODE,
                    "MoneyRequestEntityMapper failed upon deserializing remittanceInfo : " + e.getMessage(),
                    SERVICE_ORIGIN, NA);
        }
    }

    private String getCreditorMobileNumber(final CreditTransferTransaction35 creditTransferTransaction) {
        return Optional.ofNullable(getCreditorContactDetails(creditTransferTransaction))
                .map(Contact4::getMobileNumber)
                .orElse(null);
    }

    private String getCreditorEmailAddress(final CreditTransferTransaction35 creditTransferTransaction) {
        return Optional.ofNullable(getCreditorContactDetails(creditTransferTransaction))
                .map(Contact4::getEmailAddress)
                .orElse(null);
    }

    private CreditTransferTransaction35 getCreditTransferTransaction(final PaymentInstruction31 paymentInformation) {
        return paymentInformation.getCreditTransferTransaction().get(0);
    }

    private PaymentInstruction31 getPaymentInstruction(final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse) {
        return incomingRequestForPaymentResponse.getCreditorPaymentActivationRequest().getPaymentInformation().get(0);
    }

    private Contact4 getCreditorContactDetails(final CreditTransferTransaction35 creditTransferTransaction) {
        return creditTransferTransaction.getCreditor().getContactDetails();
    }
}
