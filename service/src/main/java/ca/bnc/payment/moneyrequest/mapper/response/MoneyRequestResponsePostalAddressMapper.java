package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.CountryCode;
import ca.bnc.payment.moneyrequest.generated.model.AddressType3Choice;
import ca.bnc.payment.moneyrequest.generated.model.PostalAddress24;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class MoneyRequestResponsePostalAddressMapper {

    public PostalAddress24 map(final ca.bnc.payment.interac_money_request.generated.model.PostalAddress24 postalAddress24) {
        return new PostalAddress24()
                .streetName(postalAddress24.getStreetName())
                .buildingNumber(postalAddress24.getBuildingNumber())
                .postCode(postalAddress24.getPostCode())
                .townName(postalAddress24.getTownName())
                .addressLine(postalAddress24.getAddressLine())
                .countrySubDivision(postalAddress24.getCountrySubDivision())
                .department(postalAddress24.getDepartment())
                .subDepartment(postalAddress24.getSubDepartment())
                .addressType(
                        Optional.ofNullable(postalAddress24.getAddressType())
                            .map(this::mapAddressType)
                            .orElse(null)
                )
                .country(
                        Optional.ofNullable(postalAddress24.getCountry())
                                .map(this::mapCountry)
                                .orElse(null)
                );
    }

    private AddressType3Choice mapAddressType(
            final ca.bnc.payment.interac_money_request.generated.model.AddressType3Choice addressType3Choice
    ) {
        return new AddressType3Choice().code(AddressType3Choice.CodeEnum.fromValue(addressType3Choice.getCode().getValue()));
    }

    private PostalAddress24.CountryEnum mapCountry(final CountryCode countryCode) {
        return PostalAddress24.CountryEnum.fromValue(countryCode.getValue());
    }

}
