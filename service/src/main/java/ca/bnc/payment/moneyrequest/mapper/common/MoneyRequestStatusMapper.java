package ca.bnc.payment.moneyrequest.mapper.common;

import ca.bnc.payment.interac_money_request.generated.model.RequestForPaymentStatus;
import ca.bnc.payment.moneyrequest.generated.model.MoneyRequestStatus;
import org.springframework.stereotype.Component;

@Component
public class MoneyRequestStatusMapper {

    public MoneyRequestStatus map(final RequestForPaymentStatus requestForPaymentStatus) {
        return switch (requestForPaymentStatus) {
            case INITIATED -> MoneyRequestStatus.INITIATED;
            case AVAILABLE -> MoneyRequestStatus.AVAILABLE_TO_BE_FULFILLED;
            case FULFILLED -> MoneyRequestStatus.FULFILLED;
            case DECLINED -> MoneyRequestStatus.DECLINED;
            case CANCELLED -> MoneyRequestStatus.CANCELLED;
            case EXPIRED -> MoneyRequestStatus.EXPIRED;
            case DEPOSIT_FAILED -> MoneyRequestStatus.DEPOSIT_FAILED;
            case DEPOSIT_COMPLETE -> MoneyRequestStatus.DEPOSIT_COMPLETE;
        };
    }
}
