package ca.bnc.payment.moneyrequest.controller;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

@Component
public class LogContextHelper {

    private static final String LOG_FIELD_REQUEST_ID = "requestId";
    private static final String LOG_FIELD_CLIENT_ID = "clientId";
    private static final String LOG_FIELD_BNCBUSINESSTRACE_ID = "bncbusinesstraceid";
    private static final String LOG_FIELD_INTERACMONEYREQUEST_ID = "interacMoneyRequestId";


    public Map<String, Object> contextFor(
            final UUID requestId,
            final String clientId,
            final UUID bncBusinessTraceId,
            final String interacMoneyRequestId
    ) {
        return Map.of(
                LOG_FIELD_REQUEST_ID, requestId.toString(),
                LOG_FIELD_CLIENT_ID, clientId,
                LOG_FIELD_BNCBUSINESSTRACE_ID, bncBusinessTraceId.toString(),
                LOG_FIELD_INTERACMONEYREQUEST_ID, interacMoneyRequestId
        );
    }
}
