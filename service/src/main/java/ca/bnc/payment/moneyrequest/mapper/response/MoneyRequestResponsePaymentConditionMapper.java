package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.PaymentCondition1;
import ca.bnc.payment.moneyrequest.generated.model.PaymentCondition;
import org.springframework.stereotype.Component;

@Component
public class MoneyRequestResponsePaymentConditionMapper {

    public PaymentCondition map(final PaymentCondition1 paymentCondition) {
        return new PaymentCondition().amountModificationAllowed(paymentCondition.getAmountModificationAllowed());
    }
}
