package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.CategoryPurpose1Choice;
import ca.bnc.payment.interac_money_request.generated.model.PaymentInstruction31;
import ca.bnc.payment.interac_money_request.generated.model.PaymentTypeInformation26;
import ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.ANN;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.AP;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.BPY;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.CCB;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.DON;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.EI;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.EXP;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.INS;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.INV;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.MTG;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.OAS;
import static ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose.CodeEnum.RLS;

@Component
public class MoneyRequestResponseCategoryPurposeMapper {

    public CategoryPurpose map(final PaymentInstruction31 paymentInstruction) {
        return Optional.ofNullable(paymentInstruction)
                .map(PaymentInstruction31::getPaymentTypeInformation)
                .map(PaymentTypeInformation26::getCategoryPurpose)
                .map(this::mapCategoryPurpose)
                .map(code -> new CategoryPurpose().code(code))
                .orElse(null);
    }

    private CategoryPurpose.CodeEnum mapCategoryPurpose(final CategoryPurpose1Choice categoryPurpose) {
        return switch (categoryPurpose.getCode()) {
            case _240 -> ANN;
            case _260 -> INV;
            case _330 -> INS;
            case _370 -> MTG;
            case _400 -> RLS;
            case _430 -> BPY;
            case _460 -> AP;
            case _480 -> DON;
            case _452 -> EXP;
            case _308 -> CCB;
            case _311 -> OAS;
            case _318 -> EI;
            default -> null;
        };
    }
}
