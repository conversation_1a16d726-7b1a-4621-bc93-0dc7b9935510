package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.GroupHeader78;
import ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135;
import ca.bnc.payment.moneyrequest.generated.model.GroupHeader;
import ca.bnc.payment.moneyrequest.generated.model.InitiatingParty;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class MoneyRequestResponseGroupHeaderMapper {

    private static final Integer MONEY_REQUEST_NUMBER_OF_TRANSACTIONS = 1;
    private static final String INITIATING_PARTY_NAME_NOT_PROVIDED = "NOTPROVIDED";

    public GroupHeader map(final GroupHeader78 groupHeader) {
        return new GroupHeader()
                .messageIdentification(groupHeader.getMessageIdentification())
                .creationDateTime(groupHeader.getCreationDatetime())
                .numberOfTransactions(MONEY_REQUEST_NUMBER_OF_TRANSACTIONS)
                .initiatingParty(mapInitiatingPartyName(groupHeader.getInitiatingParty()));
    }

    private InitiatingParty mapInitiatingPartyName(final PartyIdentification135 partyIdentification) {
        return Optional.ofNullable(partyIdentification.getName())
                .map(name -> new InitiatingParty().name(name))
                .orElseGet(() -> new InitiatingParty().name(INITIATING_PARTY_NAME_NOT_PROVIDED));
    }

}
