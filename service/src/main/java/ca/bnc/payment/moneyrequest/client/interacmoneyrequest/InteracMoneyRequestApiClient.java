package ca.bnc.payment.moneyrequest.client.interacmoneyrequest;

import ca.bnc.payment.interac_money_request.generated.api.RequestsApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "interacMoneyRequestApiClient",
        url = "${providers.interac-money-request.url}",
        configuration = InteracMoneyRequestApiErrorDecoder.class)
public interface InteracMoneyRequestApiClient extends RequestsApi {
}
