package ca.bnc.payment.moneyrequest.client.party;

import ca.bnc.payment.moneyrequest.client.interceptor.InterceptorFactory;
import feign.RequestInterceptor;
import feign.RequestTemplate;

public final class PartyApiOktaInterceptor implements RequestInterceptor {

    private static final String TOKEN_NAME = "partyScope";
    private final RequestInterceptor delegate;

    public PartyApiOktaInterceptor(final InterceptorFactory interceptorFactory) {
        this.delegate = interceptorFactory.createBncInterceptor(TOKEN_NAME);
    }

    @Override
    public void apply(final RequestTemplate template) {
        delegate.apply(template);
    }
}
