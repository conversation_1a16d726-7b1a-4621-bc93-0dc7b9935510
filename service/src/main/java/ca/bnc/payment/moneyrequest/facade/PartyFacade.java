package ca.bnc.payment.moneyrequest.facade;

import ca.bnc.payment.moneyrequest.adapter.PartyAdapter;
import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.model.ActivePartyIdentifier;
import ca.nbc.payment.pmtpartnersparty.model.IdentifierStatus;
import ca.nbc.payment.pmtpartnersparty.model.IdentifierType;
import ca.nbc.payment.pmtpartnersparty.model.Identifiers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

import static ca.bnc.payment.moneyrequest.constant.Constant.CUSTOMER_NOT_FOUND_CODE;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_ID_NOT_FOUND;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_SERVICE_ORIGIN;
import static ca.nbc.payment.pmtpartnersparty.model.IdentifierStatus.ACTIVE;

@Component
@RequiredArgsConstructor
public class PartyFacade {

    private static final List<IdentifierType> IDENTIFIER_TYPES = List.of(IdentifierType.INTERAC_USER_ID);
    private static final IdentifierStatus IDENTIFIER_STATUS = IdentifierStatus.ACTIVE;
    private static final String ACCEPT_VERSION = "v1";

    private final PartyAdapter partyAdapter;

    public ActivePartyIdentifier fetchActiveInteracIdentifier(final String xChannelId, final UUID xRequestId, final String xClientId) {

        final Identifiers identifiers = partyAdapter.getPartyClientById(
                xChannelId,
                ACCEPT_VERSION,
                xRequestId.toString(),
                xClientId,
                IDENTIFIER_TYPES,
                IDENTIFIER_STATUS
            );
            return extractActiveIdentifierOrThrowException(identifiers, xClientId);
    }

    private ActivePartyIdentifier extractActiveIdentifierOrThrowException(final Identifiers identifiers, final String clientId) {
        if (identifiers == null) {
            throw createBadRequestException(clientId);
        }
        return identifiers
                .getIdentifiers()
                .stream()
                .filter(identifier -> identifier.getStatus() == ACTIVE)
                .map(pmtIdentifier -> new ActivePartyIdentifier(pmtIdentifier.getValue(), identifiers.getMerger().getClientType()))
                .findFirst()
                .orElseThrow(() -> createBadRequestException(clientId));
    }

    private MoneyRequestException createBadRequestException(final String clientId) {
        return MoneyRequestException.badRequest(
                CUSTOMER_NOT_FOUND_CODE,
                INTERAC_API_ID_NOT_FOUND.formatted(clientId),
                PARTY_SERVICE_ORIGIN,
                NA
        );
    }

}
