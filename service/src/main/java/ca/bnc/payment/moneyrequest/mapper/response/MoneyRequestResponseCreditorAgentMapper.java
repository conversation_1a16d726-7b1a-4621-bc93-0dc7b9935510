package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.BranchAndFinancialInstitutionIdentification6;
import ca.bnc.payment.moneyrequest.generated.model.ClearingSystemMemberIdentification;
import ca.bnc.payment.moneyrequest.generated.model.CreditorAgent;
import ca.bnc.payment.moneyrequest.generated.model.FinancialInstitutionIdentification;
import org.springframework.stereotype.Component;

@Component
public class MoneyRequestResponseCreditorAgentMapper {

    public CreditorAgent map(final BranchAndFinancialInstitutionIdentification6 creditorAgent) {
        final String memberIdentification = creditorAgent
                .getFinancialInstitutionIdentification()
                .getClearingSystemMemberIdentification()
                .getMemberIdentification();
        return new CreditorAgent().financialInstitutionIdentification(
                new FinancialInstitutionIdentification().clearingSystemMemberIdentification(
                        new ClearingSystemMemberIdentification().memberIdentification(memberIdentification)
                )
        );
    }

}
