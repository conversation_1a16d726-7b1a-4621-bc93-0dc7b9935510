package ca.bnc.payment.moneyrequest.facade;

import ca.bnc.payment.interac_money_request.generated.model.DeclineRequestForPaymentRequest;
import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.moneyrequest.adapter.InteracMoneyRequestAdapter;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.generated.model.DeclineSupplementaryData;
import ca.bnc.payment.moneyrequest.mapper.interac.InteracHeaderMapper;
import ca.bnc.payment.moneyrequest.model.InteracHeader;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;


@Component
@RequiredArgsConstructor
public class InteracMoneyRequestFacade {

    private final InteracMoneyRequestAdapter interacMoneyRequestAdapter;
    private final InteracHeaderMapper interacHeaderMapper;

    public IncomingRequestForPaymentResponse getIncomingRequestForPayment(final MoneyRequestContext moneyRequestContext) {
        final InteracHeader interacHeader = interacHeaderMapper.mapForGet(moneyRequestContext);
        return interacMoneyRequestAdapter.getIncomingRequestForPayment(
                interacHeader.xEtParticipantId(),
                interacHeader.xEtParticipantUserId(),
                interacHeader.xEtIndirectConnectorId(),
                interacHeader.authorization(),
                interacHeader.xEtRequestId(),
                interacHeader.xEtChannelIndicator(),
                interacHeader.xEtApiSignature(),
                interacHeader.xEtApiSignatureType(),
                interacHeader.xEtTransactionTime(),
                moneyRequestContext.interacMoneyRequestId()
        );
    }

    public void declineRequestForPayment(final MoneyRequestContext moneyRequestContext,
                                         final DeclineRequest declineRequest
    ) {
        DeclineRequestForPaymentRequest declineRequestForPaymentRequest = new DeclineRequestForPaymentRequest()
                .declineReason(Optional.ofNullable(
                                declineRequest
                                        .getFiToFICustomerCreditTransferV08()
                                        .getCreditTransferTransactionInformation()
                                        .getSupplementaryData()
                        )
                        .map(DeclineSupplementaryData::getDeclineReason)
                        .orElse(null));
        final InteracHeader interacHeader = interacHeaderMapper.mapForDecline(moneyRequestContext, declineRequestForPaymentRequest);

            interacMoneyRequestAdapter.declineRequestForPayment(
                    interacHeader.xEtParticipantId(),
                    interacHeader.xEtParticipantUserId(),
                    interacHeader.xEtIndirectConnectorId(),
                    interacHeader.authorization(),
                    interacHeader.xEtRequestId(),
                    interacHeader.xEtChannelIndicator(),
                    interacHeader.xEtApiSignature(),
                    interacHeader.xEtApiSignatureType(),
                    interacHeader.xEtTransactionTime(),
                    moneyRequestContext.interacMoneyRequestId(),
                    declineRequestForPaymentRequest
            );
    }

}
