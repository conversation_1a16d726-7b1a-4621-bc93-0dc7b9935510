package ca.bnc.payment.moneyrequest.adapter.async.producer;


import ca.bnc.payment.kafkaproducer.KafkaProducer;
import ca.bnc.payment.model.KafkaMessage;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEvent;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

@Component
public class AsyncPptdPublisher {

    private final String pptdTopic;
    private final KafkaProducer kafkaProducer;
    private final LogContextHolder logContextHolder;
    private static final String LOG_MESSAGING_ID = "PPTD";

    public AsyncPptdPublisher(@Value("${kafka.topics.pptd}") final String pptdTopic,
                              final KafkaProducer kafkaProducer,
                              final LogContextHolder logContextHolder) {
        this.pptdTopic = pptdTopic;
        this.kafkaProducer = kafkaProducer;
        this.logContextHolder = logContextHolder;
    }

    @Async("workerThreadExecutor")
    public void sendMessage(final DomesticETransferEvent messageObject,
                            final UUID requestId,
                            final LogContextHolder.LogContext logContext) {
        logContextHolder.runWithContext(
                () -> kafkaProducer.doSendMessage(new KafkaMessage<>(
                        pptdTopic, messageObject, Map.of("action", "POST"), requestId.toString(), LOG_MESSAGING_ID)),
                logContext);
    }
}
