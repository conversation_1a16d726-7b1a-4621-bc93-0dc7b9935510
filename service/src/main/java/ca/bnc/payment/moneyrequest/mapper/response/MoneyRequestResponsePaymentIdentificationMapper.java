package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.PaymentIdentification6;
import org.springframework.stereotype.Component;

@Component
public class MoneyRequestResponsePaymentIdentificationMapper {

    public PaymentIdentification6 map(
            final ca.bnc.payment.interac_money_request.generated.model.PaymentIdentification6 paymentIdentification
    ) {
        return new PaymentIdentification6()
                .instructionIdentification(paymentIdentification.getInstructionIdentification())
                .endToEndIdentification(paymentIdentification.getEndToEndIdentification());
    }
}
