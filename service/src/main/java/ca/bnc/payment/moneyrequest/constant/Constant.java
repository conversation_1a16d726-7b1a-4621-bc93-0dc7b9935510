package ca.bnc.payment.moneyrequest.constant;

public final class Constant {

    public static final String UTC_DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    public static final String REQUEST_INVALID_CODE = "REQUEST_INVALID";
    public static final String TECHNICAL_ERROR_CODE = "TECHNICAL_ERROR";
    public static final String CUSTOMER_NOT_FOUND_CODE = "CUSTOMER_NOT_FOUND";
    public static final String INVALID_REQUEST_STATUS_CODE = "CANNOT_DUE_REQUEST_STATUS";
    public static final String SAME_RECIPIENT_SENDER_CODE = "SAME_RECIPIENT_SENDER";

    public static final String SERVICE_ORIGIN = "pmt-incoming-money-request-api";
    public static final String PARTY_SERVICE_ORIGIN = "pmt-partners-party-api";
    public static final String NA = "NA";

    public static final String GENERIC_ERROR_LOG = "code: %s | text: %s | origin: %s | rule: %s";

    public static final String TOKEN_GENERATION_FAILED = "The token generation failed";
    public static final String INTERNAL_SERVER_ERROR_GENERIC = "An unexpected error occurred.";
    public static final String CANNOT_DUE_REQUEST_STATUS = "Creditor (customer) is not allowed to perform this operation " +
            "due to current status of the request for payment";
    public static final String INVALID_MONEY_REQUEST_STATUS = "Invalid MoneyRequestStatus";

    public static final String PARTY_API_GENERIC_ERROR = "The Payment Party API encountered an error";
    public static final String PARTY_API_UNAVAILABLE = "The Payment Party API is unreachable";
    public static final String PARTY_API_BAD_REQUEST = "Bad request received by the Payment Party API";
    public static final String PARTY_API_INTERNAL_SERVER_ERROR = "The Payment Party API encountered an unexpected condition "
            + "that prevented it from fulfilling the request.";
    public static final String PARTY_API_SERVICE_UNAVAILABLE = "The Payment Party API is not ready to handle the request.";

    public static final String INTERAC_API_GENERIC_ERROR = "The Interac Payments API encountered an error";
    public static final String INTERAC_API_UNAVAILABLE = "The Interac Payments API is unreachable";
    public static final String INTERAC_API_BAD_REQUEST = "Bad request received by the Interac Payments API";
    public static final String INTERAC_API_UNAUTHORIZED = "The service wants to get a protected resource from Interac without providing the proper authorization.";
    public static final String INTERAC_API_FORBIDDEN = "The service does not have the necessary permissions to get the sent domestic payment informations from Interac";
    public static final String INTERAC_API_TOO_MANY_REQUESTS = "The Interac Payments API blocked the %s INCOMING MONEY REQUEST request call due to rate limiting";
    public static final String INTERAC_API_INTERNAL_SERVER_ERROR = "The Interac Payments API encountered an unexpected condition that prevented it " +
            "from fulfilling the request.";
    public static final String INTERAC_API_ID_NOT_FOUND = "The Interac User Id of the client identified by %S was not found.";
    public static final String INTERAC_API_SERVICE_UNAVAILABLE = "The Interac Payments API is not ready to handle the request.";

    public static final String DYNAMODB_UNREACHABLE = "AWS DynamoDB API is unreachable";
    public static final String DYNAMODB_BAD_REQUEST = "Bad request received after calling GetItem operation from AWS DynamoDB API";
    public static final String DYNAMODB_INTERNAL_SERVER_ERROR = "The AWS DynamoDB API encountered an unexpected condition that prevented it "
            + "from fulfilling the GetItem request.";
    public static final String DYNAMODB_UNEXPECTED_ERROR = "An unexpected error occurred while calling AWS DynamoDB API";
    public static final String NO_REASON_PROVIDED = "No reason provided";
    public static final String INTERAC_ERR_TABLE = "InteracErrCodeToNormalizedErrCode";

    private Constant() { /* empty */ }
}
