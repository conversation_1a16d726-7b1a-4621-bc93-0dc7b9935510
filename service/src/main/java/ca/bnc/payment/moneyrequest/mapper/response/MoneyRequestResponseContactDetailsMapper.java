package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.Contact4;
import org.springframework.stereotype.Component;

@Component
public class MoneyRequestResponseContactDetailsMapper {

    public Contact4 map(final ca.bnc.payment.interac_money_request.generated.model.Contact4 contactDetails) {
        return new Contact4()
                .name(contactDetails.getName())
                .emailAddress(contactDetails.getEmailAddress())
                .mobileNumber(contactDetails.getMobileNumber())
                .phoneNumber(contactDetails.getPhoneNumber())
                .faxNumber(contactDetails.getFaxNumber());
    }

}
