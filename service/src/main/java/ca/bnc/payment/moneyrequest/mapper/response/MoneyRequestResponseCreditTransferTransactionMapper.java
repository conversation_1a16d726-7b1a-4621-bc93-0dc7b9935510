package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.CreditTransferTransaction35;
import ca.bnc.payment.moneyrequest.generated.model.CreditTransferTransaction;
import ca.bnc.payment.moneyrequest.generated.model.Invoice;
import ca.bnc.payment.moneyrequest.generated.model.RemittanceInformation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class MoneyRequestResponseCreditTransferTransactionMapper {

    private final MoneyRequestResponsePaymentIdentificationMapper moneyRequestResponsePaymentIdentificationMapper;
    private final MoneyRequestResponseAmountMapper moneyRequestResponseAmountMapper;
    private final MoneyRequestResponseCreditorAgentMapper moneyRequestResponseCreditorAgentMapper;
    private final MoneyRequestResponseCreditorMapper moneyRequestResponseCreditorMapper;
    private final MoneyRequestResponseRemittanceInformationMapper moneyRequestResponseRemittanceInformationMapper;
    private final MoneyRequestResponseInvoiceMapper moneyRequestResponseInvoiceMapper;

    public CreditTransferTransaction map(final CreditTransferTransaction35 creditTransferTransaction) {
        return new CreditTransferTransaction()
                .paymentIdentification(moneyRequestResponsePaymentIdentificationMapper.map(
                        creditTransferTransaction.getPaymentIdentification())
                )
                .amount(moneyRequestResponseAmountMapper.map(creditTransferTransaction.getAmount()))
                .creditorAgent(moneyRequestResponseCreditorAgentMapper.map(creditTransferTransaction.getCreditorAgent()))
                .creditor(moneyRequestResponseCreditorMapper.map(creditTransferTransaction.getCreditor()))
                .remittanceInformation(mapRemittanceInformation(creditTransferTransaction))
                .invoice(mapInvoice(creditTransferTransaction));
    }

    private RemittanceInformation mapRemittanceInformation(final CreditTransferTransaction35 creditTransferTransaction) {
        return Optional.ofNullable(creditTransferTransaction.getRemittanceInformation())
                .map(moneyRequestResponseRemittanceInformationMapper::map)
                .orElse(null);
    }

    private Invoice mapInvoice(final CreditTransferTransaction35 creditTransferTransaction) {
        return Optional.ofNullable(creditTransferTransaction.getEnclosedFile())
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(moneyRequestResponseInvoiceMapper::map)
                .orElse(null);
    }

}
