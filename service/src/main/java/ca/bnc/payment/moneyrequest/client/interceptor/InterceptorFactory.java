package ca.bnc.payment.moneyrequest.client.interceptor;

import ca.bnc.payment.moneyrequest.util.ChannelIdUtil;
import ca.bnc.payment.moneyrequest.util.ParticipantIdUtil;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import feign.RequestInterceptor;
import org.springframework.stereotype.Component;

@Component
public final class InterceptorFactory {
    private final OktaClientTokenManager oktaClientTokenManager;
    private final ParticipantIdUtil participantIdUtil;
    private final ChannelIdUtil channelIdUtil;

    public InterceptorFactory(final OktaClientTokenManager oktaClientTokenManager,
                              final ParticipantIdUtil participantIdUtil,
                              final ChannelIdUtil channelIdUtil) {
        this.oktaClientTokenManager = oktaClientTokenManager;
        this.participantIdUtil = participantIdUtil;
        this.channelIdUtil = channelIdUtil;
    }

    public RequestInterceptor createBncInterceptor(final String tokenConfigName) {
        TokenProvider tokenProvider = new ConfigTokenProvider(oktaClientTokenManager, tokenConfigName);
        RequestInterceptor authInterceptor = new SimpleOktaInterceptor(tokenProvider);
        return new BncOktaInterceptor(authInterceptor, participantIdUtil, channelIdUtil);
    }

}
