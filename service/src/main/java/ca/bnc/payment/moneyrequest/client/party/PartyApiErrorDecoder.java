package ca.bnc.payment.moneyrequest.client.party;

import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.Error;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_API_BAD_REQUEST;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_API_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_API_SERVICE_UNAVAILABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;

@RequiredArgsConstructor
public class PartyApiErrorDecoder implements ErrorDecoder {

    private final PartyApiDecoderErrorsBuilder partyApiDecoderErrorsBuilder;

    @Override
    public Exception decode(final String methodKey, final Response response) {
        final HttpStatus httpStatus = HttpStatus.valueOf(response.status());
        final Error error = partyApiDecoderErrorsBuilder.buildErrorFromResponseBody(response);

        return switch (httpStatus) {
            case BAD_REQUEST -> MoneyRequestException.internalServerError(
                            TECHNICAL_ERROR_CODE,
                            PARTY_API_BAD_REQUEST,
                            PARTY_SERVICE_ORIGIN,
                            NA
                    );
            case INTERNAL_SERVER_ERROR -> MoneyRequestException.internalServerError(
                    TECHNICAL_ERROR_CODE,
                    PARTY_API_INTERNAL_SERVER_ERROR,
                    PARTY_SERVICE_ORIGIN,
                    NA
            );
            case SERVICE_UNAVAILABLE -> MoneyRequestException.internalServerError(
                            TECHNICAL_ERROR_CODE,
                            PARTY_API_SERVICE_UNAVAILABLE,
                            PARTY_SERVICE_ORIGIN,
                            NA
                    );
            default -> MoneyRequestException.internalServerError(error);
        };
    }
}
