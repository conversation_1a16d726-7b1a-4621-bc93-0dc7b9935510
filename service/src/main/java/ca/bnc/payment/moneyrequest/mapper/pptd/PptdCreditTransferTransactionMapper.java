package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.ActiveCurrencyAndAmount;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.ChargeBearerType1Code;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.Contact4;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.PartyIdentification135;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.PaymentTypeInformation28;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;

@RequiredArgsConstructor
@Component
public class PptdCreditTransferTransactionMapper {

    private final PptdPaymentIdentificationMapper pptdPaymentIdentificationMapper;
    private final TimeService timeService;

    private static final String FULFILL_REQUEST_FOR_PAYMENT = "FULFILL_REQUEST_FOR_PAYMENT";
    private static final BigDecimal MIN_VALID_AMOUNT = new BigDecimal("0.01");
    private static final BigDecimal MAX_VALID_AMOUNT = new BigDecimal("**************");

    public CreditTransferTransaction39 map(
            final DeclineRequest declineRequest,
            final IncomingMoneyRequestEntity incomingMoneyRequestEntity
    ) {
        return new CreditTransferTransaction39()
                .paymentIdentification(pptdPaymentIdentificationMapper.map(
                        declineRequest
                                .getFiToFICustomerCreditTransferV08()
                                .getCreditTransferTransactionInformation()
                                .getPaymentIdentification())
                )
                .paymentTypeInformation(
                        new PaymentTypeInformation28()
                                .localInstrument(new LocalInstrument2Choice().proprietary(FULFILL_REQUEST_FOR_PAYMENT))
                )
                .interbankSettlementAmount(
                        new ActiveCurrencyAndAmount()
                                .amount(mapAmount(incomingMoneyRequestEntity.getAmount()))
                                .currency(mapCurrency(incomingMoneyRequestEntity.getCurrency()))
                )
                .interbankSettlementDate(parseInterbankSettlementDate(incomingMoneyRequestEntity.getSysModifiedDate()))
                .chargeBearer(ChargeBearerType1Code.SLEV)
                .debtor(new PartyIdentification135().name(incomingMoneyRequestEntity.getDebtorName()))
                .creditor(new PartyIdentification135()
                        .name(incomingMoneyRequestEntity.getCreditorName())
                        .contactDetails(new Contact4()
                                .phoneNumber(incomingMoneyRequestEntity.getCreditorMobileNumber())
                                .emailAddress(incomingMoneyRequestEntity.getCreditorEmailAddress())
                        )
                );
    }

    private LocalDate parseInterbankSettlementDate(final String sysModifiedDate) {
        try {
            return timeService.parseToLocalDate(sysModifiedDate);
        } catch (DateTimeParseException dateTimeParseException) {
            throw new IllegalArgumentException("Error building Kafka message for PPTD: invalid interbank settlement date '%s'"
                    .formatted(sysModifiedDate));
        }
    }

    private BigDecimal mapAmount(final BigDecimal amount) {
        if (amount == null || isAmountOutsidePptdContractBoundaries(amount)) {
            throw new IllegalArgumentException(
                    "Error building Kafka message for PPTD: invalid amount '%s' (not between 0.01 and **************)".formatted(amount)
            );
        }
        return amount;
    }

    private boolean isAmountOutsidePptdContractBoundaries(final BigDecimal amount) {
        return amount.compareTo(MIN_VALID_AMOUNT) < 0 || amount.compareTo(MAX_VALID_AMOUNT) > 0;
    }

    private String mapCurrency(final String currency) {
        if (currency == null) {
            throw new IllegalArgumentException("Error building Kafka message for PPTD: currency is null");
        }
        return currency;
    }

}
