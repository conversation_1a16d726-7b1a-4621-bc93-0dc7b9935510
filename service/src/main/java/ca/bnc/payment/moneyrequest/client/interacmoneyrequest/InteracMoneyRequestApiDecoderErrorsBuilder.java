package ca.bnc.payment.moneyrequest.client.interacmoneyrequest;

import ca.bnc.payment.interac_money_request.generated.model.ErrorModel;
import ca.bnc.payment.moneyrequest.generated.model.Error;
import ca.bnc.payment.normalization.lib.service.NormalizationService;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Optional;

import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_GENERIC_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_ERR_TABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;

@Component
@AllArgsConstructor
public class InteracMoneyRequestApiDecoderErrorsBuilder {

    private static final Logger LOGGER = LoggerFactory.getLogger(InteracMoneyRequestApiDecoderErrorsBuilder.class.getName());

    private static final Error DEFAULT_ERROR = new Error()
            .code(TECHNICAL_ERROR_CODE)
            .text(INTERAC_API_GENERIC_ERROR)
            .origin(SERVICE_ORIGIN)
            .rule(NA);


    private final ObjectMapper objectMapper;
    private final LoggingFacade loggingFacade;
    private final NormalizationService normalizationService;

    public Error buildErrorFromResponseBody(final Response response) {
        return Optional.ofNullable(response.body())
                .map(this::getError)
                .orElse(DEFAULT_ERROR);
    }

    private Error getError(final Response.Body body) {
        Error error = null;
        try (InputStream inputStream = body.asInputStream()) {
            final ErrorModel interacError = objectMapper.readValue(inputStream, ErrorModel.class);
            error = new Error()
                    .code(normalizationService.normalize(INTERAC_ERR_TABLE, interacError.getCode()).orElse(TECHNICAL_ERROR_CODE))
                    .text(interacError.getText())
                    .origin(SERVICE_ORIGIN)
                    .rule(NA);
        } catch (final Exception exception) {
            loggingFacade.error(LOGGER, "An error occurred while parsing error response from the Interac Payments API", exception);
        }
        return error;
    }

}
