package ca.bnc.payment.moneyrequest.mapper.interac;

import ca.bnc.payment.interac_money_request.generated.model.ChannelIndicator;
import ca.bnc.payment.interac_money_request.generated.model.DeclineRequestForPaymentRequest;
import ca.bnc.payment.interac_money_request.generated.model.SignatureType;
import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.model.InteracHeader;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.util.ParticipantIdUtil;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ca.nbc.payment.pmt_security_library.interac.InteracEtransferClientTokenManager;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.Optional;

import static ca.bnc.payment.moneyrequest.generated.model.ChannelType.WEB;

@Component
@RequiredArgsConstructor
public class InteracHeaderMapper {
    private static final Logger LOGGER = LoggerFactory.getLogger(InteracHeaderMapper.class);
    private final ParticipantIdUtil participantIdUtil;
    private final InteracEtransferClientTokenManager interacEtransferClientTokenManager;
    private final TimeService timeService;
    private final ObjectMapper objectMapper;
    private final LoggingFacade loggingFacade;

    public InteracHeader mapForGet(final MoneyRequestContext moneyRequestContext) {
        return mapInteracHeader(moneyRequestContext, null);
    }

    public InteracHeader mapForDecline(final MoneyRequestContext moneyRequestContext,
                                       final DeclineRequestForPaymentRequest declineRequest) {
        return mapInteracHeader(moneyRequestContext, declineRequest);
    }

    private InteracHeader mapInteracHeader(final MoneyRequestContext moneyRequestContext,
                                          final DeclineRequestForPaymentRequest payload) {
        final OffsetDateTime transactionTime = timeService.getNowOffsetDateTime();
        return createInteracHeader(
                moneyRequestContext,
                getPayloadSignature(payload, transactionTime),
                transactionTime
        );
    }

    private InteracHeader createInteracHeader(final MoneyRequestContext moneyRequestContext,
                                              final String signature,
                                              final OffsetDateTime transactionTime) {
        return InteracHeader.builder()
                .xEtParticipantId(participantIdUtil.getDirectParticipantIdentifier())
                .xEtParticipantUserId(moneyRequestContext.activePartyIdentifier().interacUserId())
                .xEtIndirectConnectorId(getIndirectConnectorId(moneyRequestContext.activePartyIdentifier().clientType()))
                .authorization(null)
                .xEtRequestId(moneyRequestContext.requestId().toString())
                .xEtChannelIndicator(getChannelIndicatorFromChannelType(moneyRequestContext.channelType()))
                .xEtApiSignature(signature)
                .xEtApiSignatureType(SignatureType.PAYLOAD_DIGEST_SHA256)
                .xEtTransactionTime(transactionTime)
                .build();
    }

    private String getPayloadSignature(final DeclineRequestForPaymentRequest payload, final OffsetDateTime transactionTime) {
        try {
            final String httpPayload = Objects.isNull(payload) ? null : objectMapper.writeValueAsString(payload);
            return Optional
                    .ofNullable(interacEtransferClientTokenManager.getTokenAccess(httpPayload, transactionTime))
                    .orElse(StringUtils.SPACE);
        } catch (Exception e) {
            loggingFacade.error(LOGGER, e.getMessage());
            return StringUtils.SPACE;
        }
    }

    private String getIndirectConnectorId(final ClientType clientType) {
        return clientType == ClientType.ORGANISATION ? participantIdUtil.getIndirectConnectorIdentifier() : null;
    }

    private ChannelIndicator getChannelIndicatorFromChannelType(final ChannelType xChannelType) {
        if (WEB == xChannelType) {
            return ChannelIndicator.ONLINE;
        }
        return ChannelIndicator.MOBILE;
    }
}
