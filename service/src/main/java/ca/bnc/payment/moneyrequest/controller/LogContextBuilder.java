package ca.bnc.payment.moneyrequest.controller;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

@Component
public class LogContextBuilder {

    public static final String LOG_FIELD_REQUEST_ID = "requestId";
    public static final String LOG_FIELD_USER_ID = "userId";

    public Map<String, Object> contextFor(final UUID requestId) {
        return Map.of(LOG_FIELD_REQUEST_ID, requestId.toString());
    }

    public Map<String, Object> contextFor(final UUID requestId, final String userId) {
        return Map.of(
                LOG_FIELD_REQUEST_ID, requestId.toString(),
                LOG_FIELD_USER_ID, userId
        );
    }

}
