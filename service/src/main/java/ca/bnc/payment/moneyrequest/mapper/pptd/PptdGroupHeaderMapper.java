package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.GroupHeader93;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class PptdGroupHeaderMapper {

    private static final String NUMBER_OF_TRANSACTIONS = "1";

    public GroupHeader93 map(final DeclineRequest declineRequest) {
        final ca.bnc.payment.moneyrequest.generated.model.GroupHeader93 groupHeader = declineRequest
                .getFiToFICustomerCreditTransferV08()
                .getGroupHeader();
        return new GroupHeader93()
                .messageIdentification(groupHeader.getMessageIdentification())
                .creationDatetime(groupHeader.getCreationDateTime())
                .numberOfTransactions(NUMBER_OF_TRANSACTIONS);
    }
}
