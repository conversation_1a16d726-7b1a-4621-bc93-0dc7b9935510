package ca.bnc.payment.moneyrequest.adapter;

import ca.bnc.payment.moneyrequest.client.party.PartyApiClient;
import ca.bnc.payment.moneyrequest.exception.RetryableMoneyRequestException;
import ca.nbc.payment.pmtpartnersparty.model.IdentifierStatus;
import ca.nbc.payment.pmtpartnersparty.model.IdentifierType;
import ca.nbc.payment.pmtpartnersparty.model.Identifiers;
import feign.RetryableException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.util.List;

import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_API_UNAVAILABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;

@Component
@RequiredArgsConstructor
public class PartyAdapter {

    private final PartyApiClient partyApiClient;

    @Retryable(retryFor = {RetryableMoneyRequestException.class},
            maxAttemptsExpression = "${providers.party.retry.attempts}",
            backoff = @Backoff(delayExpression = "${providers.party.retry.backoff}"))
    public Identifiers getPartyClientById(final String xChannelId,
                                          final String acceptVersion,
                                          final String xRequestId,
                                          final String clientId,
                                          final List<IdentifierType> identifierTypes,
                                          final IdentifierStatus status) {
        try {
            final ResponseEntity<Identifiers> partyClientResponse = partyApiClient.getPartyIdentifierById(
                    xChannelId,
                    acceptVersion,
                    xRequestId,
                    clientId,
                    identifierTypes,
                    status
            );
            return partyClientResponse.getBody();
        } catch (RetryableException e) {
            throw new RetryableMoneyRequestException(
                    TECHNICAL_ERROR_CODE,
                    PARTY_API_UNAVAILABLE,
                    PARTY_SERVICE_ORIGIN,
                    NA
            );
        }
    }
}
