package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.GroupHeader78;
import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.moneyrequest.generated.model.CreditorPaymentActivationRequest;
import ca.bnc.payment.moneyrequest.generated.model.DomesticFulfillmentMoneyRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MoneyRequestResponseMapper {

    private final MoneyRequestResponseGroupHeaderMapper moneyRequestResponseGroupHeaderMapper;
    private final MoneyRequestResponsePaymentInformationMapper moneyRequestResponsePaymentInformationMapper;

    public DomesticFulfillmentMoneyRequest map(final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse) {
        final GroupHeader78 groupHeader = incomingRequestForPaymentResponse.getCreditorPaymentActivationRequest().getGroupHeader();
        return new DomesticFulfillmentMoneyRequest().creditorPaymentActivationRequest(
                new CreditorPaymentActivationRequest()
                        .groupHeader(moneyRequestResponseGroupHeaderMapper.map(groupHeader))
                        .paymentInformation(moneyRequestResponsePaymentInformationMapper.map(incomingRequestForPaymentResponse))
        );
    }

}
