@startuml
' See https://github.com/awslabs/aws-icons-for-plantuml/blob/main/AWSSymbols.md
!define AWSPuml https://raw.githubusercontent.com/awslabs/aws-icons-for-plantuml/v15.0/dist
!include AWSPuml/AWSCommon.puml
!include AWSPuml/Compute/all.puml
!include AWSPuml/Database/all.puml
!include  AWSPuml/ApplicationIntegration/all.puml
!include  AWSPuml/Containers/all.puml

skinparam wrapWidth 0
skinparam maxMessageSize 0


' avoid problems with angled crows feet
skinparam linetype ortho
skinparam Shadowing false
skinparam defaulttextalignment center
skinparam SequenceMessageAlign center
skinparam SequenceGroupBodyBackgroundColor transparent



skinparam sequencebox {
    BorderColor #grey
    BackGroundColor #WhiteSmoke
}

skinparam participant {
    BackgroundColor #white
    BorderColor #black
}

skinparam sequence {
LifeLineBorderColor #black
}
skinparam sequenceReferenceBackgroundColor #palegreen
skinparam sequenceReferenceHeaderBackgroundColor #limegreen
skinparam sequenceGroupBackgroundColor #skyblue
skinparam sequenceDividerBackgroundColor #yellow

skinparam Arrow {
    Thickness 2
    Color black
    FontColor black
}
skinparam style strictuml

participant "<$ElasticContainerServiceContainer1>\nClient\n application" as senderClient

box "PYMT Domain - E-Transfer Payment Processing" #E1D5E7
  participant "==$AWSImg(ElasticContainerServiceContainer2)\n**pmt-incoming-money-request-api**" as incomingMoneyRequestBusinessApi
end box


box "PYMT Domain - Technical API" #FFE6CC
  participant "//DynamoDB Global Table//\n==$AWSImg(DynamoDB)\nINCOMING_MONEY_REQUEST_TABLE" as db
end box

box "Domaine Sécurité" #LightBlue
participant "IAM-Okta" as IAM
end box

box "PYMT Domain - Party" #E1D5E7
  participant "==$AWSImg(ElasticContainerServiceContainer2)\npmt-partners-party-api" as partyAPI
end box

box "Interac" #E6E6E6
  boundary "Payments REST API" as interac
end box


box "PPTD Domain" #7FFF00

 participant  "Kafka Topic\n==$AWSImg(SimpleNotificationServiceTopic)\n**pptd.prd.tpc.etransfer.pmt.trx.v1**" as pptd

end box

senderClient -> incomingMoneyRequestBusinessApi:  call **Decline Incoming Money Request ** request(**$DeclinePaymentPathParameters**)**<color:blue> (R1)</color>**
incomingMoneyRequestBusinessApi-> incomingMoneyRequestBusinessApi: **Validate** if the request respect the contract **<color:blue>(R2)</color>**
alt Validation Failed
    incomingMoneyRequestBusinessApi--> senderClient : <color:red> **HTTP-400**</color> **<color:red>(ER2a)</color>**
end

ref over incomingMoneyRequestBusinessApi, db
    |||
    **Retrieve Incoming Money Request ****<color:blue>(RA.*)</color>**
    |||
end ref

incomingMoneyRequestBusinessApi-> IAM: GET JWT Token Request (Authorization) [HTTP] **<color:blue>(RB.2)</color>**
incomingMoneyRequestBusinessApi<-- IAM

ref over incomingMoneyRequestBusinessApi, partyAPI
    |||
    **Get the Interac identifier from the Client identifier** **<color:blue>(RB.*)</color>**
    |||
end ref

ref over incomingMoneyRequestBusinessApi, interac
    |||
    **Decline Incoming Payment ****<color:blue>(RC.*)</color>**
    |||
end ref

ref over incomingMoneyRequestBusinessApi, db
    |||
    **Update Incoming Payment ****<color:blue>(RD.*)</color>**
    |||
end ref

ref over incomingMoneyRequestBusinessApi, pptd
    |||
    **RE.* - Publish to PPTD ****<color:blue>(RE.*)</color>**
    |||
end ref

incomingMoneyRequestBusinessApi--> senderClient : <color:green> **HTTP-204**</color>

note over incomingMoneyRequestBusinessApi
    **Operation END**
end note


@enduml