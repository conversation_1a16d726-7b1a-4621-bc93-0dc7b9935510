package ca.bnc.payment.moneyrequest.client.interacmoneyrequest;

import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.exception.RetryableMoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.Error;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_BAD_REQUEST;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_FORBIDDEN;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_SERVICE_UNAVAILABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_TOO_MANY_REQUESTS;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_UNAUTHORIZED;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;

@RequiredArgsConstructor
public class InteracMoneyRequestApiErrorDecoder implements ErrorDecoder {

    private final InteracMoneyRequestApiDecoderErrorsBuilder interacMoneyRequestApiDecoderErrorsBuilder;

    private static final String DECLINE_METHOD_NAME = "declinerequestforpayment";
    private static final String DECLINE_OPERATION_MESSAGE = "DECLINE";
    private static final String GET_OPERATION_MESSAGE = "GET";

    @Override
    public Exception decode(final String methodKey, final Response response) {
        final HttpStatus httpStatus = HttpStatus.valueOf(response.status());
        final Error error = interacMoneyRequestApiDecoderErrorsBuilder.buildErrorFromResponseBody(response);
        return switch (httpStatus) {
            case BAD_REQUEST -> MoneyRequestException.badRequest(error.text(INTERAC_API_BAD_REQUEST + ": " + error.getText()));
            case UNAUTHORIZED -> MoneyRequestException.internalServerError(
                    TECHNICAL_ERROR_CODE,
                    INTERAC_API_UNAUTHORIZED,
                    SERVICE_ORIGIN,
                    NA
            );
            case FORBIDDEN -> MoneyRequestException.internalServerError(
                    TECHNICAL_ERROR_CODE,
                    INTERAC_API_FORBIDDEN,
                    SERVICE_ORIGIN,
                    NA
            );
            case TOO_MANY_REQUESTS -> MoneyRequestException.internalServerError(
                    TECHNICAL_ERROR_CODE,
                    INTERAC_API_TOO_MANY_REQUESTS.formatted(getOperationName(methodKey)),
                    SERVICE_ORIGIN,
                    NA
            );
            case INTERNAL_SERVER_ERROR -> MoneyRequestException.internalServerError(
                    TECHNICAL_ERROR_CODE,
                    INTERAC_API_INTERNAL_SERVER_ERROR,
                    SERVICE_ORIGIN,
                    NA
            );
            case SERVICE_UNAVAILABLE -> new RetryableMoneyRequestException(
                    TECHNICAL_ERROR_CODE,
                    INTERAC_API_SERVICE_UNAVAILABLE,
                    SERVICE_ORIGIN,
                    NA
            );
            case NOT_FOUND -> MoneyRequestException.notFound(error);
            default -> MoneyRequestException.internalServerError(error);
        };
    }

    private String getOperationName(final String methodKey) {
        if (methodKey != null && methodKey.toLowerCase().contains(DECLINE_METHOD_NAME)) {
            return DECLINE_OPERATION_MESSAGE;
        }
        return GET_OPERATION_MESSAGE;
    }
}
