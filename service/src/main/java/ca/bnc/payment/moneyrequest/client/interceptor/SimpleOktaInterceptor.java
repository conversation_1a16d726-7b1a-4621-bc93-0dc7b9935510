package ca.bnc.payment.moneyrequest.client.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.http.HttpHeaders;

public record SimpleOktaInterceptor(TokenProvider tokenProvider) implements RequestInterceptor {
    @Override
    public void apply(final RequestTemplate template) {
        template.header(HttpHeaders.AUTHORIZATION, tokenProvider.getToken());
    }
}
