package ca.bnc.payment.moneyrequest.client.interceptor;

import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import ca.nbc.payment.pmt_security_library.utils.OktaUtil;

import java.util.Objects;

import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static ca.bnc.payment.moneyrequest.constant.Constant.TOKEN_GENERATION_FAILED;

public record ConfigTokenProvider(OktaClientTokenManager oktaClientTokenManager,
                                  String tokenConfigName) implements TokenProvider {
    @Override
    public String getToken() {
        String oktaToken = OktaUtil.getOktaToken(oktaClientTokenManager, tokenConfigName);
        if (Objects.isNull(oktaToken)) {
            throw MoneyRequestException.internalServerError(
                    TECHNICAL_ERROR_CODE,
                    TOKEN_GENERATION_FAILED,
                    SERVICE_ORIGIN,
                    NA
            );
        }

        return oktaToken;
    }
}
