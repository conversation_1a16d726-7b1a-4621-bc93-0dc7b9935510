package ca.bnc.payment.moneyrequest.mapper.interac;

import ca.bnc.payment.moneyrequest.util.ParticipantIdUtil;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class InteracConnectorIdMapper {
    private final ParticipantIdUtil participantIdUtil;
    public String map(final ClientType clientType) {
        return clientType == ClientType.ORGANISATION ? participantIdUtil.getIndirectConnectorIdentifier() : null;
    }
}
