package ca.bnc.payment.moneyrequest.adapter;

import ca.bnc.payment.interac_money_request.generated.model.ChannelIndicator;
import ca.bnc.payment.interac_money_request.generated.model.DeclineRequestForPaymentRequest;
import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.interac_money_request.generated.model.SignatureType;
import ca.bnc.payment.moneyrequest.client.interacmoneyrequest.InteracMoneyRequestApiClient;
import ca.bnc.payment.moneyrequest.exception.RetryableMoneyRequestException;
import feign.RetryableException;
import lombok.RequiredArgsConstructor;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;

import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_UNAVAILABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;

@Component
@RequiredArgsConstructor
public class InteracMoneyRequestAdapter {

    private final InteracMoneyRequestApiClient interacMoneyRequestApiClient;

    @Retryable(retryFor = {RetryableMoneyRequestException.class},
            maxAttemptsExpression = "${providers.interac-money-request.retry.attempts}",
            backoff = @Backoff(delayExpression = "${providers.interac-money-request.retry.backoff}"))
    public IncomingRequestForPaymentResponse getIncomingRequestForPayment(
            final String xEtParticipantId,
            final String xEtParticipantUserId,
            final String xEtIndirectConnectorId,
            final String authorizationHeader,
            final String xEtRequestId,
            final ChannelIndicator xEtChannelIndicator,
            final String xEtApiSignature,
            final SignatureType xEtApiSignatureType,
            final OffsetDateTime xEtTransactionTime,
            final String interacIdPathVariable
    ) {
        try {
            return interacMoneyRequestApiClient.getIncomingRequestForPayment(
                    xEtParticipantId,
                    xEtParticipantUserId,
                    xEtIndirectConnectorId,
                    authorizationHeader,
                    xEtRequestId,
                    xEtChannelIndicator,
                    xEtApiSignature,
                    xEtApiSignatureType,
                    xEtTransactionTime,
                    interacIdPathVariable
            ).getBody();
        } catch (RetryableException retryableException) {
            throw new RetryableMoneyRequestException(
                    TECHNICAL_ERROR_CODE,
                    INTERAC_API_UNAVAILABLE,
                    SERVICE_ORIGIN,
                    NA
            );
        }
    }

    @Retryable(retryFor = {RetryableMoneyRequestException.class},
            maxAttemptsExpression = "${providers.interac-money-request.retry.attempts}",
            backoff = @Backoff(delayExpression = "${providers.interac-money-request.retry.backoff}"))
    public void declineRequestForPayment(
            final String xEtParticipantId,
            final String xEtParticipantUserId,
            final String xEtIndirectConnectorId,
            final String authorizationHeader,
            final String xEtRequestId,
            final ChannelIndicator xEtChannelIndicator,
            final String xEtApiSignature,
            final SignatureType xEtApiSignatureType,
            final OffsetDateTime xEtTransactionTime,
            final String interacIdPathVariable,
            final DeclineRequestForPaymentRequest declineRequestForPaymentRequest
    ) {
        try {
            interacMoneyRequestApiClient.declineRequestForPayment(
                    xEtParticipantId,
                    xEtParticipantUserId,
                    xEtIndirectConnectorId,
                    authorizationHeader,
                    xEtRequestId,
                    xEtChannelIndicator,
                    xEtApiSignature,
                    xEtApiSignatureType,
                    xEtTransactionTime,
                    interacIdPathVariable,
                    declineRequestForPaymentRequest
            );
        } catch (RetryableException retryableException) {
            throw new RetryableMoneyRequestException(
                    TECHNICAL_ERROR_CODE,
                    INTERAC_API_UNAVAILABLE,
                    SERVICE_ORIGIN,
                    NA
            );
        }
    }
}
