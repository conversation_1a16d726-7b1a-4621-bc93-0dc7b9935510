package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.AmountType4Choice;
import ca.bnc.payment.moneyrequest.generated.model.Amount;
import org.springframework.stereotype.Component;

@Component
public class MoneyRequestResponseAmountMapper {

    public Amount map(final AmountType4Choice amount) {
        return new Amount()
                .instructedAmount(amount.getInstructedAmount().getAmount())
                .currency(amount.getInstructedAmount().getCurrency().getValue());
    }
}
