{"sequence": 44, "timestamp": "2025-09-09T21:15:34.459", "dd.trace_id": "0", "dd.span_id": "0", "version": "0.1.13-SNAPSHOT", "application.environment": "local-bnc", "application.name": "pmt-incoming-money-request-api", "logger_name": "ca.nbc.payment.pmt_logging_library.logging.HttpLogging", "thread_name": "http-nio-8080-exec-2", "level": "ERROR", "message": "INCOMING_MONEY_REQUEST", "loggingMessage": "{\"errors\":[{\"code\":\"TECHNICAL_ERROR\",\"text\":\"An unexpected error occurred.\",\"origin\":\"pmt-incoming-money-request-api\",\"rule\":\"NA\"}]}", "error": [{"code": "TECHNICAL_ERROR", "text": "An unexpected error occurred.", "origin": "pmt-incoming-money-request-api", "rule": "NA"}], "duration": 49, "x-channel-type": "WEB", "tracestate": "vendorname1=opaqueValue1", "x-checkpoint-key": "eyJhbGciOiJFUzM4NCJ9.QAAAAAV1ABAAAAAA9M3iDW5aQPS4dPJS6YSBuwJ0ABUAAAAobFslZk96cWl4WUU0P1pSdmM5VwAQdgACAAAAAA.hEjn0CG1C_NjT1gOK3tUwLdlnM5yLd1JYC3KaVv_AxRxZYSTggdPZdPcDXEY44mO9vHNn0YZ6l_6gSwLbl9QAVWZg3FbYglsZ8bOidNh3ACef7q7QE4XtNGxBDD3zMom", "x-client-id": "BD2E5746C6013778F4CD84D4533FAE6CF80C8C6F52C686691562F452D5FAZ0", "provider": {"name": "INCOMING_MONEY_REQUEST", "operation": "GET_MONEY_REQUEST"}, "responseDispatcherType": "REQUEST", "host": "localhost:8080", "context": "SENDING_RESPONSE", "action": "GET", "content-type": "application/vnd.ca.bnc.pmt+json", "connection": "keep-alive", "x-request-id": "3ee01483-2b49-467c-8cde-c48d1a1fd1c0", "uri": "/et_pmt_proc/incoming-money-request/test", "interacMoneyRequestId": "test", "accept": "application/vnd.ca.bnc.pmt+json", "traceparent": "00-80e1afed08e019fc1110464cfa66635c-7a085853722dc6d2-01", "x-client-agent-id": "30BD2E5746C6013778F4CD84D4533FAE6CF80C8C6F52C686691562F452D5FAZ0", "x-channel-id": "8131", "request-start-time": "1757466934404", "http": 500, "requestDispatcherType": "REQUEST", "bncbusinesstraceid": "d3028133-3488-4e92-9496-f331f4be9eb8", "accept-encoding": "gzip, compress, deflate, br", "user-agent": "bruno-runtime/1.29.0"}