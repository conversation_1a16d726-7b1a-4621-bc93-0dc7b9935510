package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.ExternalPaymentTransactionStatus1Code;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.SupplementaryData;
import org.springframework.stereotype.Component;

@Component
public class PptdSupplementaryDataMapper {

    public SupplementaryData map(final MoneyRequestContext moneyRequestContext) {
        return new SupplementaryData()
                .paymentDirection(SupplementaryData.PaymentDirectionEnum.OUT)
                .transactionStatus(ExternalPaymentTransactionStatus1Code.RJCT)
                .interacMoneyRequestId(moneyRequestContext.interacMoneyRequestId())
                .interacUserId(moneyRequestContext.activePartyIdentifier().interacUserId());
    }

}
