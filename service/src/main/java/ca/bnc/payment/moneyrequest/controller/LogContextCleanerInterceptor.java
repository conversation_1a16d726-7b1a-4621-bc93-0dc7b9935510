package ca.bnc.payment.moneyrequest.controller;

import ca.nbc.payment.lib.service.logging.LogContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
@Order  // Lowest precedence; we want to clean-up log context last
@RequiredArgsConstructor
public class LogContextCleanerInterceptor implements HandlerInterceptor {

    private final LogContextHolder logContextHolder;

    @Override
    public void afterCompletion(
            final HttpServletRequest request,
            final HttpServletResponse response,
            final Object handler,
            final Exception ex) {
        // Reset log context of this thread, so there is no cross-thread data leaking
        logContextHolder.reset();
    }

}
