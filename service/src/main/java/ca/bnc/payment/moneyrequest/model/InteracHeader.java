package ca.bnc.payment.moneyrequest.model;

import ca.bnc.payment.interac_money_request.generated.model.ChannelIndicator;
import ca.bnc.payment.interac_money_request.generated.model.SignatureType;
import lombok.Builder;

import java.time.OffsetDateTime;

@Builder
public record InteracHeader(
        String xEtParticipantId,
        String xEtParticipantUserId,
        String xEtIndirectConnectorId,
        String authorization,
        String xEtRequestId,
        ChannelIndicator xEtChannelIndicator,
        String xEtApiSignature,
        SignatureType xEtApiSignatureType,
        OffsetDateTime xEtTransactionTime
) { }
