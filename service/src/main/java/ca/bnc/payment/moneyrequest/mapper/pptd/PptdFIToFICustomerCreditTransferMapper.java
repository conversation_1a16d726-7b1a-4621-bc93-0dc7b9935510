package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.FIToFICustomerCreditTransfer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class PptdFIToFICustomerCreditTransferMapper {

    private final PptdGroupHeaderMapper pptdGroupHeaderMapper;
    private final PptdCreditTransferTransactionMapper pptdCreditTransferTransactionMapper;

    public FIToFICustomerCreditTransfer map(
            final DeclineRequest declineRequest,
            final IncomingMoneyRequestEntity incomingMoneyRequestEntity
    ) {
        final CreditTransferTransaction39 creditTransferTransaction39 = pptdCreditTransferTransactionMapper.map(
                declineRequest, incomingMoneyRequestEntity
        );
        return new FIToFICustomerCreditTransfer()
                .groupHeader(pptdGroupHeaderMapper.map(declineRequest))
                .creditTransferTransactionInformation(List.of(creditTransferTransaction39));
    }

}
