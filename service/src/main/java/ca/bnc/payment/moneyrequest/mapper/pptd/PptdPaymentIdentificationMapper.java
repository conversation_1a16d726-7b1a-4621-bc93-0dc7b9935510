package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.PaymentIdentification7;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class PptdPaymentIdentificationMapper {

    public PaymentIdentification7 map(
            final ca.bnc.payment.moneyrequest.generated.model.PaymentIdentification7 paymentIdentification
    ) {
        return new PaymentIdentification7()
                .instructionIdentification(paymentIdentification.getInstructionIdentification())
                .endToEndIdentification(paymentIdentification.getEndToEndIdentification());
    }
}
