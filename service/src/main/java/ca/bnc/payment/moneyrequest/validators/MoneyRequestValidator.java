package ca.bnc.payment.moneyrequest.validators;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.MoneyRequestStatus;
import org.springframework.stereotype.Component;

import java.util.Set;

import static ca.bnc.payment.moneyrequest.constant.Constant.CANNOT_DUE_REQUEST_STATUS;
import static ca.bnc.payment.moneyrequest.constant.Constant.INVALID_MONEY_REQUEST_STATUS;
import static ca.bnc.payment.moneyrequest.constant.Constant.INVALID_REQUEST_STATUS_CODE;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static ca.bnc.payment.moneyrequest.generated.model.MoneyRequestStatus.AVAILABLE_TO_BE_FULFILLED;
import static ca.bnc.payment.moneyrequest.generated.model.MoneyRequestStatus.INITIATED;

@Component
public class MoneyRequestValidator {

    private static final Set<MoneyRequestStatus> VALID_MONEY_REQUEST_STATUS = Set.of(
            INITIATED,
            AVAILABLE_TO_BE_FULFILLED
    );

    public void validate(final IncomingMoneyRequestEntity incomingMoneyRequestEntity) {
        try {
            if (incomingMoneyRequestEntity != null && !hasValidStatus(incomingMoneyRequestEntity)) {
                throw MoneyRequestException.badRequest(INVALID_REQUEST_STATUS_CODE, CANNOT_DUE_REQUEST_STATUS, SERVICE_ORIGIN, NA);
            }
        } catch (IllegalArgumentException e) {
            throw MoneyRequestException.internalServerError(TECHNICAL_ERROR_CODE, INVALID_MONEY_REQUEST_STATUS, SERVICE_ORIGIN, NA);
        }
    }

    public void checkIfEmptyAndValidate(final IncomingMoneyRequestEntity incomingMoneyRequestEntity) {
        validateNotEmpty(incomingMoneyRequestEntity);
        validate(incomingMoneyRequestEntity);
    }

    private void validateNotEmpty(final IncomingMoneyRequestEntity incomingMoneyRequestEntity) {
        if (incomingMoneyRequestEntity == null) {
            throw MoneyRequestException.notFound(null);
        }
    }

    private static boolean hasValidStatus(final IncomingMoneyRequestEntity incomingMoneyRequestEntity) {
        return VALID_MONEY_REQUEST_STATUS.contains(MoneyRequestStatus.fromValue(incomingMoneyRequestEntity.getMoneyRequestStatus()));
    }
}
