package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.util.IdGenerator;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.ChannelType;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEvent;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.Entity;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.EventType;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.PaymentTypeCode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class PptdMapper {

    private final PptdRawDataMapper pptdRawDataMapper;
    private final TimeService timeService;
    private final IdGenerator idGenerator;

    private static final String VERSION = "1.0.0";
    private static final String MESSAGE_DEFINITION_IDENTIFIER = "pacs.008.001.08";

    public DomesticETransferEvent map(
            final DeclineRequest declineRequest,
            final MoneyRequestContext moneyRequestContext,
            final IncomingMoneyRequestEntity incomingMoneyRequestEntity
    ) {
        final String instructionIdentification = idGenerator.generateInstructionIdentification();
        return new DomesticETransferEvent()
                .version(VERSION)
                .entity(Entity.DOMESTIC_ETRANSFER_PAYMENT)
                .eventType(EventType.PAYMENT_TRANSACTION_DATA_EXPORTED)
                .eventTime(timeService.getNowOffsetDateTime())
                .instructionIdentification(instructionIdentification)
                .endToEndBusinessIdentification(incomingMoneyRequestEntity.getEndToEndIdentification())
                .channelType(mapChannelType(moneyRequestContext.channelType()))
                .channelId(incomingMoneyRequestEntity.getChannelId())
                .clientId(moneyRequestContext.clientId())
                .paymentTypeCode(PaymentTypeCode.FUL)
                .msgDefIdr(MESSAGE_DEFINITION_IDENTIFIER)
                .rawData(
                        pptdRawDataMapper.map(
                                moneyRequestContext,
                                instructionIdentification,
                                incomingMoneyRequestEntity.getEndToEndIdentification(),
                                declineRequest,
                                incomingMoneyRequestEntity
                        )
                );
    }

    private ChannelType mapChannelType(
            final ca.bnc.payment.moneyrequest.generated.model.ChannelType channelType
    ) {
        return channelType == ca.bnc.payment.moneyrequest.generated.model.ChannelType.WEB ? ChannelType.WEB : ChannelType.MOBILE;
    }

}
