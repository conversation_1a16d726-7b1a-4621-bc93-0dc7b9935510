package ca.bnc.payment.moneyrequest.client.interceptor;

import ca.bnc.payment.moneyrequest.util.ChannelIdUtil;
import ca.bnc.payment.moneyrequest.util.ParticipantIdUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;

import java.util.Collection;
import java.util.Optional;
import java.util.stream.Stream;

public final class BncOktaInterceptor implements RequestInterceptor {

    private static final String X_CHANNEL_ID = "x-channel-id";
    private final RequestInterceptor delegate;
    private final ParticipantIdUtil participantIdUtil;
    private final ChannelIdUtil channelIdUtil;

    public BncOktaInterceptor(final RequestInterceptor delegate,
                              final ParticipantIdUtil participantIdUtil,
                              final ChannelIdUtil channelIdUtil) {
        this.delegate = delegate;
        this.participantIdUtil = participantIdUtil;
        this.channelIdUtil = channelIdUtil;
    }

    @Override
    public void apply(final RequestTemplate template) {
        String channelId = getChannelId(template);
        if (isEligible(channelId)) {
            delegate.apply(template);
        }
    }

    private static String getChannelId(final RequestTemplate template) {
        return Optional.ofNullable(template.headers().get(X_CHANNEL_ID))
                .map(Collection::stream)
                .flatMap(Stream::findFirst)
                .orElse(null);
    }

    private boolean isEligible(final String channelId) {
        return participantIdUtil.isBncParticipant()
                && channelIdUtil.isBncChannelId(channelId);
    }
}
