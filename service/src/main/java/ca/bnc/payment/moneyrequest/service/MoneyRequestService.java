package ca.bnc.payment.moneyrequest.service;

import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.moneyrequest.adapter.async.producer.AsyncPptdPublisher;
import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.facade.InteracMoneyRequestFacade;
import ca.bnc.payment.moneyrequest.facade.PartyFacade;
import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.generated.model.DomesticFulfillmentMoneyRequest;
import ca.bnc.payment.moneyrequest.mapper.entity.MoneyRequestEntityMapper;
import ca.bnc.payment.moneyrequest.mapper.pptd.PptdMapper;
import ca.bnc.payment.moneyrequest.mapper.response.MoneyRequestResponseMapper;
import ca.bnc.payment.moneyrequest.model.ActivePartyIdentifier;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.repository.IncomingMoneyRequestRepository;
import ca.bnc.payment.moneyrequest.validators.MoneyRequestValidator;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class MoneyRequestService {

    private final PartyFacade partyFacade;
    private final InteracMoneyRequestFacade interacMoneyRequestFacade;
    private final MoneyRequestEntityMapper moneyRequestEntityMapper;
    private final IncomingMoneyRequestRepository incomingMoneyRequestRepository;
    private final MoneyRequestValidator moneyRequestValidator;
    private final MoneyRequestResponseMapper moneyRequestResponseMapper;
    private final PptdMapper pptdMapper;
    private final AsyncPptdPublisher asyncPptdPublisher;
    private final LogContextHolder logContextHolder;

    public DomesticFulfillmentMoneyRequest processGetIncomingMoneyRequest(
            final String channelId,
            final ChannelType channelType,
            final UUID requestId,
            final String clientId,
            final String interacMoneyRequestId
    ) {
        moneyRequestValidator.validate(incomingMoneyRequestRepository.retrieve(interacMoneyRequestId));
        log.info("Processing Get Incoming Money Request for request ID {}", requestId);

        final ActivePartyIdentifier activeInteracIdentifier = partyFacade.fetchActiveInteracIdentifier(channelId, requestId, clientId);

        final MoneyRequestContext moneyRequestContext = new MoneyRequestContext(
                channelId,
                channelType,
                requestId,
                clientId,
                interacMoneyRequestId,
                activeInteracIdentifier
        );

        final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse =
                interacMoneyRequestFacade.getIncomingRequestForPayment(moneyRequestContext);

        final IncomingMoneyRequestEntity incomingMoneyRequestEntity =
                moneyRequestEntityMapper.mapForGetMoneyRequest(incomingRequestForPaymentResponse, moneyRequestContext);

        incomingMoneyRequestRepository.save(incomingMoneyRequestEntity);

        return moneyRequestResponseMapper.map(incomingRequestForPaymentResponse);
    }

    public void processDeclineIncomingMoneyRequest(
            final String channelId,
            final ChannelType channelType,
            final UUID requestId,
            final String clientId,
            final String interacMoneyRequestId,
            final DeclineRequest declineRequest
    ) {
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = incomingMoneyRequestRepository.retrieve(interacMoneyRequestId);
        moneyRequestValidator.checkIfEmptyAndValidate(incomingMoneyRequestEntity);
        final ActivePartyIdentifier activeInteracIdentifier = partyFacade.fetchActiveInteracIdentifier(channelId, requestId, clientId);
        final MoneyRequestContext moneyRequestContext = new MoneyRequestContext(
                channelId,
                channelType,
                requestId,
                clientId,
                interacMoneyRequestId,
                activeInteracIdentifier
        );
        final IncomingMoneyRequestEntity updatedMoneyRequestEntity = moneyRequestEntityMapper.updateForDeclineMoneyRequest(
                declineRequest,
                incomingMoneyRequestEntity
        );
        interacMoneyRequestFacade.declineRequestForPayment(moneyRequestContext, declineRequest);
        incomingMoneyRequestRepository.save(updatedMoneyRequestEntity);
        asyncPptdPublisher.sendMessage(pptdMapper.map(declineRequest, moneyRequestContext, updatedMoneyRequestEntity),
                moneyRequestContext.requestId(),
                logContextHolder.copyContext());
    }

}
