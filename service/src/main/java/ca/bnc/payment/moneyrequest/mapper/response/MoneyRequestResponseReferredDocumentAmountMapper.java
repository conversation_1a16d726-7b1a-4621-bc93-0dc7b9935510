package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.ActiveOrHistoricCurrencyAndAmount;
import ca.bnc.payment.interac_money_request.generated.model.CreditDebitCode;
import ca.bnc.payment.moneyrequest.generated.model.DocumentAdjustment1;
import ca.bnc.payment.moneyrequest.generated.model.RemittanceAmount2;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class MoneyRequestResponseReferredDocumentAmountMapper {

    public RemittanceAmount2 map(
            final ca.bnc.payment.interac_money_request.generated.model.RemittanceAmount2 remittanceAmount
    ) {
        final List<ca.bnc.payment.interac_money_request.generated.model.DocumentAdjustment1> documentAdjustment
                = remittanceAmount.getAdjustmentAmountAndReason();
        return new RemittanceAmount2()
                .duePayableAmount(
                        Optional.ofNullable(remittanceAmount.getDuePayableAmount())
                                .map(ActiveOrHistoricCurrencyAndAmount::getAmount)
                                .orElse(null)
                )
                .remittedAmount(
                        Optional.ofNullable(remittanceAmount.getRemittedAmount())
                                .map(ActiveOrHistoricCurrencyAndAmount::getAmount)
                                .orElse(null)
                )
                .adjustmentAmountAndReason(
                        Optional.ofNullable(documentAdjustment)
                                .filter(list -> !list.isEmpty())
                                .map(list -> list.get(0))
                                .map(this::mapAdjustmentAmountAndReason)
                                .orElse(null)
                );
    }

    private DocumentAdjustment1 mapAdjustmentAmountAndReason(
            final ca.bnc.payment.interac_money_request.generated.model.DocumentAdjustment1 documentAdjustment
    ) {
        return new DocumentAdjustment1()
                .amount(documentAdjustment.getAmount().getAmount())
                .reason(documentAdjustment.getReason())
                .additionalInformation(documentAdjustment.getAdditionalInformation())
                .creditDebitIndicator(mapCreditDebitIndicator(documentAdjustment.getCreditDebitIndicator()));
    }

    private DocumentAdjustment1.CreditDebitIndicatorEnum mapCreditDebitIndicator(final CreditDebitCode creditDebitIndicator) {
        if (creditDebitIndicator == null) {
            return null;
        }
        return switch (creditDebitIndicator) {
            case CRDT -> DocumentAdjustment1.CreditDebitIndicatorEnum.CRDT;
            case DBIT -> DocumentAdjustment1.CreditDebitIndicatorEnum.DBIT;
        };
    }

}
