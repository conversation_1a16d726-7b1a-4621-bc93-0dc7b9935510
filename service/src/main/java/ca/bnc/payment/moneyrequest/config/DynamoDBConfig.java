package ca.bnc.payment.moneyrequest.config;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.nbc.payment.dynamodb.DynamoDBClientManager;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

@Configuration
@RequiredArgsConstructor
public class DynamoDBConfig {

    private final DynamoDBClientManager dynamoDBClientManager;

    @Bean
    public DynamoDbEnhancedClient dynamoDbEnhancedClient() {
        return dynamoDBClientManager.getEnhancedClient("money-request");
    }

    @Bean
    public DynamoDbTable<IncomingMoneyRequestEntity> dynamoDBTable(
            final DynamoDbEnhancedClient dynamoDbEnhancedClient,
            final @Value("${dynamodb.tables.money-request.tableName}") String tableName) {
        TableSchema<IncomingMoneyRequestEntity> tableSchema = TableSchema.fromBean(IncomingMoneyRequestEntity.class);
        return dynamoDbEnhancedClient.table(tableName, tableSchema);
    }

}
