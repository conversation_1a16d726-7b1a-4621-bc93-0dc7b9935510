package ca.bnc.payment.moneyrequest.util;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

@Component
@RequiredArgsConstructor
public class TimeService {

    private static final DateTimeFormatter ISO_8601_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    private final Clock clock;

    public OffsetDateTime getNowOffsetDateTime() {
        return OffsetDateTime.now(clock);
    }

    public LocalDateTime getNowLocalDateTime() {
        return LocalDateTime.now(clock);
    }

    public String formatLocalDateTime(final LocalDateTime date) {
        return date.format(ISO_8601_FORMATTER);
    }

    public String getFormattedNowLocalDateTime() {
        final LocalDateTime now = getNowLocalDateTime();
        return formatLocalDateTime(now);
    }

    public OffsetDateTime parseToOffsetDateTime(final String iso8601DateTimeString) {
        final Instant instant = Instant.parse(iso8601DateTimeString);
        return OffsetDateTime.ofInstant(instant, ZoneOffset.UTC);
    }

    public LocalDate parseToLocalDate(final String iso8601DateTimeString) {
        final Instant instant = Instant.parse(iso8601DateTimeString);
        return instant.atZone(ZoneOffset.UTC).toLocalDate();
    }

}
