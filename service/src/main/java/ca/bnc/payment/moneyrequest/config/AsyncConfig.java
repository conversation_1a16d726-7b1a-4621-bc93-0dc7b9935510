package ca.bnc.payment.moneyrequest.config;

import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Setter
@EnableAsync
@Configuration
@ConfigurationProperties(prefix = "application.async.thread.pool")
public class AsyncConfig {

    private int coreSize;
    private int maxSize;
    private int awaitTerminationSeconds;

    @Bean(name = "workerThreadExecutor")
    public ThreadPoolTaskExecutor workerThreadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setThreadNamePrefix("WorkerThread-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        executor.initialize();
        return executor;
    }

}
