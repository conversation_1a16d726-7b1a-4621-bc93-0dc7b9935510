package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.CreditorReferenceInformation2;
import ca.bnc.payment.moneyrequest.generated.model.CreditorReferenceType1Choice;
import ca.bnc.payment.moneyrequest.generated.model.CreditorReferenceType2;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class MoneyRequestResponseCreditorReferenceInformationMapper {

    public CreditorReferenceInformation2 map(
            final ca.bnc.payment.interac_money_request.generated.model.CreditorReferenceInformation2 creditorReferenceInformation
    ) {
        return new CreditorReferenceInformation2()
                .reference(creditorReferenceInformation.getReference())
                .type(Optional.ofNullable(creditorReferenceInformation.getType())
                        .map(this::mapCreditorReferenceType)
                        .orElse(null));
    }

    private CreditorReferenceType2 mapCreditorReferenceType(
            final ca.bnc.payment.interac_money_request.generated.model.CreditorReferenceType2 creditorReferenceType
    ) {
        return new CreditorReferenceType2().codeOrProprietary(
                new CreditorReferenceType1Choice().code(
                        CreditorReferenceType1Choice.CodeEnum.fromValue(creditorReferenceType.getCodeOrProprietary().getCode().getValue())
                )
        );
    }


}
