package ca.bnc.payment.moneyrequest.exception;

import ca.bnc.payment.moneyrequest.generated.model.Error;


public final class RetryableMoneyRequestException extends MoneyRequestException {
    public RetryableMoneyRequestException(final String code, final String text, final String origin, final String rule) {
        super(ErrorInfo.internalServerError(new Error().code(code).text(text).origin(origin).rule(rule)));
    }
}
