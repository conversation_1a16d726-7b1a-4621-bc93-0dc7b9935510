package ca.bnc.payment.moneyrequest.controller.advice;

import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.Error;
import ca.bnc.payment.moneyrequest.generated.model.Errors;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.Optional;

import static ca.bnc.payment.moneyrequest.constant.Constant.GENERIC_ERROR_LOG;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERNAL_SERVER_ERROR_GENERIC;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.REQUEST_INVALID_CODE;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;

@ControllerAdvice
@RequiredArgsConstructor
public class MoneyRequestControllerAdvice {

    private final Logger logger = LoggerFactory.getLogger(this.getClass().getName());
    private final LoggingFacade loggingFacade;

    @ExceptionHandler(MoneyRequestException.class)
    public ResponseEntity<Errors> handleIncomingMoneyRequestException(final MoneyRequestException moneyRequestException) {
        if (moneyRequestException.getErrorInfo().error() == null) {
            logError(moneyRequestException);
            return buildResponseEntity(null, moneyRequestException.getErrorInfo().status());
        }

        logError(moneyRequestException.getErrorInfo().error(), moneyRequestException);
        return buildResponseEntity(moneyRequestException.getErrorInfo().error(), moneyRequestException.getErrorInfo().status());
    }

    @ExceptionHandler(MissingRequestHeaderException.class)
    public ResponseEntity<Errors> handleMissingRequestHeaderException(final MissingRequestHeaderException e) {
        Error error = new Error().code(REQUEST_INVALID_CODE).text(e.getMessage()).origin(SERVICE_ORIGIN).rule(NA);
        logError(error, e);
        return new ResponseEntity<>(new Errors().addErrorsItem(error), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<Errors> handleMethodArgumentTypeMismatchException(final MethodArgumentTypeMismatchException e) {
        Error error = new Error()
                .code(REQUEST_INVALID_CODE)
                .text("Mismatch value %s for %s".formatted(e.getValue(), e.getName()))
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        logError(error, e);
        return new ResponseEntity<>(new Errors().addErrorsItem(error), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Errors> handleMethodArgumentNotValidException(final MethodArgumentNotValidException e) {
        String message = Optional.ofNullable(e.getFieldError())
                .map(fieldError -> String.format("The %s %s", fieldError.getField(), fieldError.getDefaultMessage()))
                .orElseGet(e::getMessage);

        Error error = new Error()
                .code(REQUEST_INVALID_CODE)
                .text(message)
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        logError(error, e);
        return new ResponseEntity<>(new Errors().addErrorsItem(error), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(HttpMediaTypeNotAcceptableException.class)
    public ResponseEntity<Errors> handleHttpMediaTypeNotAcceptableException(final HttpMediaTypeNotAcceptableException e) {

        String message = Optional.ofNullable(e.getBody().getDetail())
                .map(String::new)
                .orElse("Wrong Content-Type or Accept Header");

        Error error = new Error()
                .code(REQUEST_INVALID_CODE)
                .text(message)
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        logError(error, e);
        return new ResponseEntity<>(new Errors().addErrorsItem(error), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<Errors> handleHttpMessageNotReadableException(final HttpMessageNotReadableException e) {
        Error error = new Error()
                .code(REQUEST_INVALID_CODE)
                .text(e.getMessage())
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        logError(error, e);
        return new ResponseEntity<>(new Errors().addErrorsItem(error), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Errors> handleOtherException(final Exception e) {
        Error error = new Error().code(TECHNICAL_ERROR_CODE).text(INTERNAL_SERVER_ERROR_GENERIC).origin(SERVICE_ORIGIN).rule(NA);
        logError(error, e);
        return new ResponseEntity<>(new Errors().addErrorsItem(error), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private void logError(final Error error, final Exception e) {
        String errorMessage = String.format(GENERIC_ERROR_LOG, error.getCode(), error.getText(), error.getOrigin(), error.getRule());
        loggingFacade.error(logger, errorMessage, e);
    }

    private ResponseEntity<Errors> buildResponseEntity(final Error error, final HttpStatusCode status) {
     return  Optional.ofNullable(error)
                .map(err -> ResponseEntity.status(status)
                        .body(new Errors().addErrorsItem(err)))
                .orElseGet(() -> ResponseEntity.status(status).build());

    }

    private void logError(final Exception e) {
        loggingFacade.error(logger, e);
    }

}
