package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.Document12;
import ca.bnc.payment.moneyrequest.generated.model.Invoice;
import ca.bnc.payment.moneyrequest.generated.model.Type;
import org.springframework.stereotype.Component;

@Component
public class MoneyRequestResponseInvoiceMapper {

    public Invoice map(final Document12 invoice) {
        return new Invoice()
                .issueDate(invoice.getIssueDate())
                .type(mapInvoiceType(invoice));
    }

    private Type mapInvoiceType(final Document12 invoice) {
        return new Type()
                .identification(invoice.getIdentification())
                .code(Type.CodeEnum.fromValue(invoice.getType().getCode().getValue()));
    }

}
