package ca.bnc.payment.moneyrequest.client.party;

import ca.bnc.payment.moneyrequest.generated.model.Error;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ca.nbc.payment.pmtpartnersparty.model.Errors;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Collections;
import java.util.Optional;

import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_API_GENERIC_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;

@Component
@AllArgsConstructor
public class PartyApiDecoderErrorsBuilder {

    private static final Logger LOGGER = LoggerFactory.getLogger(PartyApiDecoderErrorsBuilder.class.getName());

    private static final Error DEFAULT_ERROR = new Error()
            .code(TECHNICAL_ERROR_CODE)
            .text(PARTY_API_GENERIC_ERROR)
            .origin(PARTY_SERVICE_ORIGIN)
            .rule(NA);

    private final ObjectMapper objectMapper;
    private final LoggingFacade loggingFacade;

    public Error buildErrorFromResponseBody(final Response response) {
        return Optional.ofNullable(response.body())
                .map(this::getError)
                .orElse(DEFAULT_ERROR);
    }

    private Error getError(final Response.Body body) {
        try (InputStream inputStream = body.asInputStream()) {
            final Errors partyApiErrors = objectMapper.readValue(inputStream, Errors.class);
            return Optional.ofNullable(partyApiErrors.getErrors())
                    .orElse(Collections.emptyList())
                    .stream()
                    .findFirst()
                    .map(partyApiError ->
                            new Error()
                                    .code(partyApiError.getCode())
                                    .text(partyApiError.getText())
                                    .origin(partyApiError.getOrigin())
                                    .rule(partyApiError.getRule())
                    ).orElse(DEFAULT_ERROR);
        } catch (final Exception exception) {
            loggingFacade.error(LOGGER, "An error occurred while parsing error response from the Party API", exception);
            return null;
        }
    }

}
