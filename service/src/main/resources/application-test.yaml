app:
  name: pmt-incoming-money-request-api
applicationmanagement:
  thresholdheapinpercent: 95
application:
  async:
    thread:
      pool:
        awaitTerminationSeconds: 60
        coreSize: 10
        maxSize: 10
  directParticipantIdentifier: CA000006
  indirectConnectorId: 201720189
dynamodb:
  tables:
    money-request:
      endpoint: http://localhost:8001
      region: ca-central-1
      tableName: INCOMING_MONEY_REQUEST_TABLE
interac-security:
  enabled: false
  keystore-alias: "etapi-sign-key-ca"
  keystore-password: "password"
kafka:
  bootstrap-servers: localhost:9092
  password: ''
  retry-template:
    backoffPeriod: 200
    maxAttempts: 3
  sasl-mechanism: PLAIN
  security-protocol: PLAINTEXT
  topics:
    pptd: payments-dev-pmt-pptd-tpc.etrf.trx.v1.0
  username: ''
logging:
  level:
    ca.bnc.payment: DEBUG
okta:
  enabled: true
  jwks:
    authorizationServerId: "fake_auth_server_id"
    clientId: "clientID"
    privateKey: "privateKey"
    url: http://localhost:13001
providers:
  interac-money-request:
    retry:
      attempts: 3
      backoff: 100
    url: http://localhost:8110
  party:
    retry:
      attempts: 3
      backoff: 100
    url: http://localhost:8110
spring:
  cloud:
    openfeign:
      client:
        config:
          interacMoneyRequestApiClient:
            connectTimeout: 2000
            readTimeout: 2000
          partyApiClient:
            connectTimeout: 2000
            readTimeout: 2000
      httpclient:
        disableSslValidation: true