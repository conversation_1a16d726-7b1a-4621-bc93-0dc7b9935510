app:
  name: ${APP_NAME}
applicationmanagement:
  thresholdheapinpercent: ${THRESHOLD_HEAP_PERCENT}
application:
  async:
    thread:
      pool:
        awaitTerminationSeconds: ${APPLICATION_ASYNC_THREAD_POOL_AWAIT_TERMINATION_SECONDS}
        coreSize: ${APPLICATION_ASYNC_THREAD_POOL_CORE_SIZE}
        maxSize: ${APPLICATION_ASYNC_THREAD_POOL_MAX_SIZE}
  directParticipantIdentifier: ${APPLICATION_DIRECTPARTICIPANTIDENTIFIER}
  indirectConnectorId: ${INDIRECT_CONNECTOR_ID}
dynamodb:
  health:
    enabled: true
  tables:
    money-request:
      region: ${DYNAMO_DB_REGION}
      retryPolicy: NONE
      tableName: ${DYNAMO_DB_TABLE_NAME}
      timeout: 3000
health:
  config:
    enabled: true
interac-security:
  enabled: ${INTERAC_SIGNING_ENABLED}
  keystore-alias: "${INTERAC_SIGNING_KEY_ALIAS}"
  keystore-password: "${INTERAC_SIGNING_KEYSTORE_PASSWORD}"
  keystore-path: "/mnt/certs/keystore/interac-keystore.jks"
  timeToLive: 30000
kafka:
  bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS}
  password: ${KAFKA_PASSWORD}
  retry-template:
    backoffPeriod: ${KAFKA_RETRY_BACKOFF_PERIOD}
    maxAttempts: ${KAFKA_RETRY_MAX_ATTEMPTS}
  sasl-mechanism: ${KAFKA_SASL_MECHANISM}
  security-protocol: ${KAFKA_SECURITY_PROTOCOL}
  topics:
    pptd: ${KAFKA_PPTD_TOPIC}
  username: ${KAFKA_USERNAME}
logging:
  level:
    ca.bnc.payment: ${APP_LOG_LEVEL}
    org.springframework.web: INFO
    org.springframework: INFO
management:
  endpoint:
    health:
      group:
        liveness:
          include: heapIndicator, ping
          show-details: always
        readiness:
          include: ping
          show-details: always
      show-details: always
  health:
    defaults:
      enabled: true
    mapping:
    show-details: always
    web:
      exposure:
        include: "*"
  server:
    port: 9999
providers:
  interac-money-request:
    retry:
      attempts: ${INTERACMONEYREQUEST_RETRY_ATTEMPTS}
      backoff: ${INTERACMONEYREQUEST_RETRY_BACKOFF}
    url: ${INTERACMONEYREQUEST_URL}
  party:
    retry:
      attempts: ${PARTY_RETRY_ATTEMPTS}
      backoff: ${PARTY_RETRY_BACKOFF}
    url: ${PARTY_URL}
server:
  port: 8080
spring:
  application:
    name: pmt-incoming-money-request-api
    version: "@project.version@"
  cloud:
    openfeign:
      client:
        config:
          interacMoneyRequestApiClient:
            connectTimeout: ${INTERACMONEYREQUEST_CONNECTION_TIMEOUT}
            loggerLevel: full
            readTimeout: ${INTERACMONEYREQUEST_READ_TIMEOUT}
          partyApiClient:
            connectTimeout: ${PARTY_CONNECTION_TIMEOUT}
            loggerLevel: full
            readTimeout: ${PARTY_READ_TIMEOUT}
  jackson:
    deserialization:
      ADJUST_DATES_TO_CONTEXT_TIME_ZONE: false
    serialization:
      WRITE_DATES_WITH_ZONE_ID: true
    time-zone: UTC

  profiles:
    include: ${SPRING_PROFILE_INCLUDE}