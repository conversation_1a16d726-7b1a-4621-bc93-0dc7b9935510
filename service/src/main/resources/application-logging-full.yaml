logging:
  logDomain: INCOMING_MONEY_REQUEST
  config:
    api:
      ["GET::/et_pmt_proc/incoming-money-request/{interacMoneyRequestId}"]:
        provider:
          name: INCOMING_MONEY_REQUEST
          operation: GET_MONEY_REQUEST
        request:
          body: true
          allHeaders: true
          allPathVariables: true
        response:
          body: true
          allHeaders: true
          allPathVariables: true
      ["POST::/et_pmt_proc/incoming-money-request/{interacMoneyRequestId}/decline"]:
        provider:
          name: INCOMING_MONEY_REQUEST
          operation: DECLINE_MONEY_REQUEST
        request:
          body: true
          allHeaders: true
          allPathVariables: true
        response:
          body: true
          allHeaders: true
          allPathVariables: true
      ["PROVIDER:GET::/requests/{id}/incoming"]:
        hostname: "${providers.interac-money-request.url}"
        provider:
          name: GET_MONEY_REQUEST
          operation: GETINCOMINGREQUESTFORPAYMENT
        request:
          allPathVariables: true
          allHeaders: true
          body: true
        response:
          allHeaders: true
          body: true
      ["PROVIDER:POST::/requests/{id}/decline"]:
        hostname: "${providers.interac-money-request.url}"
        provider:
          name: POST_MONEY_REQUEST
          operation: DECLINEREQUESTFORPAYMENT
        request:
          allPathVariables: true
          allHeaders: true
          body: true
        response:
          allPathVariables: true
          allHeaders: true
          body: true
      ["PROVIDER:GET::/partners-party/{clientId}/identifiers"]:
        hostname: "${providers.party.url}"
        provider:
          name: GET_PARTY
          operation: GETPARTYIDENTIFIERBYID
        request:
          allPathVariables: true
          allHeaders: true
          allQueryVariables: true
          body: true
        response:
          allPathVariables: true
          allHeaders: true
          body: true
    messaging:
      ["PPTD"]:
        provider:
          name: PPTD
          operation: PUBLISH
        request:
          body: true
          allHeaders: true