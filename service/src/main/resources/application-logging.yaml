logging:
  logDomain: INCOMING_MONEY_REQUEST
  config:
    api:
      ["GET::/et_pmt_proc/incoming-money-request/{interacMoneyRequestId}"]:
        provider:
          name: INCOMING_MONEY_REQUEST
          operation: GET_MONEY_REQUEST
        request:
          headers:
            - x-channel-id
            - x-channel-type
            - x-client-id
            - x-request-id
            - traceparent
            - bncbusinesstraceid
          allPathVariables: true
          fieldsToResponse:
            - interacMoneyRequestId
            - bncbusinesstraceid
            - x-client-id
        response:
          allHeaders: true
          fields:
            - creditorPaymentActivationRequest>groupHeader>messageIdentification
            - creditorPaymentActivationRequest>paymentInformation>moneyRequestStatus
      ["POST::/et_pmt_proc/incoming-money-request/{interacMoneyRequestId}/decline"]:
        provider:
          name: INCOMING_MONEY_REQUEST
          operation: DECLINE_MONEY_REQUEST
        request:
          headers:
            - x-channel-id
            - x-channel-type
            - x-client-id
            - x-request-id
            - traceparent
            - bncbusinesstraceid
          allPathVariables: true
          fieldsToResponse:
            - interacMoneyRequestId
            - bncbusinesstraceid
            - x-client-id
          fields:
            - FIToFICustomerCreditTransferV08>groupHeader>messageIdentification
            - FIToFICustomerCreditTransferV08>creditTransferTransactionInformation>paymentIdentification>endToEndIdentification
            - FIToFICustomerCreditTransferV08>creditTransferTransactionInformation>supplementaryData>declineReason
        response:
          allHeaders: true
      ["PROVIDER:GET::/requests/{id}/incoming"]:
        hostname: "${providers.interac-money-request.url}"
        provider:
          name: GET_MONEY_REQUEST
          operation: GETINCOMINGREQUESTFORPAYMENT
        request:
          allPathVariables: true
          headers:
            - x-et-participant-id
            - x-et-participant-user-id
            - x-request-id
            - x-et-channel-indicator
            - traceparent
            - bncbusinesstraceid
        response:
          allHeaders: true
          fields:
            - creditor_payment_activation_request>group_header>message_identification
            - creditor_payment_activation_request>group_header>initiatingParty>name
            - creditor_payment_activation_request>paymentInformation>categoryPurpose>code
            - creditor_payment_activation_request>paymentInformation>moneyRequestStatus
            - creditor_payment_activation_request>paymentInformation>debitor>name
            - creditor_payment_activation_request>paymentInformation>creditTransferTransaction>endToEndIdentification
      ["PROVIDER:POST::/requests/{id}/decline"]:
        hostname: "${providers.interac-money-request.url}"
        provider:
          name: POST_MONEY_REQUEST
          operation: DECLINEREQUESTFORPAYMENT
        request:
          allPathVariables: true
          headers:
            - x-et-participant-id
            - x-et-participant-user-id
            - x-request-id
            - x-et-channel-indicator
          fields:
            - decline_reason
        response:
          allHeaders: true
      ["PROVIDER:GET::/partners-party/{clientId}/identifiers"]:
        hostname: "${providers.party.url}"
        provider:
          name: GET_PARTY
          operation: GETPARTYIDENTIFIERBYID
        request:
          allPathVariables: true
          allQueryVariables: true
          headers:
            - traceparent
            - accept-version
            - x-channel-id
            - x-request-id
            - x-client-type
        response:
          fields:
            - merger>clientId
            - merger>clientType
            - identifiers>clientId
            - identifiers>value
            - identifiers>type
            - identifiers>status
    messaging:
      ["PPTD"]:
        provider:
          name: PPTD
          operation: PUBLISH
        request:
          fields:
            - endToEndBusinessIdentification
            - channelId
            - channelType
            - clientType**
            - paymentTypeCode
            - msgDefIdr
            - rawData>approvalRequired
            - rawData>supplementaryData>paymentDirection
            - rawData>supplementaryData>transactionStatus
            - rawData>supplementaryData>eTransferId*
            - rawData>supplementaryData>interacUserId
          headers:
            - kafka_topic
            - id