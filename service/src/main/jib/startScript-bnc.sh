#!/bin/bash
set -x

echo "Running init script for BNC"

# Run the application
java -javaagent:/opt/datadog/dd-java-agent.jar -Djavax.net.ssl.trustStore=/mnt/certs/truststore/nbc-root-truststore.jks -Djavax.net.ssl.trustStorePassword=${TRUST_STORE_PASSWORD} -Djavax.net.ssl.keyStore=/mnt/certs/keystore/keystore-cert.jks -Djavax.net.ssl.keyStorePassword=${KEYSTORE_PASSWORD} -Dcom.sun.management.jmxremote -cp @/app/jib-classpath-file ca.bnc.payment.moneyrequest.Application