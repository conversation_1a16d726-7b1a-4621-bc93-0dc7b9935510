package ca.bnc.payment.moneyrequest.client.party;

import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.Error;
import feign.Request;
import feign.Response;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_API_BAD_REQUEST;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_API_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_API_SERVICE_UNAVAILABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PartyApiErrorDecoderTest {

    private PartyApiErrorDecoder partyApiErrorDecoder;

    @Mock
    private PartyApiDecoderErrorsBuilder partyApiDecoderErrorsBuilder;

    @BeforeEach
    void setup() {
        partyApiErrorDecoder = new PartyApiErrorDecoder(partyApiDecoderErrorsBuilder);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(partyApiDecoderErrorsBuilder);
    }

    @ParameterizedTest(name = "responseCode = {0}, expectedException = {1}, error = {2}")
    @MethodSource("responseCodes")
    void givenPartyException_whenDecode_thenThrowException(final Response response,
                                                           final Exception expectedException,
                                                           final Error error) {
        given(partyApiDecoderErrorsBuilder.buildErrorFromResponseBody(response)).willReturn(error);

        final Exception exception = partyApiErrorDecoder.decode(null, response);

        assertThat(exception)
                .isExactlyInstanceOf(MoneyRequestException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }

    private static Stream<Arguments> responseCodes() {
        final Request request = Instancio.create(Request.class);
        final Error error = Instancio.create(Error.class);

        return Stream.of(
                Arguments.of(
                        Response.builder().request(request).status(400).build(),
                        MoneyRequestException.internalServerError(
                            TECHNICAL_ERROR_CODE,
                                PARTY_API_BAD_REQUEST,
                            PARTY_SERVICE_ORIGIN,
                            NA
                        ),
                        error
                ),
                Arguments.of(
                        Response.builder().request(request).status(500).build(),
                        MoneyRequestException.internalServerError(
                                TECHNICAL_ERROR_CODE,
                                PARTY_API_INTERNAL_SERVER_ERROR,
                                PARTY_SERVICE_ORIGIN,
                                NA
                        ),
                        error
                ),
                Arguments.of(
                        Response.builder().request(request).status(503).build(),
                        MoneyRequestException.internalServerError(
                                TECHNICAL_ERROR_CODE,
                                PARTY_API_SERVICE_UNAVAILABLE,
                                PARTY_SERVICE_ORIGIN,
                                NA
                        ),
                        error
                ),
                Arguments.of(
                        Response.builder().request(request).status(418).build(),
                        MoneyRequestException.internalServerError(
                                error.getCode(),
                                error.getText(),
                                error.getOrigin(),
                                error.getRule()
                        ),
                        error
                )
        );
    }


}
