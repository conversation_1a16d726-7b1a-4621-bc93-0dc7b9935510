package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.RemittanceInformation16;
import ca.bnc.payment.moneyrequest.generated.model.RemittanceInformation;
import ca.bnc.payment.moneyrequest.generated.model.StructuredRemittanceInformation16;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;

@ExtendWith(MockitoExtension.class)
class MoneyRequestResponseRemittanceInformationMapperTest {

    private MoneyRequestResponseRemittanceInformationMapper moneyRequestResponseRemittanceInformationMapper;

    @Mock
    private MoneyRequestResponseRemittanceInformationStructuredMapper moneyRequestResponseRemittanceInformationStructuredMapper;

    @BeforeEach
    void setUp() {
            moneyRequestResponseRemittanceInformationMapper = new MoneyRequestResponseRemittanceInformationMapper(moneyRequestResponseRemittanceInformationStructuredMapper);
    }

    @Test
    void map() {
        final RemittanceInformation16 remittanceInformation = Instancio.create(RemittanceInformation16.class);
        final ca.bnc.payment.interac_money_request.generated.model.StructuredRemittanceInformation16 structuredRemittanceInformation
                = remittanceInformation.getStructured().get(0);
        final StructuredRemittanceInformation16 expectedStructuredRemittanceInformation = Instancio.create(StructuredRemittanceInformation16.class);
        given(moneyRequestResponseRemittanceInformationStructuredMapper.map(structuredRemittanceInformation)).willReturn(expectedStructuredRemittanceInformation);

        final RemittanceInformation actual = moneyRequestResponseRemittanceInformationMapper.map(remittanceInformation);

        assertThat(actual.getStructured().get(0)).isEqualTo(expectedStructuredRemittanceInformation);
        assertThat(actual.getUnstructured()).isNotNull();
        then(moneyRequestResponseRemittanceInformationStructuredMapper).should().map(structuredRemittanceInformation);
    }

}
