package ca.bnc.payment.moneyrequest.controller;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;
import java.util.UUID;

import static ca.bnc.payment.moneyrequest.controller.LogContextBuilder.LOG_FIELD_REQUEST_ID;
import static ca.bnc.payment.moneyrequest.controller.LogContextBuilder.LOG_FIELD_USER_ID;
import static org.assertj.core.api.Assertions.assertThat;

class LogContextBuilderTest {

    private static final UUID REQUEST_ID = Instancio.create(UUID.class);

    private LogContextBuilder testee;

    @BeforeEach
    void setup() {
        testee = new LogContextBuilder();
    }

    @Test
    void testContextForCreateUser() {
        Map<String, Object> actual = testee.contextFor(REQUEST_ID);
        assertThat(actual).containsExactlyInAnyOrderEntriesOf(Map.of(
                LOG_FIELD_REQUEST_ID, REQUEST_ID.toString()));
    }

    @Test
    void testContextForGetUser() {
        String userId = "anyString";
        Map<String, Object> actual = testee.contextFor(REQUEST_ID, userId);
        assertThat(actual).containsExactlyInAnyOrderEntriesOf(Map.of(
                LOG_FIELD_REQUEST_ID, REQUEST_ID.toString(),
                LOG_FIELD_USER_ID, userId));
    }

}
