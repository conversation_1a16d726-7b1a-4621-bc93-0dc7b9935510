package ca.bnc.payment.moneyrequest.controller;

import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.generated.model.DomesticFulfillmentMoneyRequest;
import ca.bnc.payment.moneyrequest.service.MoneyRequestService;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Map;
import java.util.UUID;
import java.util.function.Supplier;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class MoneyRequestControllerTest {

    private static final String X_CHANNEL_ID = Instancio.create(String.class);
    private static final UUID X_REQUEST_ID = Instancio.create(UUID.class);
    private static final String X_CLIENT_ID = Instancio.create(String.class);
    private static final UUID BNC_BUSINESS_TRACE_ID = Instancio.create(UUID.class);
    private static final String INTERAC_MONEY_REQUEST_ID = Instancio.create(String.class);
    private static final DeclineRequest DECLINE_REQUEST = Instancio.create(DeclineRequest.class);

    private MoneyRequestController testee;

    @Mock
    private MoneyRequestService moneyRequestService;

    @Mock
    private LogContextHolder logContextHolder;

    @Mock
    private LogContextHelper logContextHelper;

    @Captor
    private ArgumentCaptor<Supplier<ResponseEntity<Object>>> responseSupplierCaptor;

    @BeforeEach
    void setUp() {
        testee = new MoneyRequestController(moneyRequestService, logContextHolder, logContextHelper);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(moneyRequestService, logContextHolder, logContextHelper);
    }

    @Test
    void testGetIncomingMoneyRequestReturnsNull() {
        ResponseEntity<Object> expectedApiResponse = ResponseEntity.status(HttpStatus.OK).body(null);
        Map<String, Object> logContext = Instancio.create(new TypeToken<>() {
        });
        given(logContextHelper.contextFor(X_REQUEST_ID, X_CLIENT_ID, BNC_BUSINESS_TRACE_ID, INTERAC_MONEY_REQUEST_ID)).willReturn(logContext);
        given(logContextHolder.runWithContextNoReset(responseSupplierCaptor.capture(), eq(logContext))).willReturn(expectedApiResponse);
        ResponseEntity<DomesticFulfillmentMoneyRequest> actualApiResponse = testee.getIncomingMoneyRequest(
                INTERAC_MONEY_REQUEST_ID,
                X_CHANNEL_ID,
                ChannelType.WEB,
                X_REQUEST_ID,
                X_CLIENT_ID,
                "accept",
                "traceparent",
                "tracestate",
                BNC_BUSINESS_TRACE_ID,
                "xClientAgentId",
                "xAgentId"
        );

        assertEquals(expectedApiResponse, actualApiResponse);
        assertThat(responseSupplierCaptor.getAllValues())
                .singleElement()
                .satisfies(responseEntitySupplier -> assertThat(responseEntitySupplier.get())
                        .satisfies(supplierEntity -> {
                            assertThat(supplierEntity.getStatusCode()).isEqualTo(HttpStatus.OK);
                            assertThat(supplierEntity.getBody()).isNull();
                        }));
        then(moneyRequestService).should().processGetIncomingMoneyRequest(X_CHANNEL_ID, ChannelType.WEB, X_REQUEST_ID, X_CLIENT_ID,
                INTERAC_MONEY_REQUEST_ID);
    }

    @Test
    void testDeclineReturnsNull() {
        ResponseEntity<Object> expectedApiResponse = ResponseEntity.status(HttpStatus.NO_CONTENT).body(null);
        Map<String, Object> logContext = Instancio.create(new TypeToken<>() {
        });
        given(logContextHelper.contextFor(X_REQUEST_ID, X_CLIENT_ID, BNC_BUSINESS_TRACE_ID, INTERAC_MONEY_REQUEST_ID)).willReturn(logContext);
        given(logContextHolder.runWithContextNoReset(responseSupplierCaptor.capture(), eq(logContext))).willReturn(expectedApiResponse);
        ResponseEntity<Void> result = testee.decline(
                INTERAC_MONEY_REQUEST_ID,
                X_CHANNEL_ID,
                ChannelType.WEB,
                X_CLIENT_ID,
                "accept",
                X_REQUEST_ID,
                "traceparent",
                "tracestate",
                BNC_BUSINESS_TRACE_ID,
                "xClientAgentId",
                "xAgentId",
                DECLINE_REQUEST
        );
        assertThat(result).isEqualTo(expectedApiResponse);
        assertThat(responseSupplierCaptor.getAllValues())
                .singleElement()
                .satisfies(responseEntitySupplier -> assertThat(responseEntitySupplier.get())
                        .satisfies(supplierEntity -> {
                            assertThat(supplierEntity.getStatusCode()).isEqualTo(HttpStatus.NO_CONTENT);
                            assertThat(supplierEntity.getBody()).isNull();
                        }));
        then(moneyRequestService).should().processDeclineIncomingMoneyRequest(
                X_CHANNEL_ID,
                ChannelType.WEB,
                X_REQUEST_ID,
                X_CLIENT_ID,
                INTERAC_MONEY_REQUEST_ID,
                DECLINE_REQUEST
        );
    }

}
