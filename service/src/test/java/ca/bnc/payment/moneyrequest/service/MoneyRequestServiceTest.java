package ca.bnc.payment.moneyrequest.service;

import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.moneyrequest.adapter.async.producer.AsyncPptdPublisher;
import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.facade.InteracMoneyRequestFacade;
import ca.bnc.payment.moneyrequest.facade.PartyFacade;
import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.generated.model.DomesticFulfillmentMoneyRequest;
import ca.bnc.payment.moneyrequest.mapper.entity.MoneyRequestEntityMapper;
import ca.bnc.payment.moneyrequest.mapper.pptd.PptdMapper;
import ca.bnc.payment.moneyrequest.mapper.response.MoneyRequestResponseMapper;
import ca.bnc.payment.moneyrequest.model.ActivePartyIdentifier;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.repository.IncomingMoneyRequestRepository;
import ca.bnc.payment.moneyrequest.validators.MoneyRequestValidator;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEvent;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class MoneyRequestServiceTest {

    private MoneyRequestService moneyRequestService;

    @Mock
    private PartyFacade partyFacade;

    @Mock
    private InteracMoneyRequestFacade interacMoneyRequestFacade;

    @Mock
    private MoneyRequestEntityMapper moneyRequestEntityMapper;

    @Mock
    private IncomingMoneyRequestRepository incomingMoneyRequestRepository;

    @Mock
    private MoneyRequestValidator moneyRequestValidator;

    @Mock
    private MoneyRequestResponseMapper moneyRequestResponseMapper;

    @Mock
    private PptdMapper pptdMapper;
    @Mock
    private AsyncPptdPublisher asyncPptdPublisher;
    @Mock
    private LogContextHolder logContextHolder;

    private static final String X_CHANNEL_ID = Instancio.create(String.class);
    private static final ChannelType X_CHANNEL_TYPE = Instancio.create(ChannelType.class);
    private static final UUID X_REQUEST_ID = Instancio.create(UUID.class);
    private static final String X_CLIENT_ID = Instancio.create(String.class);
    private static final String INTERAC_MONEY_REQUEST_ID = Instancio.create(String.class);
    private static final DeclineRequest DECLINE_REQUEST = Instancio.create(DeclineRequest.class);
    private static final String INTERAC_USER = Instancio.create(String.class);

    @BeforeEach
    void init() {
        moneyRequestService = new MoneyRequestService(
                partyFacade,
                interacMoneyRequestFacade,
                moneyRequestEntityMapper,
                incomingMoneyRequestRepository,
                moneyRequestValidator,
                moneyRequestResponseMapper,
                pptdMapper,
                asyncPptdPublisher,
                logContextHolder
        );
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(partyFacade, interacMoneyRequestFacade, moneyRequestEntityMapper, incomingMoneyRequestRepository, moneyRequestValidator);
    }

    @Test
    void processGetIncomingMoneyRequest() {
        final ActivePartyIdentifier activePartyIdentifier = Instancio.of(ActivePartyIdentifier.class)
                .set(field("interacUserId"), INTERAC_USER)
                .set(field("clientType"), ClientType.ORGANISATION)
                .create();
        final MoneyRequestContext moneyRequestContext = Instancio.of(MoneyRequestContext.class)
                .set(field("channelId"), X_CHANNEL_ID)
                .set(field("channelType"), X_CHANNEL_TYPE)
                .set(field("requestId"), X_REQUEST_ID)
                .set(field("clientId"), X_CLIENT_ID)
                .set(field("interacMoneyRequestId"), INTERAC_MONEY_REQUEST_ID)
                .set(field("activePartyIdentifier"), activePartyIdentifier)
                .create();
        final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse = Instancio.create(IncomingRequestForPaymentResponse.class);
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.create(IncomingMoneyRequestEntity.class);
        final DomesticFulfillmentMoneyRequest domesticFulfillmentMoneyRequest = Instancio.create(DomesticFulfillmentMoneyRequest.class);
        given(partyFacade.fetchActiveInteracIdentifier(X_CHANNEL_ID, X_REQUEST_ID, X_CLIENT_ID)).willReturn(activePartyIdentifier);
        given(interacMoneyRequestFacade.getIncomingRequestForPayment(moneyRequestContext)).willReturn(incomingRequestForPaymentResponse);
        given(incomingMoneyRequestRepository.retrieve(INTERAC_MONEY_REQUEST_ID)).willReturn(incomingMoneyRequestEntity);
        given(moneyRequestEntityMapper.mapForGetMoneyRequest(incomingRequestForPaymentResponse, moneyRequestContext)).willReturn(incomingMoneyRequestEntity);
        given(moneyRequestResponseMapper.map(incomingRequestForPaymentResponse)).willReturn(domesticFulfillmentMoneyRequest);

        DomesticFulfillmentMoneyRequest actual = moneyRequestService.processGetIncomingMoneyRequest(X_CHANNEL_ID, X_CHANNEL_TYPE, X_REQUEST_ID, X_CLIENT_ID, INTERAC_MONEY_REQUEST_ID);

        InOrder inOrder = inOrder(incomingMoneyRequestRepository, moneyRequestValidator, partyFacade, interacMoneyRequestFacade,
                moneyRequestEntityMapper, moneyRequestResponseMapper);

        assertThat(actual).isEqualTo(domesticFulfillmentMoneyRequest);
        then(incomingMoneyRequestRepository).should(inOrder).retrieve(INTERAC_MONEY_REQUEST_ID);
        then(moneyRequestValidator).should(inOrder).validate(incomingMoneyRequestEntity);
        then(partyFacade).should(inOrder).fetchActiveInteracIdentifier(X_CHANNEL_ID, X_REQUEST_ID, X_CLIENT_ID);
        then(interacMoneyRequestFacade).should(inOrder).getIncomingRequestForPayment(moneyRequestContext);
        then(incomingMoneyRequestRepository).should(inOrder).save(incomingMoneyRequestEntity);
        then(moneyRequestResponseMapper).should(inOrder).map(incomingRequestForPaymentResponse);
    }

    @Test
    void processDeclineIncomingMoneyRequest() {
        final ActivePartyIdentifier activePartyIdentifier = Instancio.of(ActivePartyIdentifier.class)
                .set(field("interacUserId"), INTERAC_USER)
                .set(field("clientType"), ClientType.ORGANISATION)
                .create();
        final MoneyRequestContext moneyRequestContext = Instancio.of(MoneyRequestContext.class)
                .set(field("channelId"), X_CHANNEL_ID)
                .set(field("channelType"), X_CHANNEL_TYPE)
                .set(field("requestId"), X_REQUEST_ID)
                .set(field("clientId"), X_CLIENT_ID)
                .set(field("interacMoneyRequestId"), INTERAC_MONEY_REQUEST_ID)
                .set(field("activePartyIdentifier"), activePartyIdentifier)
                .create();
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.create(IncomingMoneyRequestEntity.class);

        DomesticETransferEvent expectedPptdEvent = Instancio.create(DomesticETransferEvent.class);
        given(partyFacade.fetchActiveInteracIdentifier(X_CHANNEL_ID, X_REQUEST_ID, X_CLIENT_ID)).willReturn(activePartyIdentifier);
        given(incomingMoneyRequestRepository.retrieve(INTERAC_MONEY_REQUEST_ID)).willReturn(incomingMoneyRequestEntity);
        given(moneyRequestEntityMapper.updateForDeclineMoneyRequest(DECLINE_REQUEST, incomingMoneyRequestEntity)).willReturn(incomingMoneyRequestEntity);
        given(pptdMapper.map(DECLINE_REQUEST, moneyRequestContext, incomingMoneyRequestEntity)).willReturn(expectedPptdEvent);

        moneyRequestService.processDeclineIncomingMoneyRequest(X_CHANNEL_ID, X_CHANNEL_TYPE, X_REQUEST_ID, X_CLIENT_ID, INTERAC_MONEY_REQUEST_ID, DECLINE_REQUEST);

        InOrder inOrder = inOrder(moneyRequestValidator, partyFacade, interacMoneyRequestFacade, moneyRequestEntityMapper,
                incomingMoneyRequestRepository, asyncPptdPublisher);

        then(moneyRequestValidator).should(inOrder).checkIfEmptyAndValidate(incomingMoneyRequestEntity);
        then(partyFacade).should(inOrder).fetchActiveInteracIdentifier(X_CHANNEL_ID, X_REQUEST_ID, X_CLIENT_ID);
        then(moneyRequestEntityMapper).should(inOrder).updateForDeclineMoneyRequest(DECLINE_REQUEST, incomingMoneyRequestEntity);
        then(interacMoneyRequestFacade).should(inOrder).declineRequestForPayment(moneyRequestContext, DECLINE_REQUEST);
        then(incomingMoneyRequestRepository).should(inOrder).save(incomingMoneyRequestEntity);
        then(asyncPptdPublisher).should(inOrder).sendMessage(expectedPptdEvent, X_REQUEST_ID, logContextHolder.copyContext());
    }

}