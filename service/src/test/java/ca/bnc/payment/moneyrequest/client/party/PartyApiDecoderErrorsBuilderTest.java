package ca.bnc.payment.moneyrequest.client.party;

import ca.bnc.payment.moneyrequest.generated.model.Error;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ca.nbc.payment.pmtpartnersparty.model.Errors;
import ch.qos.logback.classic.Logger;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;

import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_API_GENERIC_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PartyApiDecoderErrorsBuilderTest {

    private static final String ERROR_LOG = "An error occurred while parsing error response from the Party API";
    private static final Errors PARTY_API_ERRORS = Instancio.create(Errors.class);
    private static final Error DEFAULT_ERROR = new Error()
            .code(TECHNICAL_ERROR_CODE)
            .text(PARTY_API_GENERIC_ERROR)
            .origin(PARTY_SERVICE_ORIGIN)
            .rule(NA);

    private final Logger logger = (Logger) LoggerFactory.getLogger(PartyApiDecoderErrorsBuilder.class.getName());

    private PartyApiDecoderErrorsBuilder partyApiDecoderErrorsBuilder;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private LoggingFacade loggingFacade;

    @Mock
    private Response response;

    @Mock
    private Response.Body body;

    @Mock
    private InputStream inputStream;

    @BeforeEach
    void setup() {
        partyApiDecoderErrorsBuilder = new PartyApiDecoderErrorsBuilder(objectMapper, loggingFacade);
        given(response.body()).willReturn(body);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(
                objectMapper,
                objectMapper,
                loggingFacade,
                response,
                body,
                inputStream);
    }

    @Test
    void givenResponseBodyNull_whenBuildErrorFromResponseBody_thenReturnDefaultError() {
        given(response.body()).willReturn(null);

        Error actualError = partyApiDecoderErrorsBuilder.buildErrorFromResponseBody(response);

        assertThat(actualError).isEqualTo(DEFAULT_ERROR);
    }

    @Test
    void givenInputStreamException_whenBuildErrorFromResponseBody_thenLogError() throws IOException {
        final IOException ioException = Instancio.create(IOException.class);
        given(body.asInputStream()).willThrow(ioException);

        Error actualError = partyApiDecoderErrorsBuilder.buildErrorFromResponseBody(response);

        assertThat(actualError).isEqualTo(DEFAULT_ERROR);
        then(loggingFacade).should().error(logger, ERROR_LOG, ioException);
    }

    @Test
    void givenObjectMapperException_whenBuildErrorFromResponseBody_thenLogError() throws IOException {
        final RuntimeException exception = Instancio.create(RuntimeException.class);
        given(body.asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(inputStream, Errors.class)).willThrow(exception);

        Error actualError = partyApiDecoderErrorsBuilder.buildErrorFromResponseBody(response);

        assertThat(actualError).isEqualTo(DEFAULT_ERROR);
        then(loggingFacade).should().error(logger, ERROR_LOG, exception);
        then(inputStream).should().close();
    }

    @Test
    void whenBuildErrorFromResponseBody_thenReturnErrors() throws IOException {
        given(body.asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(inputStream, Errors.class)).willReturn(PARTY_API_ERRORS);
        Error expectedError = new Error()
                .code(PARTY_API_ERRORS.getErrors().get(0).getCode())
                .text(PARTY_API_ERRORS.getErrors().get(0).getText())
                .origin(PARTY_API_ERRORS.getErrors().get(0).getOrigin())
                .rule(PARTY_API_ERRORS.getErrors().get(0).getRule());

        Error actualError = partyApiDecoderErrorsBuilder.buildErrorFromResponseBody(response);

        assertThat(actualError).isEqualTo(expectedError);
        then(inputStream).should().close();
    }

}