package ca.bnc.payment.moneyrequest.util;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Clock;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;

import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class TimeServiceTest {

    private static final String FORMATTED_LOCAL_DATE_TIME = "2024-01-01T00:00:00.000Z";
    private static final OffsetDateTime OFFSET_DATE_TIME = OffsetDateTime.parse("2024-01-01T00:00:00.000Z");
    private static final LocalDateTime LOCAL_DATE_TIME = LocalDateTime.parse("2024-01-01T00:00:00.000");
    private static final LocalDate LOCAL_DATE = LocalDate.parse("2024-01-01");
    private static final Clock clock = Clock.fixed(LOCAL_DATE_TIME.toInstant(UTC), UTC);

    private TimeService instance;

    @BeforeEach
    void setUp() {
        instance = new TimeService(clock);
    }

    @Test
    void whenGetNowOffsetDateTime_willReturn() {
        final OffsetDateTime result = instance.getNowOffsetDateTime();
        assertThat(result).isEqualTo(OFFSET_DATE_TIME);
    }

    @Test
    void whenGetNowLocalDateTime_willReturn() {
        final LocalDateTime result = instance.getNowLocalDateTime();
        assertThat(result).isEqualTo(LOCAL_DATE_TIME);
    }

    @Test
    void whenFormatLocalDateTime_willReturn() {
        final String result = instance.formatLocalDateTime(LOCAL_DATE_TIME);
        assertThat(result).isEqualTo(FORMATTED_LOCAL_DATE_TIME);
    }

    @Test
    void whenGetFormattedNowLocalDateTime_willReturn() {
        final String result = instance.getFormattedNowLocalDateTime();
        assertThat(result).isEqualTo(FORMATTED_LOCAL_DATE_TIME);
    }

    @Test
    void whenParseToOffsetDateTime_willReturn() {
        final OffsetDateTime result = instance.parseToOffsetDateTime(FORMATTED_LOCAL_DATE_TIME);
        assertThat(result).isEqualTo(OFFSET_DATE_TIME);
    }

    @Test
    void whenParseToOffsetDateTime_withInvalidFormat_willThrowException() {
        assertThatThrownBy(() -> instance.parseToOffsetDateTime("invalid-date"))
                .isInstanceOf(DateTimeParseException.class);
    }

    @Test
    void whenParseToLocalDate_willReturn() {
        final LocalDate result = instance.parseToLocalDate(FORMATTED_LOCAL_DATE_TIME);
        assertThat(result).isEqualTo(LOCAL_DATE);
    }

    @Test
    void whenParseToLocalDate_withInvalidFormat_willThrowException() {
        assertThatThrownBy(() -> instance.parseToLocalDate("invalid-date"))
                .isInstanceOf(DateTimeParseException.class);
    }
}
