package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.ActiveOrHistoricCurrencyAndAmount;
import ca.bnc.payment.interac_money_request.generated.model.CreditDebitCode;
import ca.bnc.payment.moneyrequest.generated.model.DocumentAdjustment1;
import ca.bnc.payment.moneyrequest.generated.model.RemittanceAmount2;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MoneyRequestResponseReferredDocumentAmountMapperTest {

    private MoneyRequestResponseReferredDocumentAmountMapper moneyRequestResponseReferredDocumentAmountMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseReferredDocumentAmountMapper = new MoneyRequestResponseReferredDocumentAmountMapper();
    }

    @ParameterizedTest(name = "sourceCreditDebitIndicator = {0}, expectedCreditDebitIndicator = {1}")
    @CsvSource({
            "CRDT, CRDT",
            "DBIT, DBIT",
    })
    void map(final String sourceCreditDebitIndicator, final String expectedCreditDebitIndicator) {
        final ca.bnc.payment.interac_money_request.generated.model.RemittanceAmount2 remittanceAmount =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.RemittanceAmount2.class);
        final ActiveOrHistoricCurrencyAndAmount duePayableAmount = remittanceAmount.getDuePayableAmount();
        final BigDecimal expectedDuePayableAmount = duePayableAmount.getAmount();
        final ActiveOrHistoricCurrencyAndAmount remittedAmount = remittanceAmount.getRemittedAmount();
        final BigDecimal expectedRemittedAmount = remittedAmount.getAmount();
        final List<ca.bnc.payment.interac_money_request.generated.model.DocumentAdjustment1> adjustmentAmountAndReason = remittanceAmount.getAdjustmentAmountAndReason();
        final ca.bnc.payment.interac_money_request.generated.model.DocumentAdjustment1 documentAdjustment = adjustmentAmountAndReason.get(0);
        documentAdjustment.setCreditDebitIndicator(CreditDebitCode.fromValue(sourceCreditDebitIndicator));

        final BigDecimal expectedAdjustmentAmount = documentAdjustment.getAmount().getAmount();
        final String expectedReason = documentAdjustment.getReason();
        final String expectedAdditionalInformation = documentAdjustment.getAdditionalInformation();

        final RemittanceAmount2 actual = moneyRequestResponseReferredDocumentAmountMapper.map(remittanceAmount);

        assertThat(actual).isNotNull();
        assertThat(actual.getDuePayableAmount()).isEqualTo(expectedDuePayableAmount);
        assertThat(actual.getRemittedAmount()).isEqualTo(expectedRemittedAmount);
        assertThat(actual.getAdjustmentAmountAndReason()).isNotNull();
        assertThat(actual.getAdjustmentAmountAndReason().getAmount()).isEqualTo(expectedAdjustmentAmount);
        assertThat(actual.getAdjustmentAmountAndReason().getReason()).isEqualTo(expectedReason);
        assertThat(actual.getAdjustmentAmountAndReason().getAdditionalInformation()).isEqualTo(expectedAdditionalInformation);
        assertThat(actual.getAdjustmentAmountAndReason().getCreditDebitIndicator()).isEqualTo(DocumentAdjustment1.CreditDebitIndicatorEnum.fromValue(expectedCreditDebitIndicator));
    }

    @Test
    void map_withNullCreditDebitIndicator() {
        final ca.bnc.payment.interac_money_request.generated.model.RemittanceAmount2 remittanceAmount =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.RemittanceAmount2.class);
        final ActiveOrHistoricCurrencyAndAmount duePayableAmount = remittanceAmount.getDuePayableAmount();
        final BigDecimal expectedDuePayableAmount = duePayableAmount.getAmount();
        final ActiveOrHistoricCurrencyAndAmount remittedAmount = remittanceAmount.getRemittedAmount();
        final BigDecimal expectedRemittedAmount = remittedAmount.getAmount();
        final List<ca.bnc.payment.interac_money_request.generated.model.DocumentAdjustment1> adjustmentAmountAndReason = remittanceAmount.getAdjustmentAmountAndReason();
        final ca.bnc.payment.interac_money_request.generated.model.DocumentAdjustment1 documentAdjustment = adjustmentAmountAndReason.get(0);
        documentAdjustment.setCreditDebitIndicator(null);

        final BigDecimal expectedAdjustmentAmount = documentAdjustment.getAmount().getAmount();
        final String expectedReason = documentAdjustment.getReason();
        final String expectedAdditionalInformation = documentAdjustment.getAdditionalInformation();

        final RemittanceAmount2 actual = moneyRequestResponseReferredDocumentAmountMapper.map(remittanceAmount);

        assertThat(actual).isNotNull();
        assertThat(actual.getDuePayableAmount()).isEqualTo(expectedDuePayableAmount);
        assertThat(actual.getRemittedAmount()).isEqualTo(expectedRemittedAmount);
        assertThat(actual.getAdjustmentAmountAndReason()).isNotNull();
        assertThat(actual.getAdjustmentAmountAndReason().getAmount()).isEqualTo(expectedAdjustmentAmount);
        assertThat(actual.getAdjustmentAmountAndReason().getReason()).isEqualTo(expectedReason);
        assertThat(actual.getAdjustmentAmountAndReason().getAdditionalInformation()).isEqualTo(expectedAdditionalInformation);
        assertThat(actual.getAdjustmentAmountAndReason().getCreditDebitIndicator()).isNull();
    }

    @Test
    void map_withEmptyDocumentAdjustmentList() {
        final ca.bnc.payment.interac_money_request.generated.model.RemittanceAmount2 remittanceAmount =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.RemittanceAmount2.class);
        remittanceAmount.setAdjustmentAmountAndReason(List.of());
        final ActiveOrHistoricCurrencyAndAmount duePayableAmount = remittanceAmount.getDuePayableAmount();
        final BigDecimal expectedDuePayableAmount = duePayableAmount.getAmount();
        final ActiveOrHistoricCurrencyAndAmount remittedAmount = remittanceAmount.getRemittedAmount();
        final BigDecimal expectedRemittedAmount = remittedAmount.getAmount();

        final RemittanceAmount2 actual = moneyRequestResponseReferredDocumentAmountMapper.map(remittanceAmount);

        assertThat(actual).isNotNull();
        assertThat(actual.getDuePayableAmount()).isEqualTo(expectedDuePayableAmount);
        assertThat(actual.getRemittedAmount()).isEqualTo(expectedRemittedAmount);
        assertThat(actual.getAdjustmentAmountAndReason()).isNull();
    }

}