package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEventRawData;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEventRawDataMessageData;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.SupplementaryData;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PptdRawDataMapperTest {

    private static final String END_TO_END_IDENTIFICATION = "fbf92832-0a6c-444f-af63-8e7da6bec0fe";
    private static final String INSTRUCTION_IDENTIFICATION = "instructionIdentification";
    private static final String DATE_TIME_STRING = "2014-12-22T10:15:30Z";

    private PptdRawDataMapper pptdRawDataMapper;

    @Mock
    private PptdMessageDataMapper pptdMessageDataMapper;

    @Mock
    private PptdSupplementaryDataMapper pptdSupplementaryDataMapper;

    @Mock
    private TimeService timeService;

    @BeforeEach
    void setup() {
        pptdRawDataMapper = new PptdRawDataMapper(pptdMessageDataMapper, pptdSupplementaryDataMapper, timeService);
    }

    @AfterEach
    void verifyNoUndesiredInteractions() {
        verifyNoMoreInteractions(pptdMessageDataMapper, pptdSupplementaryDataMapper, timeService);
    }

    @Test
    void map() {
        final MoneyRequestContext moneyRequestContext = Instancio.create(MoneyRequestContext.class);
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.of(IncomingMoneyRequestEntity.class)
                .set(field("sysCreatedDate"), DATE_TIME_STRING)
                .create();
        final DomesticETransferEventRawDataMessageData domesticETransferEventRawDataMessageData = Instancio.create(DomesticETransferEventRawDataMessageData.class);
        final SupplementaryData supplementaryData = Instancio.create(SupplementaryData.class);
        given(pptdMessageDataMapper.map(declineRequest, incomingMoneyRequestEntity)).willReturn(domesticETransferEventRawDataMessageData);
        given(pptdSupplementaryDataMapper.map(moneyRequestContext)).willReturn(supplementaryData);
        given(timeService.parseToOffsetDateTime(DATE_TIME_STRING)).willReturn(OffsetDateTime.parse(DATE_TIME_STRING));

        final DomesticETransferEventRawData actual = pptdRawDataMapper.map(
                moneyRequestContext,
                INSTRUCTION_IDENTIFICATION,
                END_TO_END_IDENTIFICATION,
                declineRequest,
                incomingMoneyRequestEntity
        );

        assertThat(actual.getCreationDatetime()).isEqualTo(DATE_TIME_STRING);
        assertThat(actual.getInstructionIdentification()).isEqualTo(INSTRUCTION_IDENTIFICATION);
        assertThat(actual.getEndToEndBusinessIdentification()).isEqualTo(END_TO_END_IDENTIFICATION);
        assertThat(actual.getApprovalRequired()).isFalse();
        assertThat(actual.getMessageDefinitionIdentifier()).isEqualTo("pacs.008.001.08");
        assertThat(actual.getClientId()).isEqualTo(moneyRequestContext.clientId());
        assertThat(actual.getMessageData()).isSameAs(domesticETransferEventRawDataMessageData);
        assertThat(actual.getSupplementaryData()).isSameAs(supplementaryData);
    }

    @Test
    void map_invalidDate() {
        final MoneyRequestContext moneyRequestContext = Instancio.create(MoneyRequestContext.class);
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        final String invalidDate = "invalidDate";
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.of(IncomingMoneyRequestEntity.class)
                .set(field("sysCreatedDate"), invalidDate)
                .create();
        given(timeService.parseToOffsetDateTime(invalidDate)).willThrow(DateTimeParseException.class);

        assertThatThrownBy(() -> pptdRawDataMapper.map(
                moneyRequestContext,
                INSTRUCTION_IDENTIFICATION,
                END_TO_END_IDENTIFICATION,
                declineRequest,
                incomingMoneyRequestEntity
        ))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Error building Kafka message for PPTD: invalid creation date time 'invalidDate'");
    }

}
