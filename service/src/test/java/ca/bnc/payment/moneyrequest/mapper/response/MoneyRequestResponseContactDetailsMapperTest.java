package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.Contact4;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MoneyRequestResponseContactDetailsMapperTest {

    private MoneyRequestResponseContactDetailsMapper moneyRequestResponseContactDetailsMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseContactDetailsMapper = new MoneyRequestResponseContactDetailsMapper();
    }

    @Test
    void map() {
        final ca.bnc.payment.interac_money_request.generated.model.Contact4 contactDetails =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.Contact4.class);

        final Contact4 actual = moneyRequestResponseContactDetailsMapper.map(contactDetails);

        assertThat(actual)
                .isNotNull()
                .usingRecursiveComparison()
                .isEqualTo(contactDetails);
    }

}