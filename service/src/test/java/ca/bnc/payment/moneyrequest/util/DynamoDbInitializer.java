package ca.bnc.payment.moneyrequest.util;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.waiters.WaiterResponse;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.internal.client.DefaultDynamoDbEnhancedClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.DescribeTableRequest;
import software.amazon.awssdk.services.dynamodb.model.DescribeTableResponse;
import software.amazon.awssdk.services.dynamodb.model.ResourceNotFoundException;
import software.amazon.awssdk.services.dynamodb.waiters.DynamoDbWaiter;

import java.net.URI;

import static software.amazon.awssdk.regions.Region.CA_CENTRAL_1;

/**
 * Class to run a local DynamoDB instance
 */
public class DynamoDbInitializer {

    private static final String INCOMING_MONEY_REQUEST_TABLE = "INCOMING_MONEY_REQUEST_TABLE";

    public static void main(final String[] args) {
        createTable(INCOMING_MONEY_REQUEST_TABLE);
    }

    private static void createTable(final String tableName) {
        System.out.println("Creating an Amazon DynamoDB table " + tableName + ".");
        DynamoDbClient ddb = DynamoDbClient.builder()
                .endpointOverride(URI.create("http://localhost:8077"))
                .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create("accessKeyId", "secretAccessKey")))
                .region(CA_CENTRAL_1)
                .build();

        DefaultDynamoDbEnhancedClient enhancedClient = DefaultDynamoDbEnhancedClient.builder()
                .dynamoDbClient(ddb)
                .build();

        DynamoDbTable<IncomingMoneyRequestEntity> incomingMoneyRequestTable =
                enhancedClient.table(tableName, TableSchema.fromBean(IncomingMoneyRequestEntity.class));

        try {
            // Delete if present
            incomingMoneyRequestTable.deleteTable();
        } catch (final ResourceNotFoundException ignored) {
            // Expected
        }

        // Create the table
        incomingMoneyRequestTable.createTable();

        DescribeTableRequest describeTableRequest = DescribeTableRequest.builder()
                .tableName(tableName)
                .build();

        // Wait until the Amazon DynamoDB table is created.
        DynamoDbWaiter dbWaiter = ddb.waiter();
        WaiterResponse<DescribeTableResponse> waiterResponse = dbWaiter.waitUntilTableExists(describeTableRequest);
        waiterResponse.matched().response().ifPresent(System.out::println);
        System.out.println("Table " + tableName + " created");
    }

}

