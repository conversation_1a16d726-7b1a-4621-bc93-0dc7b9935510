package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.CategoryPurpose1Choice;
import ca.bnc.payment.interac_money_request.generated.model.ExternalCategoryPurpose1Code;
import ca.bnc.payment.interac_money_request.generated.model.PaymentInstruction31;
import ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose;
import org.instancio.Instancio;
import org.instancio.Select;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MoneyRequestResponseCategoryPurposeMapperTest {

    private MoneyRequestResponseCategoryPurposeMapper moneyRequestResponseCategoryPurposeMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseCategoryPurposeMapper = new MoneyRequestResponseCategoryPurposeMapper();
    }

    @ParameterizedTest(name = "sourceCategoryPurposeCode = {0}, expectedCategoryPurposeCode = {1}")
    @CsvSource({
            "_240, ANN",
            "_260, INV",
            "_330, INS",
            "_370, MTG",
            "_400, RLS",
            "_430, BPY",
            "_460, AP",
            "_480, DON",
            "_452, EXP",
            "_308, CCB",
            "_311, OAS",
            "_318, EI"
    })
    void mapListedCategoryPurposeCode(final String sourceCategoryPurposeCode, final String expectedCategoryPurposeCode) {
        final ExternalCategoryPurpose1Code categoryPurposeCode = ExternalCategoryPurpose1Code.valueOf(sourceCategoryPurposeCode);
        final PaymentInstruction31 paymentInstruction = Instancio.of(PaymentInstruction31.class)
                .set(Select.field(CategoryPurpose1Choice::getCode), categoryPurposeCode)
                .create();

        final CategoryPurpose actual = moneyRequestResponseCategoryPurposeMapper.map(paymentInstruction);

        assertThat(actual.getCode()).isEqualTo(CategoryPurpose.CodeEnum.valueOf(expectedCategoryPurposeCode));
    }

    @Test
    void mapUnlistedCategoryPurposeCode() {
        final PaymentInstruction31 paymentInstruction = Instancio.of(PaymentInstruction31.class)
                .set(Select.field(CategoryPurpose1Choice::getCode), ExternalCategoryPurpose1Code.BONU)
                .create();

        final CategoryPurpose actual = moneyRequestResponseCategoryPurposeMapper.map(paymentInstruction);

        assertThat(actual).isNull();
    }

}
