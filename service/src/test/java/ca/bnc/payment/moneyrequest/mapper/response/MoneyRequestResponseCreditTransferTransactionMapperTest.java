package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.BranchAndFinancialInstitutionIdentification6;
import ca.bnc.payment.interac_money_request.generated.model.CreditTransferTransaction35;
import ca.bnc.payment.interac_money_request.generated.model.Document12;
import ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135;
import ca.bnc.payment.interac_money_request.generated.model.RemittanceInformation16;
import ca.bnc.payment.moneyrequest.generated.model.Amount;
import ca.bnc.payment.moneyrequest.generated.model.CreditTransferTransaction;
import ca.bnc.payment.moneyrequest.generated.model.Creditor;
import ca.bnc.payment.moneyrequest.generated.model.CreditorAgent;
import ca.bnc.payment.moneyrequest.generated.model.Invoice;
import ca.bnc.payment.moneyrequest.generated.model.PaymentIdentification6;
import ca.bnc.payment.moneyrequest.generated.model.RemittanceInformation;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class MoneyRequestResponseCreditTransferTransactionMapperTest {
    
    private MoneyRequestResponseCreditTransferTransactionMapper moneyRequestResponseCreditTransferTransactionMapper;
    
    @Mock
    private MoneyRequestResponsePaymentIdentificationMapper moneyRequestResponsePaymentIdentificationMapper;

    @Mock
    private MoneyRequestResponseAmountMapper moneyRequestResponseAmountMapper;

    @Mock
    private MoneyRequestResponseCreditorAgentMapper moneyRequestResponseCreditorAgentMapper;

    @Mock
    private MoneyRequestResponseCreditorMapper moneyRequestResponseCreditorMapper;

    @Mock
    private MoneyRequestResponseRemittanceInformationMapper moneyRequestResponseRemittanceInformationMapper;

    @Mock
    private MoneyRequestResponseInvoiceMapper moneyRequestResponseInvoiceMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseCreditTransferTransactionMapper = new MoneyRequestResponseCreditTransferTransactionMapper(
                moneyRequestResponsePaymentIdentificationMapper,
                moneyRequestResponseAmountMapper,
                moneyRequestResponseCreditorAgentMapper,
                moneyRequestResponseCreditorMapper,
                moneyRequestResponseRemittanceInformationMapper,
                moneyRequestResponseInvoiceMapper
        );
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(
                moneyRequestResponsePaymentIdentificationMapper,
                moneyRequestResponseAmountMapper,
                moneyRequestResponseCreditorAgentMapper,
                moneyRequestResponseCreditorMapper,
                moneyRequestResponseRemittanceInformationMapper,
                moneyRequestResponseInvoiceMapper
        );
    }

    @Test
    void map() {
        final CreditTransferTransaction35 creditTransferTransaction = Instancio.create(CreditTransferTransaction35.class);
        final ca.bnc.payment.interac_money_request.generated.model.PaymentIdentification6 paymentIdentification
                = creditTransferTransaction.getPaymentIdentification();
        final PaymentIdentification6 expectedMappedPaymentIdentification = Instancio.create(PaymentIdentification6.class);
        final ca.bnc.payment.interac_money_request.generated.model.AmountType4Choice amount = creditTransferTransaction.getAmount();
        final Amount expectedAmount = Instancio.create(Amount.class);
        final BranchAndFinancialInstitutionIdentification6 creditorAgent = creditTransferTransaction.getCreditorAgent();
        final CreditorAgent expectedCreditorAgent = Instancio.create(CreditorAgent.class);
        final PartyIdentification135 creditor = creditTransferTransaction.getCreditor();
        final Creditor expectedCreditor = Instancio.create(Creditor.class);
        final RemittanceInformation16 remittanceInformation = creditTransferTransaction.getRemittanceInformation();
        final RemittanceInformation expectedRemittanceInformation = Instancio.create(RemittanceInformation.class);
        final Document12 invoice = creditTransferTransaction.getEnclosedFile().get(0);
        final Invoice expectedInvoice = Instancio.create(Invoice.class);
        given(moneyRequestResponsePaymentIdentificationMapper.map(paymentIdentification)).willReturn(expectedMappedPaymentIdentification);
        given(moneyRequestResponseAmountMapper.map(amount)).willReturn(expectedAmount);
        given(moneyRequestResponseCreditorAgentMapper.map(creditorAgent)).willReturn(expectedCreditorAgent);
        given(moneyRequestResponseCreditorMapper.map(creditor)).willReturn(expectedCreditor);
        given(moneyRequestResponseRemittanceInformationMapper.map(remittanceInformation)).willReturn(expectedRemittanceInformation);
        given(moneyRequestResponseInvoiceMapper.map(invoice)).willReturn(expectedInvoice);

        final CreditTransferTransaction actual = moneyRequestResponseCreditTransferTransactionMapper.map(creditTransferTransaction);

        assertThat(actual).isNotNull();
        assertThat(actual.getPaymentIdentification()).isEqualTo(expectedMappedPaymentIdentification);
        assertThat(actual.getAmount()).isEqualTo(expectedAmount);
        assertThat(actual.getCreditorAgent()).isEqualTo(expectedCreditorAgent);
        assertThat(actual.getCreditor()).isEqualTo(expectedCreditor);
        assertThat(actual.getRemittanceInformation()).isEqualTo(expectedRemittanceInformation);
        assertThat(actual.getInvoice()).isEqualTo(expectedInvoice);
        then(moneyRequestResponsePaymentIdentificationMapper).should().map(paymentIdentification);
        then(moneyRequestResponseAmountMapper).should().map(amount);
        then(moneyRequestResponseCreditorAgentMapper).should().map(creditorAgent);
        then(moneyRequestResponseCreditorMapper).should().map(creditor);
        then(moneyRequestResponseRemittanceInformationMapper).should().map(remittanceInformation);
        then(moneyRequestResponseInvoiceMapper).should().map(invoice);
    }

    @Test
    void map_withEmptyEnclosedFilesList() {
        final CreditTransferTransaction35 creditTransferTransaction = Instancio.create(CreditTransferTransaction35.class);
        creditTransferTransaction.setEnclosedFile(List.of());
        final ca.bnc.payment.interac_money_request.generated.model.PaymentIdentification6 paymentIdentification
                = creditTransferTransaction.getPaymentIdentification();
        final PaymentIdentification6 expectedMappedPaymentIdentification = Instancio.create(PaymentIdentification6.class);
        final ca.bnc.payment.interac_money_request.generated.model.AmountType4Choice amount = creditTransferTransaction.getAmount();
        final Amount expectedAmount = Instancio.create(Amount.class);
        final BranchAndFinancialInstitutionIdentification6 creditorAgent = creditTransferTransaction.getCreditorAgent();
        final CreditorAgent expectedCreditorAgent = Instancio.create(CreditorAgent.class);
        final PartyIdentification135 creditor = creditTransferTransaction.getCreditor();
        final Creditor expectedCreditor = Instancio.create(Creditor.class);
        final RemittanceInformation16 remittanceInformation = creditTransferTransaction.getRemittanceInformation();
        final RemittanceInformation expectedRemittanceInformation = Instancio.create(RemittanceInformation.class);
        given(moneyRequestResponsePaymentIdentificationMapper.map(paymentIdentification)).willReturn(expectedMappedPaymentIdentification);
        given(moneyRequestResponseAmountMapper.map(amount)).willReturn(expectedAmount);
        given(moneyRequestResponseCreditorAgentMapper.map(creditorAgent)).willReturn(expectedCreditorAgent);
        given(moneyRequestResponseCreditorMapper.map(creditor)).willReturn(expectedCreditor);
        given(moneyRequestResponseRemittanceInformationMapper.map(remittanceInformation)).willReturn(expectedRemittanceInformation);

        final CreditTransferTransaction actual = moneyRequestResponseCreditTransferTransactionMapper.map(creditTransferTransaction);

        assertThat(actual).isNotNull();
        assertThat(actual.getPaymentIdentification()).isEqualTo(expectedMappedPaymentIdentification);
        assertThat(actual.getAmount()).isEqualTo(expectedAmount);
        assertThat(actual.getCreditorAgent()).isEqualTo(expectedCreditorAgent);
        assertThat(actual.getCreditor()).isEqualTo(expectedCreditor);
        assertThat(actual.getRemittanceInformation()).isEqualTo(expectedRemittanceInformation);
        assertThat(actual.getInvoice()).isNull();
        then(moneyRequestResponsePaymentIdentificationMapper).should().map(paymentIdentification);
        then(moneyRequestResponseAmountMapper).should().map(amount);
        then(moneyRequestResponseCreditorAgentMapper).should().map(creditorAgent);
        then(moneyRequestResponseCreditorMapper).should().map(creditor);
        then(moneyRequestResponseRemittanceInformationMapper).should().map(remittanceInformation);
    }
    
}
