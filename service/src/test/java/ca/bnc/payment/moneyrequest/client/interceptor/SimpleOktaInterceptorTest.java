package ca.bnc.payment.moneyrequest.client.interceptor;

import feign.RequestTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;

@ExtendWith(MockitoExtension.class)
class SimpleOktaInterceptorTest {

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_TOKEN = "Bearer jwtToken";

    private SimpleOktaInterceptor testee;

    @Mock
    private ConfigTokenProvider configTokenProvider;

    @Mock
    private RequestTemplate requestTemplate;

    @BeforeEach
    void setUp() {
        testee = new SimpleOktaInterceptor(configTokenProvider);
    }

    @Test
    void shouldReturnBncOktaInterceptorWhenCreateBncInterceptorIsCalled() {
        given(configTokenProvider.getToken()).willReturn(BEARER_TOKEN);

        testee.apply(requestTemplate);

        then(requestTemplate).should().header(eq(AUTHORIZATION_HEADER), eq(BEARER_TOKEN));
    }
}