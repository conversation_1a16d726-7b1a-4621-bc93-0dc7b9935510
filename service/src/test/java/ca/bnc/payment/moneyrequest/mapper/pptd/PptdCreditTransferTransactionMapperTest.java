package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.CreditTransferTransaction39;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.stream.Stream;

import static ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.ChargeBearerType1Code.SLEV;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PptdCreditTransferTransactionMapperTest {

    private PptdCreditTransferTransactionMapper pptdCreditTransferTransactionMapper;

    @Mock
    private PptdPaymentIdentificationMapper pptdPaymentIdentificationMapper;

    @Mock
    private TimeService timeService;

    @BeforeEach
    void setup() {
        pptdCreditTransferTransactionMapper = new PptdCreditTransferTransactionMapper(pptdPaymentIdentificationMapper, timeService);
    }

    @AfterEach
    public void verifyNoUndesiredInteractions() {
        verifyNoMoreInteractions(pptdPaymentIdentificationMapper, timeService);
    }

    @ParameterizedTest
    @MethodSource("validAmounts")
    void map_validAmounts(final BigDecimal validAmount) {
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        final String sysModifiedDate = "2023-01-15";
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.of(IncomingMoneyRequestEntity.class)
                .set(field("sysModifiedDate"), sysModifiedDate)
                .set(field("amount"), validAmount)
                .create();
        given(pptdPaymentIdentificationMapper.map(
                declineRequest.getFiToFICustomerCreditTransferV08()
                        .getCreditTransferTransactionInformation()
                        .getPaymentIdentification()))
                .willReturn(Instancio.create(ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.PaymentIdentification7.class));
        given(timeService.parseToLocalDate(sysModifiedDate)).willReturn(LocalDate.parse(sysModifiedDate));

        final CreditTransferTransaction39 actual = pptdCreditTransferTransactionMapper.map(declineRequest, incomingMoneyRequestEntity);

        assertThat(actual.getPaymentIdentification()).isNotNull();
        assertThat(actual.getPaymentTypeInformation().getLocalInstrument().getProprietary()).isEqualTo("FULFILL_REQUEST_FOR_PAYMENT");
        assertThat(actual.getInterbankSettlementAmount().getAmount()).isEqualTo(incomingMoneyRequestEntity.getAmount());
        assertThat(actual.getInterbankSettlementAmount().getCurrency()).isEqualTo(incomingMoneyRequestEntity.getCurrency());
        assertThat(actual.getInterbankSettlementDate()).isEqualTo(LocalDate.parse(sysModifiedDate));
        assertThat(actual.getChargeBearer()).isEqualTo(SLEV);
        assertThat(actual.getDebtor().getName()).isEqualTo(incomingMoneyRequestEntity.getDebtorName());
        assertThat(actual.getCreditor().getName()).isEqualTo(incomingMoneyRequestEntity.getCreditorName());
        assertThat(actual.getCreditor().getContactDetails().getPhoneNumber()).isEqualTo(incomingMoneyRequestEntity.getCreditorMobileNumber());
        assertThat(actual.getCreditor().getContactDetails().getEmailAddress()).isEqualTo(incomingMoneyRequestEntity.getCreditorEmailAddress());
    }

    private static Stream<Arguments> validAmounts() {
        return Stream.of(
                Arguments.argumentSet("boundary amount 0.01", new BigDecimal("0.01")),
                Arguments.argumentSet("boundary amount **************", new BigDecimal("**************"))
        );
    }

    @Test
    void map_invalidDate() {
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        final String invalidDate = "invalidDate";
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.of(IncomingMoneyRequestEntity.class)
                .set(field("sysModifiedDate"), invalidDate)
                .create();
        given(pptdPaymentIdentificationMapper.map(
                declineRequest.getFiToFICustomerCreditTransferV08()
                        .getCreditTransferTransactionInformation()
                        .getPaymentIdentification()))
                .willReturn(Instancio.create(ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.PaymentIdentification7.class));
        given(timeService.parseToLocalDate(invalidDate)).willThrow(new DateTimeParseException("Invalid date", invalidDate, 0));

        assertThatThrownBy(() -> pptdCreditTransferTransactionMapper.map(declineRequest, incomingMoneyRequestEntity))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Error building Kafka message for PPTD: invalid interbank settlement date 'invalidDate'");
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("invalidAmounts")
    void map_invalidAmounts(final BigDecimal invalidAmount) {
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        final String sysModifiedDate = "2023-01-15";
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.of(IncomingMoneyRequestEntity.class)
                .set(field("sysModifiedDate"), sysModifiedDate)
                .set(field("amount"), invalidAmount)
                .create();
        given(pptdPaymentIdentificationMapper.map(
                declineRequest.getFiToFICustomerCreditTransferV08()
                        .getCreditTransferTransactionInformation()
                        .getPaymentIdentification()))
                .willReturn(Instancio.create(ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.PaymentIdentification7.class));

        assertThatThrownBy(() -> pptdCreditTransferTransactionMapper.map(declineRequest, incomingMoneyRequestEntity))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Error building Kafka message for PPTD: invalid amount '%s' (not between 0.01 and **************)".formatted(invalidAmount));
    }

    private static Stream<Arguments> invalidAmounts() {
        return Stream.of(
                Arguments.argumentSet("amount lower than 0.01", new BigDecimal("0.009")),
                Arguments.argumentSet("amount greater than **************", new BigDecimal("**************.01"))
        );
    }

    @Test
    void map_nullAmount() {
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        final String sysModifiedDate = "2023-01-15";
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.of(IncomingMoneyRequestEntity.class)
                .set(field("sysModifiedDate"), sysModifiedDate)
                .set(field("amount"), null)
                .create();
        given(pptdPaymentIdentificationMapper.map(
                declineRequest.getFiToFICustomerCreditTransferV08()
                        .getCreditTransferTransactionInformation()
                        .getPaymentIdentification()))
                .willReturn(Instancio.create(ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.PaymentIdentification7.class));

        assertThatThrownBy(() -> pptdCreditTransferTransactionMapper.map(declineRequest, incomingMoneyRequestEntity))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Error building Kafka message for PPTD: invalid amount 'null' (not between 0.01 and **************)");
    }

    @Test
    void map_nullCurrency() {
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        final String sysModifiedDate = "2023-01-15";
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.of(IncomingMoneyRequestEntity.class)
                .set(field("sysModifiedDate"), sysModifiedDate)
                .set(field("currency"), null)
                .create();
        given(pptdPaymentIdentificationMapper.map(
                declineRequest.getFiToFICustomerCreditTransferV08()
                        .getCreditTransferTransactionInformation()
                        .getPaymentIdentification()))
                .willReturn(Instancio.create(ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.PaymentIdentification7.class));

        assertThatThrownBy(() -> pptdCreditTransferTransactionMapper.map(declineRequest, incomingMoneyRequestEntity))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Error building Kafka message for PPTD: currency is null");
    }

}
