package ca.bnc.payment.moneyrequest.facade;

import ca.bnc.payment.moneyrequest.adapter.PartyAdapter;
import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.model.ActivePartyIdentifier;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import ca.nbc.payment.pmtpartnersparty.model.Identifier;
import ca.nbc.payment.pmtpartnersparty.model.IdentifierStatus;
import ca.nbc.payment.pmtpartnersparty.model.Identifiers;
import ca.nbc.payment.pmtpartnersparty.model.IdentifiersMerger;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static ca.nbc.payment.pmtpartnersparty.model.IdentifierType.INTERAC_USER_ID;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PartyFacadeTest {

    private static final String X_CHANNEL_ID = Instancio.create(String.class);
    private static final UUID X_REQUEST_ID = Instancio.create(UUID.class);
    private static final String X_CLIENT_ID = Instancio.create(String.class);
    private static final String ACCEPT_VERSION = "v1";
    private static final String INTERAC_USER = Instancio.create(String.class);

    private PartyFacade partyFacade;

    @Mock
    private PartyAdapter partyAdapter;

    @BeforeEach
    void setup() {
        partyFacade = new PartyFacade(partyAdapter);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(partyAdapter);
    }

    @Test
    void whenActiveIdentifierFound_thenReturnIdentifier() {
        final Identifier identifier = Instancio.of(Identifier.class)
                .set(field("value"), INTERAC_USER)
                .set(field("status"), IdentifierStatus.ACTIVE)
                .create();
        final Identifiers identifiers = Instancio.of(Identifiers.class)
                .set(field("merger"), new IdentifiersMerger().clientType(ClientType.ORGANISATION))
                .set(field("identifiers"), List.of(identifier))
                .create();
        final ActivePartyIdentifier expectedActivePartyIdentifier = Instancio.of(ActivePartyIdentifier.class)
                .set(field("interacUserId"), INTERAC_USER)
                .set(field("clientType"), ClientType.ORGANISATION)
                .create();

        given(partyAdapter.getPartyClientById(
                X_CHANNEL_ID,
                ACCEPT_VERSION,
                X_REQUEST_ID.toString(),
                X_CLIENT_ID,
                List.of(INTERAC_USER_ID),
                IdentifierStatus.ACTIVE
        )).willReturn(identifiers);

        final ActivePartyIdentifier actual = partyFacade.fetchActiveInteracIdentifier(X_CHANNEL_ID, X_REQUEST_ID, X_CLIENT_ID);

        assertThat(actual).isEqualTo(expectedActivePartyIdentifier);
    }

    @Test
    void whenActiveIdentifierNotFound_thenThrowException() {
        final MoneyRequestException expectedException = MoneyRequestException.badRequest(
                "CUSTOMER_NOT_FOUND",
                "The Interac User Id of the client identified by " + X_CLIENT_ID + " was not found.",
                "pmt-partners-party-api",
                "NA"
        );
        final Identifier identifier = Instancio.of(Identifier.class)
                .set(field("status"), IdentifierStatus.INACTIVE)
                .create();
        final Identifiers identifiers = Instancio.of(Identifiers.class)
                .set(field("merger"), new IdentifiersMerger().clientType(ClientType.ORGANISATION))
                .set(field("identifiers"), List.of(identifier))
                .create();
        given(partyAdapter.getPartyClientById(
                X_CHANNEL_ID,
                ACCEPT_VERSION,
                X_REQUEST_ID.toString(),
                X_CLIENT_ID,
                List.of(INTERAC_USER_ID),
                IdentifierStatus.ACTIVE
        )).willReturn(identifiers);

        assertThatThrownBy(() -> partyFacade.fetchActiveInteracIdentifier(X_CHANNEL_ID, X_REQUEST_ID, X_CLIENT_ID))
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }

    @Test
    void whenActiveIdentifierNull_thenThrowException() {
        final MoneyRequestException expectedException = MoneyRequestException.badRequest(
                "CUSTOMER_NOT_FOUND",
                "The Interac User Id of the client identified by " + X_CLIENT_ID + " was not found.",
                "pmt-partners-party-api",
                "NA"
        );
        given(partyAdapter.getPartyClientById(
                X_CHANNEL_ID,
                ACCEPT_VERSION,
                X_REQUEST_ID.toString(),
                X_CLIENT_ID,
                List.of(INTERAC_USER_ID),
                IdentifierStatus.ACTIVE
        )).willReturn(null);

        assertThatThrownBy(() -> partyFacade.fetchActiveInteracIdentifier(X_CHANNEL_ID, X_REQUEST_ID, X_CLIENT_ID))
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }
}