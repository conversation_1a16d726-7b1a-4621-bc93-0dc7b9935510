package ca.bnc.payment.moneyrequest.mapper.entity;

import ca.bnc.payment.interac_money_request.generated.model.ActiveOrHistoricCurrencyCode;
import ca.bnc.payment.interac_money_request.generated.model.Contact4;
import ca.bnc.payment.interac_money_request.generated.model.CreditTransferTransaction35;
import ca.bnc.payment.interac_money_request.generated.model.Document12;
import ca.bnc.payment.interac_money_request.generated.model.FraudCheckResult;
import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.interac_money_request.generated.model.PaymentInstruction31;
import ca.bnc.payment.interac_money_request.generated.model.RemittanceInformation16;
import ca.bnc.payment.interac_money_request.generated.model.RequestForPaymentStatus;
import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.generated.model.DeclineSupplementaryData;
import ca.bnc.payment.moneyrequest.generated.model.Error;
import ca.bnc.payment.moneyrequest.generated.model.MoneyRequestStatus;
import ca.bnc.payment.moneyrequest.mapper.common.MoneyRequestStatusMapper;
import ca.bnc.payment.moneyrequest.model.ActivePartyIdentifier;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static ca.bnc.payment.moneyrequest.constant.Constant.UTC_DATE_TIME_FORMAT;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.params.ParameterizedInvocationConstants.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MoneyRequestEntityMapperTest {

    private static final long EXPIRATION_DELAY_IN_DAYS = 60;
    private static final LocalDateTime LOCAL_DATE_TIME = LocalDateTime.now();
    private static final OffsetDateTime EXPIRY_DATE = Instancio.create(OffsetDateTime.class);
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern(UTC_DATE_TIME_FORMAT);
    public static final MoneyRequestStatus MONEY_REQUEST_STATUS = MoneyRequestStatus.AVAILABLE_TO_BE_FULFILLED;

    private MoneyRequestEntityMapper moneyRequestEntityMapper;

    @Mock
    private TimeService timeService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private MoneyRequestStatusMapper moneyRequestStatusMapper;

    private MoneyRequestContext moneyRequestContext;

    private IncomingMoneyRequestEntity incomingMoneyRequestEntity;

    private IncomingRequestForPaymentResponse paymentResponse;


    @BeforeEach
    void setUp() {
        moneyRequestEntityMapper = new MoneyRequestEntityMapper(timeService, objectMapper, moneyRequestStatusMapper);
        given(timeService.getFormattedNowLocalDateTime()).willReturn(LOCAL_DATE_TIME.format(FORMATTER));

        moneyRequestContext = createMoneyRequestContext();
        incomingMoneyRequestEntity = createCommonMoneyRequestEntity();
        paymentResponse = createCommonPaymentResponse();
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(timeService, objectMapper, moneyRequestStatusMapper);
    }

    @Test
    void shouldReturnAllCommonFields_byIgnoringSpecialMapping() {
        given(timeService.getNowLocalDateTime()).willReturn(LOCAL_DATE_TIME);
        given(moneyRequestStatusMapper.map(paymentResponse.getRequestForPaymentStatus())).willReturn(MONEY_REQUEST_STATUS);

        final IncomingMoneyRequestEntity actualMoneyRequestEntity = assertDoesNotThrow(() -> moneyRequestEntityMapper.mapForGetMoneyRequest(paymentResponse, moneyRequestContext));

        assertThat(actualMoneyRequestEntity).usingRecursiveComparison().isEqualTo(incomingMoneyRequestEntity);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("mobileContactProvider")
    void shouldMapForGetMoneyRequestCreditorMobileNumber_usingContactDetails(Contact4 contactDetails, String expectedMobileNumber) {
        given(timeService.getNowLocalDateTime()).willReturn(LOCAL_DATE_TIME);
        given(moneyRequestStatusMapper.map(paymentResponse.getRequestForPaymentStatus())).willReturn(MONEY_REQUEST_STATUS);
        setContactDetails(contactDetails);
        assertThat(moneyRequestEntityMapper.mapForGetMoneyRequest(paymentResponse, moneyRequestContext))
                .returns(expectedMobileNumber, IncomingMoneyRequestEntity::getCreditorMobileNumber);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("emailContactProvider")
    void shouldMapForGetMoneyRequestEmailAddress_usingContactDetails(Contact4 contactDetails, String expectedEmailAddress) {
        given(timeService.getNowLocalDateTime()).willReturn(LOCAL_DATE_TIME);
        given(moneyRequestStatusMapper.map(paymentResponse.getRequestForPaymentStatus())).willReturn(MONEY_REQUEST_STATUS);
        setContactDetails(contactDetails);
        assertThat(moneyRequestEntityMapper.mapForGetMoneyRequest(paymentResponse, moneyRequestContext))
                .returns(expectedEmailAddress, IncomingMoneyRequestEntity::getCreditorEmailAddress);
    }

    @Test
    void shouldMapForGetMoneyRequestRemittanceInfo() throws Exception {
        given(timeService.getNowLocalDateTime()).willReturn(LOCAL_DATE_TIME);
        given(moneyRequestStatusMapper.map(paymentResponse.getRequestForPaymentStatus())).willReturn(MONEY_REQUEST_STATUS);
        final RemittanceInformation16 remittanceInformation = Instancio.create(RemittanceInformation16.class);
        setRemittanceInformation(remittanceInformation);
        when(objectMapper.writeValueAsString(remittanceInformation)).thenReturn("remittanceInformation");
        assertThat(moneyRequestEntityMapper.mapForGetMoneyRequest(paymentResponse, moneyRequestContext))
                .returns("remittanceInformation", IncomingMoneyRequestEntity::getRemittanceInformation);
    }

    @Test
    void shouldReturnNullWhenWriteValueAsStringThrowsException() throws JsonProcessingException {
        given(timeService.getNowLocalDateTime()).willReturn(LOCAL_DATE_TIME);
        final RemittanceInformation16 remittanceInformation = Instancio.create(RemittanceInformation16.class);
        setRemittanceInformation(remittanceInformation);
        JsonProcessingException jsonProcessingException = Instancio.create(JsonProcessingException.class);
        when(objectMapper.writeValueAsString(remittanceInformation)).thenThrow(jsonProcessingException);
        final Error expectedError = new Error()
                .code(TECHNICAL_ERROR_CODE)
                .origin(SERVICE_ORIGIN)
                .rule(NA)
                .text("MoneyRequestEntityMapper failed upon deserializing remittanceInfo : " + jsonProcessingException.getMessage());


        final MoneyRequestException expectedMoneyRequestException = MoneyRequestException.internalServerError(expectedError);

        assertThatThrownBy(() -> moneyRequestEntityMapper.mapForGetMoneyRequest(paymentResponse, moneyRequestContext))
                .isInstanceOfSatisfying(
                        MoneyRequestException.class,
                        moneyRequestException ->
                                assertThat(moneyRequestException).usingRecursiveComparison().isEqualTo(expectedMoneyRequestException));

    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("invoiceNumberProvider")
    void shouldMapForGetMoneyRequestInvoiceNumber(List<Document12> enclosedFiles, String expectedInvoiceNumber) {
        given(timeService.getNowLocalDateTime()).willReturn(LOCAL_DATE_TIME);
        given(moneyRequestStatusMapper.map(paymentResponse.getRequestForPaymentStatus())).willReturn(MONEY_REQUEST_STATUS);
        setEnclosedFiles(enclosedFiles);
        assertThat(moneyRequestEntityMapper.mapForGetMoneyRequest(paymentResponse, moneyRequestContext))
                .returns(expectedInvoiceNumber, IncomingMoneyRequestEntity::getInvoiceNumber);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("invoiceIssueDateProvider")
    void shouldMapForGetMoneyRequestInvoiceIssueDate(List<Document12> enclosedFiles, String expectedInvoiceIssueDate) {
        given(timeService.getNowLocalDateTime()).willReturn(LOCAL_DATE_TIME);
        given(moneyRequestStatusMapper.map(paymentResponse.getRequestForPaymentStatus())).willReturn(MONEY_REQUEST_STATUS);
        setEnclosedFiles(enclosedFiles);
        assertThat(moneyRequestEntityMapper.mapForGetMoneyRequest(paymentResponse, moneyRequestContext))
                .returns(expectedInvoiceIssueDate, IncomingMoneyRequestEntity::getInvoiceIssueDate);
    }

    @Test
    void shouldMapForGetMoneyRequestFraudCheckResultScore() {
        given(timeService.getNowLocalDateTime()).willReturn(LOCAL_DATE_TIME);
        given(moneyRequestStatusMapper.map(paymentResponse.getRequestForPaymentStatus())).willReturn(MONEY_REQUEST_STATUS);
        paymentResponse.getFraudCheckResult().setScore(40);
        assertThat(moneyRequestEntityMapper.mapForGetMoneyRequest(paymentResponse, moneyRequestContext))
                .returns("40", IncomingMoneyRequestEntity::getFraudCheckResultScore);
    }

    @Test
    void shouldMapForGetMoneyRequestFraudCheckResultReason() {
        given(timeService.getNowLocalDateTime()).willReturn(LOCAL_DATE_TIME);
        given(moneyRequestStatusMapper.map(paymentResponse.getRequestForPaymentStatus())).willReturn(MONEY_REQUEST_STATUS);
        paymentResponse.getFraudCheckResult().setReason("No Reason");
        assertThat(moneyRequestEntityMapper.mapForGetMoneyRequest(paymentResponse, moneyRequestContext))
                .returns("No Reason", IncomingMoneyRequestEntity::getFraudCheckResultReason);
    }

    @Test
    void shouldUpdateForDecline_withDeclineMoneyRequestReason() {
        final String declineReason = "some decline reason";
        final String modifiedDateTime = LOCAL_DATE_TIME.format(FORMATTER);
        final DeclineSupplementaryData supplementaryData = Instancio.of(DeclineSupplementaryData.class)
                .set(field("declineReason"), declineReason)
                .create();
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        declineRequest.getFiToFICustomerCreditTransferV08()
                .getCreditTransferTransactionInformation()
                .setSupplementaryData(supplementaryData);
        given(timeService.getFormattedNowLocalDateTime()).willReturn(modifiedDateTime);
        final IncomingMoneyRequestEntity expectedEntity = incomingMoneyRequestEntity.toBuilder()
                .declineReason(declineReason)
                .sysModifiedDate(modifiedDateTime)
                .moneyRequestStatus("DECLINED")
                .build();

        final IncomingMoneyRequestEntity actualEntity = moneyRequestEntityMapper.updateForDeclineMoneyRequest(declineRequest, incomingMoneyRequestEntity);

        assertThat(actualEntity).usingRecursiveComparison().isEqualTo(expectedEntity);
    }

    private static Stream<Arguments> mobileContactProvider() {
        return Stream.of(
                Arguments.argumentSet("mobile number is null", new Contact4(), null),
                Arguments.argumentSet("mobile number is not null", new Contact4().mobileNumber("************"), "************")
        );
    }

    private static Stream<Arguments> emailContactProvider() {
        return Stream.of(
                Arguments.argumentSet("email address is null", new Contact4(), null),
                Arguments.argumentSet("email address is not null", new Contact4().emailAddress("<EMAIL>"), "<EMAIL>")
        );
    }

    private static Stream<Arguments> invoiceNumberProvider() {
        final List<Document12> emptyDocs = List.of();
        final List<Document12> docList = List.of(Instancio.create(Document12.class));
        return Stream.of(
                Arguments.argumentSet("invoice number list is empty", emptyDocs, null),
                Arguments.argumentSet("invoice number list is not empty", docList, docList.get(0).getIdentification())
        );
    }

    private static Stream<Arguments> invoiceIssueDateProvider() {
        final List<Document12> emptyDocs = Collections.emptyList();
        final List<Document12> docList = List.of(Instancio.create(Document12.class));
        return Stream.of(
                Arguments.argumentSet("invoice issue date list is empty", emptyDocs, null),
                Arguments.argumentSet("invoice issue date list is not empty", docList, docList.get(0).getIssueDate().toString())
        );
    }

    private void setContactDetails(final Contact4 contactDetails) {
        final PaymentInstruction31 paymentInformation = getPaymentInstruction(paymentResponse);
        final CreditTransferTransaction35 creditTransferTransaction = getCreditTransferTransaction(paymentInformation);
        creditTransferTransaction.getCreditor().setContactDetails(contactDetails);
    }

    private void setRemittanceInformation(final RemittanceInformation16 remittanceInformation) {
        final PaymentInstruction31 paymentInformation = getPaymentInstruction(paymentResponse);
        final CreditTransferTransaction35 creditTransferTransaction = getCreditTransferTransaction(paymentInformation);
        creditTransferTransaction.setRemittanceInformation(remittanceInformation);
    }

    private void setEnclosedFiles(final List<Document12> enclosedFiles) {
        final PaymentInstruction31 paymentInformation = getPaymentInstruction(paymentResponse);
        final CreditTransferTransaction35 creditTransferTransaction = getCreditTransferTransaction(paymentInformation);
        creditTransferTransaction.setEnclosedFile(enclosedFiles);
    }

    private IncomingMoneyRequestEntity createCommonMoneyRequestEntity() {
        return IncomingMoneyRequestEntity
                .builder()
                .interacMoneyRequestId("interacMoneyRequestId")
                .endToEndIdentification("endToEndIdentification")
                .debtorName("debtorName")
                .debtorClientId("clientId")
                .debtorInteracUserId("interacUserId")
                .amount(BigDecimal.TEN)
                .creditorInstitutionId("creditorInstitutionId")
                .currency("CAD")
                .creditorName("creditorName")
                .moneyRequestStatus(MONEY_REQUEST_STATUS.getValue())
                .expiryDate(EXPIRY_DATE.toString())
                .sysCreatedDate(LOCAL_DATE_TIME.format(FORMATTER))
                .sysModifiedDate(LOCAL_DATE_TIME.format(FORMATTER))
                .sysExpirationDate(LOCAL_DATE_TIME.plusDays(EXPIRATION_DELAY_IN_DAYS).toEpochSecond(ZoneOffset.UTC))
                .fraudCheckResultAction(FraudCheckResult.ActionEnum.ALLOW.getValue())
                .channelId("channelId")
                .channelType(ChannelType.WEB.getValue())
                .build();
    }

    private MoneyRequestContext createMoneyRequestContext() {
        return new MoneyRequestContext(
                "channelId",
                ChannelType.WEB,
                null,
                "clientId",
                "interacMoneyRequestId",
                new ActivePartyIdentifier("interacUserId", ClientType.INDIVIDUAL)
        );
    }

    private IncomingRequestForPaymentResponse createCommonPaymentResponse() {
        IncomingRequestForPaymentResponse incomingPaymentResponse = Instancio.create(IncomingRequestForPaymentResponse.class);
        final PaymentInstruction31 paymentInformation = getPaymentInstruction(incomingPaymentResponse);
        final CreditTransferTransaction35 creditTransferTransaction = getCreditTransferTransaction(paymentInformation);

        incomingPaymentResponse.setRequestForPaymentStatus(RequestForPaymentStatus.AVAILABLE);
        creditTransferTransaction.getPaymentIdentification().setEndToEndIdentification("endToEndIdentification");
        paymentInformation.getDebtor().setName("debtorName");
        paymentInformation.setExpiryDate(EXPIRY_DATE);

        creditTransferTransaction.getAmount().getInstructedAmount().setAmount(BigDecimal.TEN);
        creditTransferTransaction.getAmount().getInstructedAmount().setCurrency(ActiveOrHistoricCurrencyCode.CAD);
        creditTransferTransaction
                .getCreditorAgent()
                .getFinancialInstitutionIdentification()
                .getClearingSystemMemberIdentification()
                .setMemberIdentification("creditorInstitutionId");

        creditTransferTransaction.getCreditor().setName("creditorName");

        //Null for remaining conditional value fields.
        creditTransferTransaction.getCreditor().setContactDetails(null);
        creditTransferTransaction.setRemittanceInformation(null);
        creditTransferTransaction.setEnclosedFile(null);
        final FraudCheckResult fraudCheckResult = incomingPaymentResponse.getFraudCheckResult();
        fraudCheckResult.setAction(FraudCheckResult.ActionEnum.ALLOW);
        fraudCheckResult.setScore(null);
        fraudCheckResult.setReason(null);
        return incomingPaymentResponse;
    }

    private PaymentInstruction31 getPaymentInstruction(IncomingRequestForPaymentResponse paymentResponse) {
        return paymentResponse.getCreditorPaymentActivationRequest().getPaymentInformation()
                .get(0);
    }

    private CreditTransferTransaction35 getCreditTransferTransaction(final PaymentInstruction31 paymentInformation) {
        return paymentInformation.getCreditTransferTransaction().get(0);
    }

}