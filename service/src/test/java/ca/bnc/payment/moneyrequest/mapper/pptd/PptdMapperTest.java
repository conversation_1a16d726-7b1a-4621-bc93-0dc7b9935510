package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.util.IdGenerator;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEvent;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEventRawData;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.Entity;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.EventType;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PptdMapperTest {

    private static final String END_TO_END_BUSINESS_IDENTIFICATION = "fbf92832-0a6c-444f-af63-8e7da6bec0fe";
    private static final String INSTRUCTION_IDENTIFICATION = "instructionIdentification";

    private PptdMapper pptdMapper;

    @Mock
    private PptdRawDataMapper pptdRawDataMapper;

    @Mock
    private TimeService timeService;

    @Mock
    private IdGenerator idGenerator;

    @BeforeEach
    void setup() {
        pptdMapper = new PptdMapper(pptdRawDataMapper, timeService, idGenerator);
    }

    @AfterEach
    public void verifyNoUndesiredInteractions() {
        verifyNoMoreInteractions(pptdRawDataMapper);
    }

    @ParameterizedTest
    @MethodSource("channelTypes")
    void mapDeposit(final ChannelType channelType) {
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        final MoneyRequestContext moneyRequestContext = Instancio.of(MoneyRequestContext.class)
                .set(field("channelType"), channelType)
                .create();
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.of(IncomingMoneyRequestEntity.class)
                .set(field("endToEndIdentification"), END_TO_END_BUSINESS_IDENTIFICATION)
                .create();
        final DomesticETransferEventRawData domesticETransferEventRawData = Instancio.create(DomesticETransferEventRawData.class);
        given(idGenerator.generateInstructionIdentification()).willReturn(INSTRUCTION_IDENTIFICATION);
        given(timeService.getNowOffsetDateTime()).willReturn(OffsetDateTime.parse("2014-12-22T10:15:30Z"));
        given(pptdRawDataMapper.map(
                moneyRequestContext,
                INSTRUCTION_IDENTIFICATION,
                END_TO_END_BUSINESS_IDENTIFICATION,
                declineRequest,
                incomingMoneyRequestEntity
        )).willReturn(domesticETransferEventRawData);

        final DomesticETransferEvent actual = pptdMapper.map(declineRequest, moneyRequestContext, incomingMoneyRequestEntity);

        assertThat(actual).isNotNull();
        assertThat(actual.getInstructionIdentification()).isEqualTo(INSTRUCTION_IDENTIFICATION);
        assertThat(actual.getVersion()).isEqualTo("1.0.0");
        assertThat(actual.getEntity()).isEqualTo(Entity.DOMESTIC_ETRANSFER_PAYMENT);
        assertThat(actual.getEventType()).isEqualTo(EventType.PAYMENT_TRANSACTION_DATA_EXPORTED);
        assertThat(actual.getEventTime()).isEqualTo(OffsetDateTime.parse("2014-12-22T10:15:30Z"));
        assertThat(actual.getEndToEndBusinessIdentification()).isEqualTo(END_TO_END_BUSINESS_IDENTIFICATION);
        assertThat(actual.getChannelId()).isEqualTo(incomingMoneyRequestEntity.getChannelId());
        assertThat(actual.getClientId()).isEqualTo(moneyRequestContext.clientId());
        assertThat(actual.getMsgDefIdr()).isEqualTo("pacs.008.001.08");
        assertThat(actual.getRawData()).isEqualTo(domesticETransferEventRawData);
        ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.ChannelType expectedChannelType =
                channelType == ChannelType.WEB
                        ? ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.ChannelType.WEB
                        : ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.ChannelType.MOBILE;
        assertThat(actual.getChannelType()).isEqualTo(expectedChannelType);
    }

    private static Stream<Arguments> channelTypes() {
        return Stream.of(
                Arguments.of(ChannelType.WEB),
                Arguments.of(ChannelType.MOBILE)
        );
    }

    @Test
    void pptdChannelTypesShouldInclude_MoneyRequestChannelTypes(){
        final Set<String> moneyRequestChannelTypes = Arrays.stream(ChannelType.values()).map(Enum::name).collect(Collectors.toSet());
        final Set<String> pptdChannelTypes =
                Arrays.stream(ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.ChannelType.values()).map(Enum::name).collect(Collectors.toSet());
        assertThat(pptdChannelTypes).containsAll(moneyRequestChannelTypes);
    }

}
