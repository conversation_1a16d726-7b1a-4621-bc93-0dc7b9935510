package ca.bnc.payment.moneyrequest.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.Assertions.assertThat;

class ChannelIdUtilTest {

    private ChannelIdUtil channelIdUtil;

    @BeforeEach
    void setUp() {
        channelIdUtil = new ChannelIdUtil();
    }


    @ParameterizedTest
    @CsvSource(value = {"2777, true", "OSFIN, false", "6176, false"})
    void isBncChannelId(String channelId, boolean expected) {
        assertThat(channelIdUtil.isBncChannelId(channelId)).isEqualTo(expected);
    }

    @Test
    void testNullChannelId() {
        assertThat(channelIdUtil.isBncChannelId(null)).isFalse();
    }

}