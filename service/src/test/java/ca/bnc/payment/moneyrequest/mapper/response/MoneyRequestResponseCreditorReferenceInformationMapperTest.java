package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.CreditorReferenceType1Choice;
import ca.bnc.payment.interac_money_request.generated.model.CreditorReferenceType2;
import ca.bnc.payment.interac_money_request.generated.model.DocumentType3Code;
import ca.bnc.payment.moneyrequest.generated.model.CreditorReferenceInformation2;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;

class MoneyRequestResponseCreditorReferenceInformationMapperTest {

    private MoneyRequestResponseCreditorReferenceInformationMapper moneyRequestResponseCreditorReferenceInformationMapper;

    private static final String REFERENCE = "REF123456";
    private static final String CODE_VALUE = "SCOR";

    @BeforeEach
    void setUp() {
        moneyRequestResponseCreditorReferenceInformationMapper = new MoneyRequestResponseCreditorReferenceInformationMapper();
    }

    @Test
    void map() {
        final CreditorReferenceType1Choice creditorReferenceTypeChoice = Instancio.of(CreditorReferenceType1Choice.class)
                .set(field("code"), DocumentType3Code.fromValue(CODE_VALUE))
                .create();
        final CreditorReferenceType2 creditorReferenceType = Instancio.of(CreditorReferenceType2.class)
                .set(field("codeOrProprietary"), creditorReferenceTypeChoice)
                .create();
        final ca.bnc.payment.interac_money_request.generated.model.CreditorReferenceInformation2 creditorReferenceInformation
                = Instancio.of(ca.bnc.payment.interac_money_request.generated.model.CreditorReferenceInformation2.class)
                .set(field("reference"), REFERENCE)
                .set(field("type"), creditorReferenceType)
                .create();

        final CreditorReferenceInformation2 actual = moneyRequestResponseCreditorReferenceInformationMapper.map(creditorReferenceInformation);

        assertThat(actual)
                .isNotNull()
                .usingRecursiveComparison()
                .isEqualTo(creditorReferenceInformation);
    }

}