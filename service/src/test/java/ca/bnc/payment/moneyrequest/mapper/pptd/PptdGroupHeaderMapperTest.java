package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.GroupHeader93;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.OffsetDateTime;

import static org.assertj.core.api.Assertions.assertThat;

class PptdGroupHeaderMapperTest {

    private static final String NUMBER_OF_TRANSACTIONS = "1";

    private PptdGroupHeaderMapper pptdGroupHeaderMapper;

    @BeforeEach
    void setup() {
        pptdGroupHeaderMapper = new PptdGroupHeaderMapper();
    }

    @Test
    void map() {
        final String expectedMessageIdentification = "TEST-MESSAGE-ID";
        final OffsetDateTime expectedCreationDateTime = OffsetDateTime.parse("2023-12-15T10:30:00Z");
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        declineRequest.getFiToFICustomerCreditTransferV08().getGroupHeader().setMessageIdentification(expectedMessageIdentification);
        declineRequest.getFiToFICustomerCreditTransferV08().getGroupHeader().setCreationDateTime(expectedCreationDateTime);

        final GroupHeader93 actual = pptdGroupHeaderMapper.map(declineRequest);

        assertThat(actual.getMessageIdentification()).isEqualTo(expectedMessageIdentification);
        assertThat(actual.getCreationDatetime()).isEqualTo(expectedCreationDateTime);
        assertThat(actual.getNumberOfTransactions()).isEqualTo(NUMBER_OF_TRANSACTIONS);
    }
}