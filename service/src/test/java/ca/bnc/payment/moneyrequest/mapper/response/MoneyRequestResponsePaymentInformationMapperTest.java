package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.interac_money_request.generated.model.PaymentInstruction31;
import ca.bnc.payment.interac_money_request.generated.model.RequestForPaymentStatus;
import ca.bnc.payment.moneyrequest.generated.model.CategoryPurpose;
import ca.bnc.payment.moneyrequest.generated.model.CreditTransferTransaction;
import ca.bnc.payment.moneyrequest.generated.model.Debtor;
import ca.bnc.payment.moneyrequest.generated.model.MoneyRequestStatus;
import ca.bnc.payment.moneyrequest.generated.model.PaymentCondition;
import ca.bnc.payment.moneyrequest.generated.model.PaymentInformation;
import ca.bnc.payment.moneyrequest.mapper.common.MoneyRequestStatusMapper;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class MoneyRequestResponsePaymentInformationMapperTest {

    private MoneyRequestResponsePaymentInformationMapper moneyRequestResponsePaymentInformationMapper;

    @Mock
    private MoneyRequestResponseCategoryPurposeMapper moneyRequestResponseCategoryPurposeMapper;

    @Mock
    private MoneyRequestStatusMapper moneyRequestStatusMapper;

    @Mock
    private MoneyRequestResponsePaymentConditionMapper moneyRequestResponsePaymentConditionMapper;

    @Mock
    private MoneyRequestResponseDebtorMapper moneyRequestResponseDebtorMapper;

    @Mock
    private MoneyRequestResponseCreditTransferTransactionMapper moneyRequestResponseCreditTransferTransactionMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponsePaymentInformationMapper = new MoneyRequestResponsePaymentInformationMapper(
                moneyRequestResponseCategoryPurposeMapper,
                moneyRequestStatusMapper,
                moneyRequestResponsePaymentConditionMapper,
                moneyRequestResponseDebtorMapper,
                moneyRequestResponseCreditTransferTransactionMapper
        );
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(
                moneyRequestResponseCategoryPurposeMapper,
                moneyRequestStatusMapper,
                moneyRequestResponsePaymentConditionMapper,
                moneyRequestResponseDebtorMapper,
                moneyRequestResponseCreditTransferTransactionMapper
        );
    }

    @Test
    void map() {
        final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse = Instancio.create(IncomingRequestForPaymentResponse.class);
        final PaymentInstruction31 paymentInstruction = incomingRequestForPaymentResponse.getCreditorPaymentActivationRequest().getPaymentInformation().get(0);
        final RequestForPaymentStatus requestForPaymentStatus = incomingRequestForPaymentResponse.getRequestForPaymentStatus();

        final CategoryPurpose expectedCategoryPurpose = Instancio.create(CategoryPurpose.class);
        final MoneyRequestStatus expectedMoneyRequestStatus = Instancio.create(MoneyRequestStatus.class);
        final PaymentCondition expectedPaymentCondition = Instancio.create(PaymentCondition.class);
        final Debtor expectedDebtor = Instancio.create(Debtor.class);
        final CreditTransferTransaction expectedCreditTransferTransaction = Instancio.create(CreditTransferTransaction.class);
        given(moneyRequestResponseCategoryPurposeMapper.map(paymentInstruction)).willReturn(expectedCategoryPurpose);
        given(moneyRequestStatusMapper.map(requestForPaymentStatus)).willReturn(expectedMoneyRequestStatus);
        given(moneyRequestResponsePaymentConditionMapper.map(paymentInstruction.getPaymentCondition())).willReturn(expectedPaymentCondition);
        given(moneyRequestResponseDebtorMapper.map(paymentInstruction.getDebtor())).willReturn(expectedDebtor);
        given(moneyRequestResponseCreditTransferTransactionMapper.map(paymentInstruction.getCreditTransferTransaction().get(0))).willReturn(expectedCreditTransferTransaction);

        final PaymentInformation actual = moneyRequestResponsePaymentInformationMapper.map(incomingRequestForPaymentResponse);

        assertThat(actual)
                .isNotNull()
                .satisfies(paymentInfo -> {
                    assertThat(paymentInfo.getCategoryPurpose()).isEqualTo(expectedCategoryPurpose);
                    assertThat(paymentInfo.getExpiryDate()).isEqualTo(paymentInstruction.getExpiryDate());
                    assertThat(paymentInfo.getMoneyRequestStatus()).isEqualTo(expectedMoneyRequestStatus);
                    assertThat(paymentInfo.getPaymentCondition()).isEqualTo(expectedPaymentCondition);
                    assertThat(paymentInfo.getDebtor()).isEqualTo(expectedDebtor);
                    assertThat(paymentInfo.getCreditTransferTransaction()).isEqualTo(expectedCreditTransferTransaction);
                });

        then(moneyRequestResponseCategoryPurposeMapper).should().map(paymentInstruction);
        then(moneyRequestStatusMapper).should().map(requestForPaymentStatus);
        then(moneyRequestResponsePaymentConditionMapper).should().map(paymentInstruction.getPaymentCondition());
        then(moneyRequestResponseDebtorMapper).should().map(paymentInstruction.getDebtor());
        then(moneyRequestResponseCreditTransferTransactionMapper).should().map(paymentInstruction.getCreditTransferTransaction().get(0));
    }
}