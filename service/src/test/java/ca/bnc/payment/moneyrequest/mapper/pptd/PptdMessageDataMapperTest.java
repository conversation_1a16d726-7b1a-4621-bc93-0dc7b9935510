package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEventRawDataMessageData;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.FIToFICustomerCreditTransfer;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;

@ExtendWith(MockitoExtension.class)
class PptdMessageDataMapperTest {

    private PptdMessageDataMapper pptdMessageDataMapper;

    @Mock
    private PptdFIToFICustomerCreditTransferMapper pptdFIToFICustomerCreditTransferMapper;

    @BeforeEach
    void setup() {
        pptdMessageDataMapper = new PptdMessageDataMapper(pptdFIToFICustomerCreditTransferMapper);
    }

    @Test
    void map() {
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.create(IncomingMoneyRequestEntity.class);
        final FIToFICustomerCreditTransfer fiToFICustomerCreditTransfer = Instancio.create(FIToFICustomerCreditTransfer.class);
        given(pptdFIToFICustomerCreditTransferMapper.map(declineRequest, incomingMoneyRequestEntity)).willReturn(fiToFICustomerCreditTransfer);

        final DomesticETransferEventRawDataMessageData actual = pptdMessageDataMapper.map(declineRequest, incomingMoneyRequestEntity);

        assertThat(actual.getFiToFiCustomerCreditTransfer()).isSameAs(fiToFICustomerCreditTransfer);
    }
}