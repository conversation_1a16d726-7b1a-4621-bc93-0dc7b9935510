package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.OrganisationIdentificationSchemeName1Choice;
import ca.bnc.payment.moneyrequest.generated.model.Party38Choice;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MoneyRequestResponsePartyIdentificationMapperTest {

    private MoneyRequestResponsePartyIdentificationMapper moneyRequestResponsePartyIdentificationMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponsePartyIdentificationMapper = new MoneyRequestResponsePartyIdentificationMapper();
    }

    @Test
    void map() {
        final ca.bnc.payment.interac_money_request.generated.model.Party38Choice party =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.Party38Choice.class);
        final ca.bnc.payment.interac_money_request.generated.model.OrganisationIdentification29 organisationIdentification =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.OrganisationIdentification29.class);
        final ca.bnc.payment.interac_money_request.generated.model.GenericOrganisationIdentification1 genericOrganisationIdentification =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.GenericOrganisationIdentification1.class);
        final ca.bnc.payment.interac_money_request.generated.model.OrganisationIdentificationSchemeName1Choice inputSchemeName =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.OrganisationIdentificationSchemeName1Choice.class);
        genericOrganisationIdentification.setSchemeName(inputSchemeName);
        organisationIdentification.setOther(List.of(genericOrganisationIdentification));
        party.setOrganisationIdentification(organisationIdentification);

        final Party38Choice actual = moneyRequestResponsePartyIdentificationMapper.map(party);

        assertThat(actual).isNotNull();
        assertThat(actual.getOrganisationIdentification()).isNotNull();
        assertThat(actual.getOrganisationIdentification().getOther()).isNotNull();
        assertThat(actual.getOrganisationIdentification().getOther().getIdentification())
                .isEqualTo(genericOrganisationIdentification.getIdentification());
        assertThat(actual.getOrganisationIdentification().getOther().getSchemeName()).isNotNull();
        assertThat(actual.getOrganisationIdentification().getOther().getSchemeName().getCode())
                .isEqualTo(OrganisationIdentificationSchemeName1Choice.CodeEnum.fromValue(
                        inputSchemeName.getCode().getValue()));
    }

    @Test
    void map_withEmptyList() {
        final ca.bnc.payment.interac_money_request.generated.model.Party38Choice party =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.Party38Choice.class);
        final ca.bnc.payment.interac_money_request.generated.model.OrganisationIdentification29 organisationIdentification =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.OrganisationIdentification29.class);
        organisationIdentification.setOther(List.of());
        party.setOrganisationIdentification(organisationIdentification);

        final Party38Choice actual = moneyRequestResponsePartyIdentificationMapper.map(party);

        assertThat(actual).isNotNull();
        assertThat(actual.getOrganisationIdentification()).isNotNull();
        assertThat(actual.getOrganisationIdentification().getOther()).isNotNull();
        assertThat(actual.getOrganisationIdentification().getOther().getIdentification()).isNull();
        assertThat(actual.getOrganisationIdentification().getOther().getSchemeName()).isNull();
    }
}