package ca.bnc.payment.moneyrequest.repository;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.exception.ApiCallTimeoutException;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;

import java.util.stream.Stream;

import static ca.bnc.payment.moneyrequest.constant.Constant.DYNAMODB_BAD_REQUEST;
import static ca.bnc.payment.moneyrequest.constant.Constant.DYNAMODB_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.DYNAMODB_UNEXPECTED_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.DYNAMODB_UNREACHABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class IncomingMoneyRequestRepositoryTest {

    private static final IncomingMoneyRequestEntity ENTITY = Instancio.create(IncomingMoneyRequestEntity.class);
    private static final SdkClientException EXCEPTION = SdkClientException.create(DYNAMODB_UNEXPECTED_ERROR);
    private static final String USER_ID = Instancio.create(String.class);
    private static final Key KEY = Key.builder().partitionValue(USER_ID).build();
    private static final MoneyRequestException MONEY_REQUEST_UNEXPECTED_EXCEPTION = MoneyRequestException
            .internalServerError(TECHNICAL_ERROR_CODE, DYNAMODB_UNEXPECTED_ERROR, SERVICE_ORIGIN, NA);
    private static final MoneyRequestException MONEY_REQUEST_TIMEOUT_EXCEPTION = MoneyRequestException
            .internalServerError(TECHNICAL_ERROR_CODE, DYNAMODB_UNREACHABLE, SERVICE_ORIGIN, NA);
    private static final DynamoDbException DYNAMO_DB_EXCEPTION_500 = Instancio.of(DynamoDbException.class)
            .set(field(DynamoDbException::statusCode), 500)
            .create();
    private static final DynamoDbException DYNAMO_DB_EXCEPTION_400 = Instancio.of(DynamoDbException.class)
            .set(field(DynamoDbException::statusCode), 400)
            .create();


    private IncomingMoneyRequestRepository testee;

    @Mock
    private DynamoDbTable<IncomingMoneyRequestEntity> incomingMoneyRequestTable;

    @BeforeEach
    void setUp() {
        testee = new IncomingMoneyRequestRepository(incomingMoneyRequestTable);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(incomingMoneyRequestTable);
    }

    @Test
    void whenSave_thenSuccess() {
        IncomingMoneyRequestEntity actual = testee.save(ENTITY);
        then(incomingMoneyRequestTable).should().putItem(ENTITY);
        assertThat(actual).isSameAs(ENTITY);
    }

    @Test
    void givenException_whenSave_thenThrowIncomingMoneyRequestException() {
        willThrow(EXCEPTION)
                .given(incomingMoneyRequestTable)
                .putItem(ENTITY);
        MoneyRequestException moneyRequestException = MoneyRequestException.internalServerError(TECHNICAL_ERROR_CODE, DYNAMODB_UNEXPECTED_ERROR,
                SERVICE_ORIGIN, NA);

        assertThatThrownBy(() -> testee.save(ENTITY))
                .usingRecursiveComparison()
                .isEqualTo(moneyRequestException);
    }

    @Test
    void givenApiCallTimeoutException_whenSave_thenThrowMoneyRequestException_() {
        ApiCallTimeoutException apiCallTimeoutException = Instancio.create(ApiCallTimeoutException.class);

        willThrow(apiCallTimeoutException)
                .given(incomingMoneyRequestTable)
                .putItem(ENTITY);

        assertThatThrownBy(() -> testee.save(ENTITY))
                .usingRecursiveComparison()
                .isEqualTo(MONEY_REQUEST_TIMEOUT_EXCEPTION);
    }

    //    @ParameterizedTest(name = "thrownException = {0}, expectedErrorText = {1}")
    @MethodSource("dynamoDbSaveExceptionProvider")
    @ParameterizedTest(name = "{argumentSetName}, expectedException = {0}, text = {1}")
    void givenDynamoDbException_whenSave_thenThrowMoneyRequestException(
            DynamoDbException dynamoDbException,
            String expectedErrorText
    ) {
        MoneyRequestException expectedException = MoneyRequestException.internalServerError(
                TECHNICAL_ERROR_CODE, expectedErrorText, SERVICE_ORIGIN, NA);

        willThrow(dynamoDbException)
                .given(incomingMoneyRequestTable)
                .putItem(ENTITY);

        assertThatThrownBy(() -> testee.save(ENTITY))
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }

    @Test
    void whenRetrieve_thenSuccess() {
        given(incomingMoneyRequestTable.getItem(KEY)).willReturn(ENTITY);

        IncomingMoneyRequestEntity actual = testee.retrieve(USER_ID);

        assertThat(actual).isEqualTo(ENTITY);
    }

    @Test
    void givenException_whenRetrieve_thenThrowMoneyRequestException() {
        willThrow(EXCEPTION)
                .given(incomingMoneyRequestTable)
                .getItem(KEY);

        assertThatThrownBy(() -> testee.retrieve(USER_ID))
                .usingRecursiveComparison()
                .isEqualTo(MONEY_REQUEST_UNEXPECTED_EXCEPTION);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("dynamoDbExceptionProvider")
    void givenDynamoDbException_whenRetrieve_thenThrowAppropriateException(final Exception thrownException, final String expectedErrorText) {
        final MoneyRequestException expected = MoneyRequestException.internalServerError(
                TECHNICAL_ERROR_CODE, expectedErrorText, SERVICE_ORIGIN, NA);

        willThrow(thrownException)
                .given(incomingMoneyRequestTable)
                .getItem(KEY);

        assertThatThrownBy(() -> testee.retrieve(USER_ID)).isInstanceOfSatisfying(
                MoneyRequestException.class,
                exception -> assertThat(exception).usingRecursiveComparison().isEqualTo(expected));
    }

    @Test
    void givenException_whenRetrieve_thenThrowDynamoDBExceptionWithHTTPcode111() {
        DynamoDbException dynamoDBException = Instancio.of(DynamoDbException.class)
                .set(field(DynamoDbException::statusCode), 111)
                .create();
        willThrow(dynamoDBException)
                .given(incomingMoneyRequestTable)
                .getItem(KEY);

        assertThatThrownBy(() -> testee.retrieve(USER_ID)).isInstanceOfSatisfying(
                DynamoDbException.class,
                e -> assertThat(e).isEqualTo(dynamoDBException));
    }

    private static Stream<Arguments> dynamoDbExceptionProvider() {
        ApiCallTimeoutException dynamoDbTimeout = Instancio.create(ApiCallTimeoutException.class);
        return Stream.of(
                Arguments.argumentSet("Throws DynamoDBException Timeout Exception", dynamoDbTimeout, DYNAMODB_UNREACHABLE),
                Arguments.argumentSet("Throws DynamoDBException Internal Server Error 500", DYNAMO_DB_EXCEPTION_500, DYNAMODB_INTERNAL_SERVER_ERROR),
                Arguments.argumentSet("Throws DynamoDBExceptionBadRequest Error 400", DYNAMO_DB_EXCEPTION_400, DYNAMODB_BAD_REQUEST)
        );
    }

    private static Stream<Arguments> dynamoDbSaveExceptionProvider() {
        return Stream.of(
                Arguments.argumentSet("DynamoDBException returns Internal Server Error 500", DYNAMO_DB_EXCEPTION_500, DYNAMODB_INTERNAL_SERVER_ERROR),
                Arguments.argumentSet("DynamoDBException returns BadRequest Error 400", DYNAMO_DB_EXCEPTION_400, DYNAMODB_BAD_REQUEST)
        );
    }
}
