package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.ActiveOrHistoricCurrencyCode;
import ca.bnc.payment.interac_money_request.generated.model.AmountType4Choice;
import ca.bnc.payment.moneyrequest.generated.model.Amount;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

class MoneyRequestResponseAmountMapperTest {

    private MoneyRequestResponseAmountMapper moneyRequestResponseAmountMapper;

    private static final String CURRENCY = "CAD";
    private static final BigDecimal AMOUNT = new BigDecimal("100.00");

    @BeforeEach
    void setUp() {
        moneyRequestResponseAmountMapper = new MoneyRequestResponseAmountMapper();
    }

    @Test
    void map() {
        final AmountType4Choice source = Instancio.create(AmountType4Choice.class);
        source.getInstructedAmount().setAmount(AMOUNT);
        source.getInstructedAmount().setCurrency(ActiveOrHistoricCurrencyCode.fromValue(CURRENCY));

        final Amount actual = moneyRequestResponseAmountMapper.map(source);

        assertThat(actual.getInstructedAmount()).isEqualTo(AMOUNT);
        assertThat(actual.getCurrency()).isEqualTo(CURRENCY);
    }

}