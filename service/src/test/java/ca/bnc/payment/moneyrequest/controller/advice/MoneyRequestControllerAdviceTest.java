package ca.bnc.payment.moneyrequest.controller.advice;

import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.exception.RetryableMoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.Error;
import ca.bnc.payment.moneyrequest.generated.model.Errors;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ch.qos.logback.classic.Logger;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.LoggerFactory;
import org.springframework.http.ProblemDetail;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import static ca.bnc.payment.moneyrequest.constant.Constant.GENERIC_ERROR_LOG;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERNAL_SERVER_ERROR_GENERIC;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.REQUEST_INVALID_CODE;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;
import static org.springframework.http.HttpStatus.NOT_FOUND;

@ExtendWith(MockitoExtension.class)
class MoneyRequestControllerAdviceTest {

    private static final Logger LOGGER = (Logger) LoggerFactory.getLogger(MoneyRequestControllerAdvice.class.getName());
    private static final String EXCEPTION_MESSAGE = "EXCEPTION_MESSAGE";

    @Mock
    private LoggingFacade loggingFacade;

    private MoneyRequestControllerAdvice testee;

    @BeforeEach
    void setup() {
        testee = new MoneyRequestControllerAdvice(loggingFacade);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(loggingFacade);
    }

    @Test
    void givenMoneyRequestException_thenReturnCorrespondingStatusCode() {
        final Error expectedError = new Error().code(REQUEST_INVALID_CODE).text(EXCEPTION_MESSAGE).origin(SERVICE_ORIGIN).rule(NA);
        final MissingRequestHeaderException missingRequestHeaderException = mock(MissingRequestHeaderException.class);
        given(missingRequestHeaderException.getMessage()).willReturn(EXCEPTION_MESSAGE);

        final ResponseEntity<Errors> actualResponse = testee.handleMissingRequestHeaderException(missingRequestHeaderException);

        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(BAD_REQUEST);
                    assertThat(response.getBody())
                            .isInstanceOfSatisfying(Errors.class, error -> assertThat(error.getErrors())
                                    .hasSize(1)
                                    .first()
                                    .isEqualTo(expectedError));
                });
        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, REQUEST_INVALID_CODE, EXCEPTION_MESSAGE, SERVICE_ORIGIN, NA),
                missingRequestHeaderException);
        assertThat(actualResponse.getBody()).isNotNull();
    }

    @Test
    void given_MoneyRequestException_ErrorNotNull() {
        final Error expectedError = new Error().code(TECHNICAL_ERROR_CODE).text(EXCEPTION_MESSAGE).origin(SERVICE_ORIGIN).rule(NA);
        final RetryableMoneyRequestException retryablePartyException = new RetryableMoneyRequestException(
                TECHNICAL_ERROR_CODE,
                EXCEPTION_MESSAGE,
                SERVICE_ORIGIN,
                NA);

        final ResponseEntity<Errors> actualResponse = testee.handleIncomingMoneyRequestException(retryablePartyException);

        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(INTERNAL_SERVER_ERROR);
                    assertThat(response.getBody())
                            .isInstanceOfSatisfying(Errors.class, error -> assertThat(error.getErrors())
                                    .hasSize(1)
                                    .first()
                                    .isEqualTo(expectedError));
                });
        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, TECHNICAL_ERROR_CODE, EXCEPTION_MESSAGE, SERVICE_ORIGIN, NA),
                retryablePartyException);
    }

    @Test
    void givenAny_MoneyRequestException_NullError() {

        final MoneyRequestException moneyRequestException = MoneyRequestException.notFound(null);

        final ResponseEntity<Errors> actualResponse = testee.handleIncomingMoneyRequestException(moneyRequestException);

        assertThat(actualResponse)
                .returns(null, ResponseEntity::getBody)
                .returns(NOT_FOUND, ResponseEntity::getStatusCode);

        then(loggingFacade).should().error(
                LOGGER,
                moneyRequestException);
    }

    @Test
    void givenMethodArgumentTypeMismatchException_thenReturnBadRequestError() {
        final MethodArgumentTypeMismatchException exception = mock(MethodArgumentTypeMismatchException.class);
        given(exception.getValue()).willReturn("The_Value");
        given(exception.getName()).willReturn("The_Param");
        final String errorMessage = "Mismatch value The_Value for The_Param";
        final Error expectedError = new Error()
                .code(REQUEST_INVALID_CODE)
                .text(errorMessage)
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        final ResponseEntity<Errors> actualResponse = testee.handleMethodArgumentTypeMismatchException(exception);

        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(BAD_REQUEST);
                    assertThat(response.getBody())
                            .isInstanceOfSatisfying(Errors.class, error -> assertThat(error.getErrors())
                                    .hasSize(1)
                                    .first()
                                    .isEqualTo(expectedError));
                });
        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, REQUEST_INVALID_CODE, errorMessage, SERVICE_ORIGIN, NA),
                exception);
    }

    @Test
    void givenMethodArgumentNotValidException_WithFieldError_thenReturnBadRequestError() {
        final FieldError fieldError = Instancio.create(FieldError.class);
        final String errorMessage = String.format("The %s %s", fieldError.getField(), fieldError.getDefaultMessage());

        final MethodArgumentNotValidException exception = mock(MethodArgumentNotValidException.class);
        given(exception.getFieldError()).willReturn(fieldError);
        final Error expectedError = new Error()
                .code(REQUEST_INVALID_CODE)
                .text(errorMessage)
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        final ResponseEntity<Errors> actualResponse = testee.handleMethodArgumentNotValidException(exception);

        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(BAD_REQUEST);
                    assertThat(response.getBody())
                            .isInstanceOfSatisfying(Errors.class, error -> assertThat(error.getErrors())
                                    .hasSize(1)
                                    .first()
                                    .isEqualTo(expectedError));
                });
        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, REQUEST_INVALID_CODE, errorMessage, SERVICE_ORIGIN, NA),
                exception);
    }

    @Test
    void givenMethodArgumentNotValidException_WithoutFieldError_thenReturnBadRequestError() {
        final String errorMessage = "any error message";
        final MethodArgumentNotValidException exception = mock(MethodArgumentNotValidException.class);
        given(exception.getMessage()).willReturn(errorMessage);
        final Error expectedError = new Error()
                .code(REQUEST_INVALID_CODE)
                .text(errorMessage)
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        final ResponseEntity<Errors> actualResponse = testee.handleMethodArgumentNotValidException(exception);

        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(BAD_REQUEST);
                    assertThat(response.getBody())
                            .isInstanceOfSatisfying(Errors.class, error -> assertThat(error.getErrors())
                                    .hasSize(1)
                                    .first()
                                    .isEqualTo(expectedError));
                });
        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, REQUEST_INVALID_CODE, errorMessage, SERVICE_ORIGIN, NA),
                exception);
    }

    @Test
    void givenHttpMediaTypeNotAcceptableException_WithProblemDetail_thenReturnBadRequestError() {
        final ProblemDetail problemDetail = Instancio.create(ProblemDetail.class);
        final String errorMessage = problemDetail.getDetail();

        final HttpMediaTypeNotAcceptableException exception = mock(HttpMediaTypeNotAcceptableException.class);
        given(exception.getBody()).willReturn(problemDetail);
        final Error expectedError = new Error()
                .code(REQUEST_INVALID_CODE)
                .text(errorMessage)
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        final ResponseEntity<Errors> actualResponse = testee.handleHttpMediaTypeNotAcceptableException(exception);

        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(BAD_REQUEST);
                    assertThat(response.getBody())
                            .isInstanceOfSatisfying(Errors.class, error -> assertThat(error.getErrors())
                                    .hasSize(1)
                                    .first()
                                    .isEqualTo(expectedError));
                });
        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, REQUEST_INVALID_CODE, errorMessage, SERVICE_ORIGIN, NA),
                exception);
    }

    @Test
    void givenHttpMediaTypeNotAcceptableException_WithoutProblemDetail_thenReturnBadRequestError() {
        final ProblemDetail problemDetail = Instancio.of(ProblemDetail.class)
                .set(field(ProblemDetail::getDetail), null)
                .create();
        final String errorMessage = "Wrong Content-Type or Accept Header";

        final HttpMediaTypeNotAcceptableException exception = mock(HttpMediaTypeNotAcceptableException.class);
        given(exception.getBody()).willReturn(problemDetail);
        final Error expectedError = new Error()
                .code(REQUEST_INVALID_CODE)
                .text(errorMessage)
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        final ResponseEntity<Errors> actualResponse = testee.handleHttpMediaTypeNotAcceptableException(exception);

        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(BAD_REQUEST);
                    assertThat(response.getBody())
                            .isInstanceOfSatisfying(Errors.class, error -> assertThat(error.getErrors())
                                    .hasSize(1)
                                    .first()
                                    .isEqualTo(expectedError));
                });
        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, REQUEST_INVALID_CODE, errorMessage, SERVICE_ORIGIN, NA),
                exception);
    }

    @Test
    void givenHttpMessageNotReadableException_thenReturnBadRequestError() {
        final String errorMessage = "Json Payload Parsing Error";

        final HttpMessageNotReadableException exception = mock(HttpMessageNotReadableException.class);
        given(exception.getMessage()).willReturn(errorMessage);
        final Error expectedError = new Error()
                .code(REQUEST_INVALID_CODE)
                .text(errorMessage)
                .origin(SERVICE_ORIGIN)
                .rule(NA);
        final ResponseEntity<Errors> actualResponse = testee.handleHttpMessageNotReadableException(exception);

        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(BAD_REQUEST);
                    assertThat(response.getBody())
                            .isInstanceOfSatisfying(Errors.class, error -> assertThat(error.getErrors())
                                    .hasSize(1)
                                    .first()
                                    .isEqualTo(expectedError));
                });
        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, REQUEST_INVALID_CODE, errorMessage, SERVICE_ORIGIN, NA),
                exception);
    }

    @Test
    void givenGenericException_thenReturnInternalServerError() {
        final Error expectedError = new Error().code(TECHNICAL_ERROR_CODE).text(INTERNAL_SERVER_ERROR_GENERIC).origin(SERVICE_ORIGIN).rule(NA);
        final Exception exception = mock(Exception.class);

        final ResponseEntity<Errors> actualResponse = testee.handleOtherException(exception);

        assertThat(actualResponse)
                .satisfies(response -> {
                    assertThat(response.getStatusCode()).isEqualTo(INTERNAL_SERVER_ERROR);
                    assertThat(response.getBody())
                            .isInstanceOfSatisfying(Errors.class, error -> assertThat(error.getErrors())
                                    .hasSize(1)
                                    .first()
                                    .isEqualTo(expectedError));
                });
        then(loggingFacade).should().error(
                LOGGER,
                String.format(GENERIC_ERROR_LOG, TECHNICAL_ERROR_CODE, INTERNAL_SERVER_ERROR_GENERIC, SERVICE_ORIGIN, NA),
                exception);
    }
}
