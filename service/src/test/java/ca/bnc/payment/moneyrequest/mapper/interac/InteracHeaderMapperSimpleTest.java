package ca.bnc.payment.moneyrequest.mapper.interac;

import ca.bnc.payment.interac_money_request.generated.model.ChannelIndicator;
import ca.bnc.payment.interac_money_request.generated.model.DeclineRequestForPaymentRequest;
import ca.bnc.payment.interac_money_request.generated.model.SignatureType;
import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.model.ActivePartyIdentifier;
import ca.bnc.payment.moneyrequest.model.InteracHeader;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.util.ParticipantIdUtil;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ca.nbc.payment.pmt_security_library.interac.InteracEtransferClientTokenManager;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;

@ExtendWith(MockitoExtension.class)
class InteracHeaderMapperSimpleTest {

    private static final String PARTICIPANT_ID = "direct-id";
    private static final String PARTICIPANT_USER_ID = "activeIdentifier";
    private static final String INDIRECT_CONNECTOR_ID = "indirect-id";
    private static final String ACCESS_TOKEN = "access-token";
    private static final String CHANNEL_ID = "xChannelId";
    private static final String CLIENT_ID = "xClientId";
    private static final String INTERAC_MONEY_REQUEST_ID = "interacMoneyRequestId";
    private static final UUID REQUEST_ID = UUID.randomUUID();
    private static final OffsetDateTime NOW = OffsetDateTime.now();

    private InteracHeaderMapper interacHeaderMapper;

    @Mock
    private ParticipantIdUtil participantIdUtil;

    @Mock
    private InteracEtransferClientTokenManager interacEtransferClientTokenManager;

    @Mock
    private TimeService timeService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private LoggingFacade loggingFacade;

    @BeforeEach
    void setUp() {
        interacHeaderMapper = new InteracHeaderMapper(
                participantIdUtil,
                interacEtransferClientTokenManager,
                timeService,
                objectMapper,
                loggingFacade);
    }

    @Test
    void testMapForGet_ShouldReturnCorrectHeader() {
        // Given
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext(ChannelType.WEB, ClientType.INDIVIDUAL);
        
        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(PARTICIPANT_ID);
        given(interacEtransferClientTokenManager.getTokenAccess(null, NOW)).willReturn(ACCESS_TOKEN);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);

        // When
        final InteracHeader actual = interacHeaderMapper.mapForGet(moneyRequestContext);

        // Then
        assertThat(actual).isNotNull();
        assertThat(actual.xEtParticipantId()).isEqualTo(PARTICIPANT_ID);
        assertThat(actual.xEtParticipantUserId()).isEqualTo(PARTICIPANT_USER_ID);
        assertThat(actual.xEtIndirectConnectorId()).isNull(); // Individual client type
        assertThat(actual.authorization()).isNull();
        assertThat(actual.xEtRequestId()).isEqualTo(REQUEST_ID.toString());
        assertThat(actual.xEtChannelIndicator()).isEqualTo(ChannelIndicator.ONLINE);
        assertThat(actual.xEtApiSignature()).isEqualTo(ACCESS_TOKEN);
        assertThat(actual.xEtApiSignatureType()).isEqualTo(SignatureType.PAYLOAD_DIGEST_SHA256);
        assertThat(actual.xEtTransactionTime()).isEqualTo(NOW);
    }

    @Test
    void testMapForDecline_ShouldReturnCorrectHeader() throws Exception {
        // Given
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext(ChannelType.MOBILE, ClientType.ORGANISATION);
        final DeclineRequestForPaymentRequest declineRequest = new DeclineRequestForPaymentRequest();
        final String httpPayload = "{\"decline_reason\":\"test\"}";
        
        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(PARTICIPANT_ID);
        given(participantIdUtil.getIndirectConnectorIdentifier()).willReturn(INDIRECT_CONNECTOR_ID);
        given(objectMapper.writeValueAsString(declineRequest)).willReturn(httpPayload);
        given(interacEtransferClientTokenManager.getTokenAccess(httpPayload, NOW)).willReturn(ACCESS_TOKEN);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);

        // When
        final InteracHeader actual = interacHeaderMapper.mapForDecline(moneyRequestContext, declineRequest);

        // Then
        assertThat(actual).isNotNull();
        assertThat(actual.xEtParticipantId()).isEqualTo(PARTICIPANT_ID);
        assertThat(actual.xEtParticipantUserId()).isEqualTo(PARTICIPANT_USER_ID);
        assertThat(actual.xEtIndirectConnectorId()).isEqualTo(INDIRECT_CONNECTOR_ID); // Organisation client type
        assertThat(actual.authorization()).isNull();
        assertThat(actual.xEtRequestId()).isEqualTo(REQUEST_ID.toString());
        assertThat(actual.xEtChannelIndicator()).isEqualTo(ChannelIndicator.MOBILE);
        assertThat(actual.xEtApiSignature()).isEqualTo(ACCESS_TOKEN);
        assertThat(actual.xEtApiSignatureType()).isEqualTo(SignatureType.PAYLOAD_DIGEST_SHA256);
        assertThat(actual.xEtTransactionTime()).isEqualTo(NOW);
    }

    private MoneyRequestContext buildMoneyRequestContext(final ChannelType channelType, final ClientType clientType) {
        final ActivePartyIdentifier activePartyIdentifier = new ActivePartyIdentifier(PARTICIPANT_USER_ID, clientType);
        return MoneyRequestContext.builder()
                .channelId(CHANNEL_ID)
                .channelType(channelType)
                .requestId(REQUEST_ID)
                .clientId(CLIENT_ID)
                .interacMoneyRequestId(INTERAC_MONEY_REQUEST_ID)
                .activePartyIdentifier(activePartyIdentifier)
                .build();
    }
}
