package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.PaymentIdentification6;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;

class MoneyRequestResponsePaymentIdentificationMapperTest {

    private MoneyRequestResponsePaymentIdentificationMapper moneyRequestResponsePaymentIdentificationMapper;

    private static final String INSTRUCTION_IDENTIFICATION = "ec6e707c92024f42bb6be5ab33dedf2d";
    private static final String END_TO_END_IDENTIFICATION = "a098bdb2-545e-4f0a-9055-a43723aef57";

    @BeforeEach
    void setUp() {
        moneyRequestResponsePaymentIdentificationMapper = new MoneyRequestResponsePaymentIdentificationMapper();
    }

    @Test
    void map() {
        final ca.bnc.payment.interac_money_request.generated.model.PaymentIdentification6 source =
                Instancio.of(ca.bnc.payment.interac_money_request.generated.model.PaymentIdentification6.class)
                .set(field("instructionIdentification"), INSTRUCTION_IDENTIFICATION)
                .set(field("endToEndIdentification"), END_TO_END_IDENTIFICATION)
                .create();

        final PaymentIdentification6 actual = moneyRequestResponsePaymentIdentificationMapper.map(source);

        assertThat(actual)
                .isNotNull()
                .usingRecursiveComparison()
                .isEqualTo(source);
    }
}