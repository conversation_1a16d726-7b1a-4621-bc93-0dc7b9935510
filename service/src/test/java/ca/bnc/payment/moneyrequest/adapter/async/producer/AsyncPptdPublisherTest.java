package ca.bnc.payment.moneyrequest.adapter.async.producer;


import ca.bnc.payment.kafkaproducer.KafkaProducer;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEvent;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class AsyncPptdPublisherTest {

    private static final LogContextHolder.LogContext LOG_CONTEXT = Instancio.create(LogContextHolder.LogContext.class);
    private static final DomesticETransferEvent DOMESTIC_ETRANSFER_EVENT = Instancio.create(DomesticETransferEvent.class);
    private static final String TOPIC = "my-topic";
    private static final String LOG_MESSAGING_ID = "PPTD";
    private static final UUID CORRELATION_ID = UUID.fromString("d9a76211-e4cd-4e5f-8b92-d1b85f702449");

    @Mock
    private KafkaProducer kafkaProducer;
    @Mock
    private LogContextHolder logContextHolder;
    @Captor
    private ArgumentCaptor<Runnable> runnableCaptor;

    private AsyncPptdPublisher asyncPptdPublisher;

    @BeforeEach
    void setUp() {
        asyncPptdPublisher = new AsyncPptdPublisher(TOPIC, kafkaProducer, logContextHolder);
    }

    @AfterEach
    void tear() {
        verifyNoMoreInteractions(kafkaProducer, logContextHolder);
    }

    @Test
    void testSendMessage() {
        asyncPptdPublisher.sendMessage(DOMESTIC_ETRANSFER_EVENT, CORRELATION_ID, LOG_CONTEXT);

        then(logContextHolder).should().runWithContext(runnableCaptor.capture(), eq(LOG_CONTEXT));
        assertThat(runnableCaptor.getAllValues())
                .singleElement()
                .satisfies(runnable -> assertThatCode(runnable::run).doesNotThrowAnyException());
        then(kafkaProducer).should().doSendMessage(
                eq(new ca.bnc.payment.model.KafkaMessage<>(
                        TOPIC,
                        DOMESTIC_ETRANSFER_EVENT,
                        Map.of("action", "POST"),
                        CORRELATION_ID.toString(),
                        LOG_MESSAGING_ID))
        );
    }
}