package ca.bnc.payment.moneyrequest.adapter;

import ca.bnc.payment.moneyrequest.client.party.PartyApiClient;
import ca.bnc.payment.moneyrequest.exception.RetryableMoneyRequestException;
import ca.nbc.payment.pmtpartnersparty.model.IdentifierStatus;
import ca.nbc.payment.pmtpartnersparty.model.IdentifierType;
import ca.nbc.payment.pmtpartnersparty.model.Identifiers;
import feign.RetryableException;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_API_UNAVAILABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.PARTY_SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PartyAdapterTest {

    private static final String X_CHANNEL_ID = Instancio.create(String.class);
    private static final String ACCEPT_VERSION = Instancio.create(String.class);
    private static final String X_REQUEST_ID = Instancio.create(String.class);
    private static final String CLIENT_ID = Instancio.create(String.class);
    private static final List<IdentifierType> IDENTIFIER_TYPES = Instancio.createList(IdentifierType.class);
    private static final IdentifierStatus STATUS = Instancio.create(IdentifierStatus.class);
    private static final ResponseEntity<Identifiers> RESPONSE = Instancio.create(new TypeToken<>() {});

    private PartyAdapter instance;
    @Mock
    private PartyApiClient partyApiClient;

    @BeforeEach
    void setUp() {
        instance = new PartyAdapter(partyApiClient);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(partyApiClient);
    }

    @Test
    void whenGetPartyClientById_thenReturnResponseBody() {
        given(partyApiClient.getPartyIdentifierById(X_CHANNEL_ID,
                ACCEPT_VERSION,
                X_REQUEST_ID,
                CLIENT_ID,
                IDENTIFIER_TYPES,
                STATUS)).willReturn(RESPONSE);

        final Identifiers result = instance.getPartyClientById(X_CHANNEL_ID, ACCEPT_VERSION, X_REQUEST_ID, CLIENT_ID, IDENTIFIER_TYPES, STATUS);

        final Identifiers identifiers = RESPONSE.getBody();
        assertThat(result).isEqualTo(identifiers);
    }

    @Test
    void whenGetPartyClientByIdUnReachable_thenThrowRetryableMoneyRequestException() {
        final RetryableException retryableException = Instancio.create(RetryableException.class);
        final RetryableMoneyRequestException expectedRetryableException = new RetryableMoneyRequestException(
                TECHNICAL_ERROR_CODE,
                PARTY_API_UNAVAILABLE,
                PARTY_SERVICE_ORIGIN,
                NA
        );
        given(partyApiClient.getPartyIdentifierById(X_CHANNEL_ID,
                ACCEPT_VERSION,
                X_REQUEST_ID,
                CLIENT_ID,
                IDENTIFIER_TYPES,
                STATUS)).willThrow(retryableException);

        assertThatThrownBy(() -> instance.getPartyClientById(X_CHANNEL_ID, ACCEPT_VERSION, X_REQUEST_ID, CLIENT_ID, IDENTIFIER_TYPES, STATUS))
                .isInstanceOf(RetryableMoneyRequestException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedRetryableException);
    }
}