package ca.bnc.payment.moneyrequest.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class IdGeneratorTest {

    private IdGenerator idGenerator;

    @BeforeEach
    void setUp() {
        idGenerator = new IdGenerator();
    }

    @Test
    void testGenerateInstructionIdentification() {
        assertThat(idGenerator.generateInstructionIdentification())
                .isNotNull()
                .isInstanceOf(String.class)
                .hasSize(32);
    }

    @Test
    void testGenerateCorrelationId() {
        assertThat(idGenerator.generateCorrelationId())
                .isNotEmpty()
                .isInstanceOfSatisfying(
                        String.class,
                        s -> assertThatNoException().isThrownBy(() -> UUID.fromString(s)));
    }
}
