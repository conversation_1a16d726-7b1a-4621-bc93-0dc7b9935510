package ca.bnc.payment.moneyrequest.client.interacmoneyrequest;

import ca.bnc.payment.interac_money_request.generated.model.ErrorModel;
import ca.bnc.payment.moneyrequest.generated.model.Error;
import ca.bnc.payment.normalization.lib.service.NormalizationService;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ch.qos.logback.classic.Logger;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;
import java.util.stream.Stream;

import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_GENERIC_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_ERR_TABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SAME_RECIPIENT_SENDER_CODE;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracMoneyRequestApiDecoderErrorsBuilderTest {

    private static final String ERROR_LOG = "An error occurred while parsing error response from the Interac Payments API";
    private static final ErrorModel INTERAC_API_ERROR_MODEL = Instancio.create(ErrorModel.class);
    private static final Error DEFAULT_ERROR = new Error()
            .code(TECHNICAL_ERROR_CODE)
            .text(INTERAC_API_GENERIC_ERROR)
            .origin(SERVICE_ORIGIN)
            .rule(NA);

    private final Logger logger = (Logger) LoggerFactory.getLogger(InteracMoneyRequestApiDecoderErrorsBuilder.class.getName());

    private InteracMoneyRequestApiDecoderErrorsBuilder interacMoneyRequestApiDecoderErrorsBuilder;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private LoggingFacade loggingFacade;

    @Mock
    private Response response;

    @Mock
    private Response.Body body;

    @Mock
    private InputStream inputStream;

    @Mock
    private NormalizationService normalizationService;

    @BeforeEach
    void setup() {
        interacMoneyRequestApiDecoderErrorsBuilder =
                new InteracMoneyRequestApiDecoderErrorsBuilder(
                objectMapper,
                loggingFacade,
                normalizationService);
        given(response.body()).willReturn(body);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(
                objectMapper,
                objectMapper,
                loggingFacade,
                response,
                body,
                inputStream,
                normalizationService);
    }

    @Test
    void givenResponseBodyNull_whenBuildErrorFromResponseBody_thenReturnDefaultError() {
        given(response.body()).willReturn(null);

        final Error actualError = interacMoneyRequestApiDecoderErrorsBuilder.buildErrorFromResponseBody(response);

        assertThat(actualError).isEqualTo(DEFAULT_ERROR);
    }

    @Test
    void givenInputStreamException_whenBuildErrorFromResponseBody_thenLogError() throws IOException {
        final IOException ioException = Instancio.create(IOException.class);
        given(body.asInputStream()).willThrow(ioException);

        final Error actualError = interacMoneyRequestApiDecoderErrorsBuilder.buildErrorFromResponseBody(response);

        assertThat(actualError).isEqualTo(DEFAULT_ERROR);
        then(loggingFacade).should().error(logger, ERROR_LOG, ioException);
    }

    @Test
    void givenObjectMapperException_whenBuildErrorFromResponseBody_thenLogError() throws IOException {
        final RuntimeException exception = Instancio.create(RuntimeException.class);
        given(body.asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(inputStream, ErrorModel.class)).willThrow(exception);

        final Error actualError = interacMoneyRequestApiDecoderErrorsBuilder.buildErrorFromResponseBody(response);

        assertThat(actualError).isEqualTo(DEFAULT_ERROR);
        then(loggingFacade).should().error(logger, ERROR_LOG, exception);
        then(inputStream).should().close();
    }

    @ParameterizedTest( name  = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("normalizeCodesProvider")
    void whenBuildErrorFromResponseBody_thenReturnErrors(final Optional<String> normalizeResult, final String expectedErrorCode) throws IOException {
        given(body.asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(inputStream, ErrorModel.class)).willReturn(INTERAC_API_ERROR_MODEL);
        given(normalizationService.normalize(INTERAC_ERR_TABLE, INTERAC_API_ERROR_MODEL.getCode())).willReturn(normalizeResult);
        final Error expectedError = new Error()
                .code(expectedErrorCode)
                .text(INTERAC_API_ERROR_MODEL.getText())
                .origin(SERVICE_ORIGIN)
                .rule(NA);

        final Error actualError = interacMoneyRequestApiDecoderErrorsBuilder.buildErrorFromResponseBody(response);
        assertThat(actualError).isEqualTo(expectedError);
        then(inputStream).should().close();
    }

    private static Stream<Arguments> normalizeCodesProvider() {
        return Stream.of(
                Arguments.argumentSet("Normalize Code not empty", Optional.of(SAME_RECIPIENT_SENDER_CODE), SAME_RECIPIENT_SENDER_CODE),
                Arguments.argumentSet("Normal Code is empty", Optional.empty(), TECHNICAL_ERROR_CODE)
        );
    }

}