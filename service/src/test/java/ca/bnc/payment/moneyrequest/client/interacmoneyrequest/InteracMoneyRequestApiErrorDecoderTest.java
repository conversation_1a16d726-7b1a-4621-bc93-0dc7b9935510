package ca.bnc.payment.moneyrequest.client.interacmoneyrequest;

import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.exception.RetryableMoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.Error;
import feign.Request;
import feign.Response;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_FORBIDDEN;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_SERVICE_UNAVAILABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_TOO_MANY_REQUESTS;
import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_UNAUTHORIZED;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.mockito.BDDMockito.given;

@ExtendWith(MockitoExtension.class)
class InteracMoneyRequestApiErrorDecoderTest {

    private InteracMoneyRequestApiErrorDecoder interacMoneyRequestApiErrorDecoder;

    @Mock
    private InteracMoneyRequestApiDecoderErrorsBuilder interacMoneyRequestApiDecoderErrorsBuilder;

    @BeforeEach
    void setup() {
        interacMoneyRequestApiErrorDecoder = new InteracMoneyRequestApiErrorDecoder(interacMoneyRequestApiDecoderErrorsBuilder);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("responseCodes")
    void givenInteracException_whenDecode_thenThrowException(final String methodKey,
                                                             final Response response,
                                                             final Exception expectedException,
                                                             final Error error) {
        given(interacMoneyRequestApiDecoderErrorsBuilder.buildErrorFromResponseBody(response)).willReturn(error);

        final Exception exception = interacMoneyRequestApiErrorDecoder.decode(methodKey, response);

        assertThat(exception)
                .isExactlyInstanceOf(MoneyRequestException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }

    @Test
    void givenInteracException_whenDecode_thenThrowRetryableInteracMoneyRequestException() {
        final Request request = Instancio.create(Request.class);
        final Error error = Instancio.create(Error.class);
        Response response = Response.builder().request(request).status(503).build();
        RetryableMoneyRequestException expectedException = new RetryableMoneyRequestException(
                TECHNICAL_ERROR_CODE,
                INTERAC_API_SERVICE_UNAVAILABLE,
                SERVICE_ORIGIN,
                NA
        );
        given(interacMoneyRequestApiDecoderErrorsBuilder.buildErrorFromResponseBody(response)).willReturn(error);

        final Exception exception = interacMoneyRequestApiErrorDecoder.decode(null, response);

        assertThat(exception)
                .isExactlyInstanceOf(RetryableMoneyRequestException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("tooManyRequestsTestCases")
    void givenTooManyRequestsWithDifferentMethods_whenDecode_thenThrowExceptionWithCorrectMessage(
            final String methodKey,
            final String expectedMessage) {
        final Request request = Instancio.create(Request.class);
        final Error error = Instancio.create(Error.class);
        final Response response = Response.builder().request(request).status(429).build();

        given(interacMoneyRequestApiDecoderErrorsBuilder.buildErrorFromResponseBody(response)).willReturn(error);

        final Exception exception = interacMoneyRequestApiErrorDecoder.decode(methodKey, response);

        assertThat(exception)
                .isInstanceOfSatisfying(MoneyRequestException.class,
                        moneyRequestException -> assertThat(
                                moneyRequestException.getErrorInfo().error().getText())
                                .contains(expectedMessage));
    }

    private static Stream<Arguments> responseCodes() {
        final Request request = Instancio.create(Request.class);
        final Error error = Instancio.create(Error.class);
        return Stream.of(
                Arguments.argumentSet(
                        "Interac BAD_REQUEST, return HTTP-400",
                        null,
                        Response.builder().request(request).status(400).build(),
                        MoneyRequestException.badRequest(error),
                        error
                ),
                Arguments.argumentSet(
                        "Interac UNAUTHORIZED, return HTTP-500",
                        null,
                        Response.builder().request(request).status(401).build(),
                        MoneyRequestException.internalServerError(
                                TECHNICAL_ERROR_CODE,
                                INTERAC_API_UNAUTHORIZED,
                                SERVICE_ORIGIN,
                                NA
                        ),
                        error
                ),
                Arguments.argumentSet(
                        "Interac FORBIDDEN, return HTTP-500",
                        null,
                        Response.builder().request(request).status(403).build(),
                        MoneyRequestException.internalServerError(
                                TECHNICAL_ERROR_CODE,
                                INTERAC_API_FORBIDDEN,
                                SERVICE_ORIGIN,
                                NA
                        ),
                        error
                ),
                Arguments.argumentSet(
                        "Interac INTERNAL_SERVER_ERROR, return HTTP-500",
                        null,
                        Response.builder().request(request).status(500).build(),
                        MoneyRequestException.internalServerError(
                                TECHNICAL_ERROR_CODE,
                                INTERAC_API_INTERNAL_SERVER_ERROR,
                                SERVICE_ORIGIN,
                                NA
                        ),
                        error
                ),
                Arguments.argumentSet(
                        "Other Interac error, return HTTP-500",
                        null,
                        Response.builder().request(request).status(418).build(),
                        MoneyRequestException.internalServerError(error),
                        error
                ),
                Arguments.argumentSet(
                        "Interac NOT_FOUND, return InteracMoneyRequestException",
                        null,
                        Response.builder().request(request).status(404).build(),
                        MoneyRequestException.notFound(error),
                        error
                )
        );
    }

    private static Stream<Arguments> tooManyRequestsTestCases() {
        return Stream.of(
                Arguments.argumentSet(
                        "GET method - should use GET message",
                        "getMoneyRequest",
                        INTERAC_API_TOO_MANY_REQUESTS.formatted("GET")
                ),
                Arguments.argumentSet(
                        "DECLINE method - should use DECLINE message",
                        "declinerequestforpayment",
                        INTERAC_API_TOO_MANY_REQUESTS.formatted("DECLINE")
                ),
                Arguments.argumentSet(
                        "Unknown method - should default to GET message",
                        "someOtherMethod",
                        INTERAC_API_TOO_MANY_REQUESTS.formatted("GET")
                ),
                Arguments.argumentSet(
                        "Null method - should default to GET message",
                        null,
                        INTERAC_API_TOO_MANY_REQUESTS.formatted("GET")
                )
        );
    }
}