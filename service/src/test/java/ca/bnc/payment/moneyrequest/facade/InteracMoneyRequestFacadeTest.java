package ca.bnc.payment.moneyrequest.facade;

import ca.bnc.payment.interac_money_request.generated.model.ChannelIndicator;
import ca.bnc.payment.interac_money_request.generated.model.DeclineRequestForPaymentRequest;
import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.interac_money_request.generated.model.SignatureType;
import ca.bnc.payment.moneyrequest.adapter.InteracMoneyRequestAdapter;
import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.generated.model.DeclineSupplementaryData;
import ca.bnc.payment.moneyrequest.mapper.interac.InteracHeaderMapper;
import ca.bnc.payment.moneyrequest.model.ActivePartyIdentifier;
import ca.bnc.payment.moneyrequest.model.InteracHeader;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracMoneyRequestFacadeTest {

    private static final String X_CHANNEL_ID = Instancio.create(String.class);
    private static final ChannelType X_CHANNEL_TYPE = Instancio.create(ChannelType.class);
    private static final UUID X_REQUEST_ID = Instancio.create(UUID.class);
    private static final String X_CLIENT_ID = Instancio.create(String.class);
    private static final String INTERAC_MONEY_REQUEST_ID = Instancio.create(String.class);
    private static final String INTERAC_USER_ID = Instancio.create(String.class);

    private static final String X_ET_PARTICIPANT_ID = Instancio.create(String.class);
    private static final String X_ET_PARTICIPANT_USER_ID = Instancio.create(String.class);
    private static final String X_ET_INDIRECT_CONNECTOR_ID = Instancio.create(String.class);
    private static final String AUTHORIZATION_HEADER = Instancio.create(String.class);
    private static final String X_ET_REQUEST_ID = Instancio.create(String.class);
    private static final ChannelIndicator X_ET_CHANNEL_INDICATOR = Instancio.create(ChannelIndicator.class);
    private static final String X_ET_API_SIGNATURE = Instancio.create(String.class);
    private static final SignatureType X_ET_API_SIGNATURE_TYPE = Instancio.create(SignatureType.class);
    private static final OffsetDateTime X_ET_TRANSACTION_TIME = Instancio.create(OffsetDateTime.class);

    private InteracMoneyRequestFacade interacMoneyRequestFacade;

    @Mock
    private InteracMoneyRequestAdapter interacMoneyRequestAdapter;

    @Mock
    private InteracHeaderMapper interacHeaderMapper;

    @BeforeEach
    void setup() {
        interacMoneyRequestFacade = new InteracMoneyRequestFacade(interacMoneyRequestAdapter, interacHeaderMapper);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(interacMoneyRequestAdapter);
    }

    @Test
    void whenActiveIdentifierFound_thenReturnIdentifier() {
        final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse = Instancio.create(IncomingRequestForPaymentResponse.class);
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext();
        final InteracHeader interacHeader = createInteracHeader();
        given(interacHeaderMapper.mapForGet(moneyRequestContext)).willReturn(interacHeader);
        given(interacMoneyRequestAdapter.getIncomingRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                moneyRequestContext.interacMoneyRequestId()
        )).willReturn(incomingRequestForPaymentResponse);

        final IncomingRequestForPaymentResponse actual
                = interacMoneyRequestFacade.getIncomingRequestForPayment(moneyRequestContext);

        assertThat(actual).isEqualTo(incomingRequestForPaymentResponse);
    }

    @Test
    void whenDeclineAndActiveIdentifierFound_thenReturnIdentifier() {
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext();
        final InteracHeader interacHeader = createInteracHeader();
        final String declineReason = Instancio.create(String.class);
        final DeclineRequest declineRequest = Instancio.of(DeclineRequest.class)
                .set(field(DeclineSupplementaryData::getDeclineReason), declineReason)
                .create();
        final DeclineRequestForPaymentRequest declineRequestForPaymentRequest = Instancio.of(DeclineRequestForPaymentRequest.class)
                .set(field("declineReason"), declineReason)
                .create();

        given(interacHeaderMapper.mapForDecline(moneyRequestContext, declineRequestForPaymentRequest)).willReturn(interacHeader);

        interacMoneyRequestFacade.declineRequestForPayment(moneyRequestContext, declineRequest);

        then(interacHeaderMapper).should().mapForDecline(moneyRequestContext, declineRequestForPaymentRequest);
        then(interacMoneyRequestAdapter).should().declineRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                moneyRequestContext.interacMoneyRequestId(),
                declineRequestForPaymentRequest
        );
    }

    private MoneyRequestContext buildMoneyRequestContext() {
        final ActivePartyIdentifier activePartyIdentifier = Instancio.of(ActivePartyIdentifier.class)
                .set(field("interacUserId"), INTERAC_USER_ID)
                .set(field("clientType"), ClientType.ORGANISATION)
                .create();
        return MoneyRequestContext.builder()
                .channelId(InteracMoneyRequestFacadeTest.X_CHANNEL_ID)
                .channelType(InteracMoneyRequestFacadeTest.X_CHANNEL_TYPE)
                .requestId(InteracMoneyRequestFacadeTest.X_REQUEST_ID)
                .clientId(InteracMoneyRequestFacadeTest.X_CLIENT_ID)
                .interacMoneyRequestId(InteracMoneyRequestFacadeTest.INTERAC_MONEY_REQUEST_ID)
                .activePartyIdentifier(activePartyIdentifier)
                .build();
    }

    private InteracHeader createInteracHeader() {
        return InteracHeader.builder()
                .xEtParticipantId(InteracMoneyRequestFacadeTest.X_ET_PARTICIPANT_ID)
                .xEtParticipantUserId(InteracMoneyRequestFacadeTest.X_ET_PARTICIPANT_USER_ID)
                .xEtIndirectConnectorId(InteracMoneyRequestFacadeTest.X_ET_INDIRECT_CONNECTOR_ID)
                .authorization(InteracMoneyRequestFacadeTest.AUTHORIZATION_HEADER)
                .xEtRequestId(InteracMoneyRequestFacadeTest.X_ET_REQUEST_ID)
                .xEtChannelIndicator(InteracMoneyRequestFacadeTest.X_ET_CHANNEL_INDICATOR)
                .xEtApiSignature(InteracMoneyRequestFacadeTest.X_ET_API_SIGNATURE)
                .xEtApiSignatureType(InteracMoneyRequestFacadeTest.X_ET_API_SIGNATURE_TYPE)
                .xEtTransactionTime(InteracMoneyRequestFacadeTest.X_ET_TRANSACTION_TIME)
                .build();
    }
}