package ca.bnc.payment.moneyrequest.facade;

import ca.bnc.payment.interac_money_request.generated.model.ChannelIndicator;
import ca.bnc.payment.interac_money_request.generated.model.DeclineRequestForPaymentRequest;
import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.interac_money_request.generated.model.SignatureType;
import ca.bnc.payment.moneyrequest.adapter.InteracMoneyRequestAdapter;
import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.moneyrequest.generated.model.DeclineSupplementaryData;
import ca.bnc.payment.moneyrequest.mapper.interac.InteracHeaderMapper;
import ca.bnc.payment.moneyrequest.model.ActivePartyIdentifier;
import ca.bnc.payment.moneyrequest.model.InteracHeader;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.util.ParticipantIdUtil;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ca.nbc.payment.pmt_security_library.interac.InteracEtransferClientTokenManager;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracMoneyRequestFacadeTest {

    private static final String X_CHANNEL_ID = Instancio.create(String.class);
    private static final ChannelType X_CHANNEL_TYPE = Instancio.create(ChannelType.class);
    private static final UUID X_REQUEST_ID = Instancio.create(UUID.class);
    private static final String X_CLIENT_ID = Instancio.create(String.class);
    private static final String INTERAC_MONEY_REQUEST_ID = Instancio.create(String.class);
    private static final String INTERAC_USER_ID = Instancio.create(String.class);

    private static final String X_ET_PARTICIPANT_ID = Instancio.create(String.class);
    private static final String X_ET_INDIRECT_CONNECTOR_ID = Instancio.create(String.class);
    private static final String X_ET_API_SIGNATURE = Instancio.create(String.class);
    private static final OffsetDateTime X_ET_TRANSACTION_TIME = Instancio.create(OffsetDateTime.class);

    private InteracMoneyRequestFacade interacMoneyRequestFacade;
    private InteracHeaderMapper interacHeaderMapper;

    @Mock
    private InteracMoneyRequestAdapter interacMoneyRequestAdapter;

    @Mock
    private ParticipantIdUtil participantIdUtil;

    @Mock
    private InteracEtransferClientTokenManager interacEtransferClientTokenManager;

    @Mock
    private TimeService timeService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private LoggingFacade loggingFacade;

    @BeforeEach
    void setup() {
        interacHeaderMapper = new InteracHeaderMapper(
                participantIdUtil,
                interacEtransferClientTokenManager,
                timeService,
                objectMapper,
                loggingFacade
        );
        interacMoneyRequestFacade = new InteracMoneyRequestFacade(interacMoneyRequestAdapter, interacHeaderMapper);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(interacMoneyRequestAdapter, participantIdUtil,
                interacEtransferClientTokenManager, timeService, objectMapper, loggingFacade);
    }

    @Test
    void whenGetIncomingRequestForPayment_thenMapperIsCalledAndAdapterReceivesCorrectHeaders() {
        // Given
        final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse = Instancio.create(IncomingRequestForPaymentResponse.class);
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext();

        // Setup mapper dependencies
        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(X_ET_PARTICIPANT_ID);
        given(participantIdUtil.getIndirectConnectorIdentifier()).willReturn(X_ET_INDIRECT_CONNECTOR_ID);
        given(interacEtransferClientTokenManager.getTokenAccess(null, X_ET_TRANSACTION_TIME)).willReturn(X_ET_API_SIGNATURE);
        given(timeService.getNowOffsetDateTime()).willReturn(X_ET_TRANSACTION_TIME);

        given(interacMoneyRequestAdapter.getIncomingRequestForPayment(
                X_ET_PARTICIPANT_ID,
                INTERAC_USER_ID, // This should come from the moneyRequestContext
                X_ET_INDIRECT_CONNECTOR_ID, // Organisation client type
                null, // authorization is always null
                X_REQUEST_ID.toString(), // Request ID from context
                getExpectedChannelIndicator(X_CHANNEL_TYPE), // Mapped from channel type
                X_ET_API_SIGNATURE,
                SignatureType.PAYLOAD_DIGEST_SHA256, // Always this value
                X_ET_TRANSACTION_TIME,
                moneyRequestContext.interacMoneyRequestId()
        )).willReturn(incomingRequestForPaymentResponse);

        // When
        final IncomingRequestForPaymentResponse actual
                = interacMoneyRequestFacade.getIncomingRequestForPayment(moneyRequestContext);

        // Then
        assertThat(actual).isEqualTo(incomingRequestForPaymentResponse);
    }

    @Test
    void whenDeclineRequestForPayment_thenMapperIsCalledAndAdapterReceivesCorrectHeaders() throws Exception {
        // Given
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext();
        final String declineReason = Instancio.create(String.class);
        final DeclineRequest declineRequest = Instancio.of(DeclineRequest.class)
                .set(field(DeclineSupplementaryData::getDeclineReason), declineReason)
                .create();
        final String httpPayload = "{\"decline_reason\":\"" + declineReason + "\"}";

        // Setup mapper dependencies
        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(X_ET_PARTICIPANT_ID);
        given(participantIdUtil.getIndirectConnectorIdentifier()).willReturn(X_ET_INDIRECT_CONNECTOR_ID);
        given(timeService.getNowOffsetDateTime()).willReturn(X_ET_TRANSACTION_TIME);
        given(objectMapper.writeValueAsString(org.mockito.ArgumentMatchers.any(DeclineRequestForPaymentRequest.class)))
                .willReturn(httpPayload);
        given(interacEtransferClientTokenManager.getTokenAccess(httpPayload, X_ET_TRANSACTION_TIME))
                .willReturn(X_ET_API_SIGNATURE);

        // When
        interacMoneyRequestFacade.declineRequestForPayment(moneyRequestContext, declineRequest);

        // Then
        then(interacMoneyRequestAdapter).should().declineRequestForPayment(
                X_ET_PARTICIPANT_ID,
                INTERAC_USER_ID, // This should come from the moneyRequestContext
                X_ET_INDIRECT_CONNECTOR_ID, // Organisation client type
                null, // authorization is always null
                X_REQUEST_ID.toString(), // Request ID from context
                getExpectedChannelIndicator(X_CHANNEL_TYPE), // Mapped from channel type
                X_ET_API_SIGNATURE,
                SignatureType.PAYLOAD_DIGEST_SHA256, // Always this value
                X_ET_TRANSACTION_TIME,
                moneyRequestContext.interacMoneyRequestId(),
                org.mockito.ArgumentMatchers.argThat(request ->
                    declineReason.equals(request.getDeclineReason()))
        );
    }

    private MoneyRequestContext buildMoneyRequestContext() {
        final ActivePartyIdentifier activePartyIdentifier = Instancio.of(ActivePartyIdentifier.class)
                .set(field("interacUserId"), INTERAC_USER_ID)
                .set(field("clientType"), ClientType.ORGANISATION)
                .create();
        return MoneyRequestContext.builder()
                .channelId(InteracMoneyRequestFacadeTest.X_CHANNEL_ID)
                .channelType(InteracMoneyRequestFacadeTest.X_CHANNEL_TYPE)
                .requestId(InteracMoneyRequestFacadeTest.X_REQUEST_ID)
                .clientId(InteracMoneyRequestFacadeTest.X_CLIENT_ID)
                .interacMoneyRequestId(InteracMoneyRequestFacadeTest.INTERAC_MONEY_REQUEST_ID)
                .activePartyIdentifier(activePartyIdentifier)
                .build();
    }

    @Test
    void whenGetIncomingRequestForPayment_withIndividualClient_thenIndirectConnectorIdIsNull() {
        // Given
        final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse = Instancio.create(IncomingRequestForPaymentResponse.class);
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContextWithClientType(ClientType.INDIVIDUAL);

        // Setup mapper dependencies
        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(X_ET_PARTICIPANT_ID);
        given(interacEtransferClientTokenManager.getTokenAccess(null, X_ET_TRANSACTION_TIME)).willReturn(X_ET_API_SIGNATURE);
        given(timeService.getNowOffsetDateTime()).willReturn(X_ET_TRANSACTION_TIME);

        given(interacMoneyRequestAdapter.getIncomingRequestForPayment(
                X_ET_PARTICIPANT_ID,
                INTERAC_USER_ID,
                null, // Individual client type should have null indirect connector ID
                null,
                X_REQUEST_ID.toString(),
                getExpectedChannelIndicator(X_CHANNEL_TYPE),
                X_ET_API_SIGNATURE,
                SignatureType.PAYLOAD_DIGEST_SHA256,
                X_ET_TRANSACTION_TIME,
                moneyRequestContext.interacMoneyRequestId()
        )).willReturn(incomingRequestForPaymentResponse);

        // When
        final IncomingRequestForPaymentResponse actual
                = interacMoneyRequestFacade.getIncomingRequestForPayment(moneyRequestContext);

        // Then
        assertThat(actual).isEqualTo(incomingRequestForPaymentResponse);
    }

    @Test
    void whenGetIncomingRequestForPayment_withMobileChannel_thenChannelIndicatorIsMobile() {
        // Given
        final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse = Instancio.create(IncomingRequestForPaymentResponse.class);
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContextWithChannelType(ChannelType.MOBILE);

        // Setup mapper dependencies
        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(X_ET_PARTICIPANT_ID);
        given(participantIdUtil.getIndirectConnectorIdentifier()).willReturn(X_ET_INDIRECT_CONNECTOR_ID);
        given(interacEtransferClientTokenManager.getTokenAccess(null, X_ET_TRANSACTION_TIME)).willReturn(X_ET_API_SIGNATURE);
        given(timeService.getNowOffsetDateTime()).willReturn(X_ET_TRANSACTION_TIME);

        given(interacMoneyRequestAdapter.getIncomingRequestForPayment(
                X_ET_PARTICIPANT_ID,
                INTERAC_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                null,
                X_REQUEST_ID.toString(),
                ChannelIndicator.MOBILE, // Mobile channel should map to MOBILE indicator
                X_ET_API_SIGNATURE,
                SignatureType.PAYLOAD_DIGEST_SHA256,
                X_ET_TRANSACTION_TIME,
                moneyRequestContext.interacMoneyRequestId()
        )).willReturn(incomingRequestForPaymentResponse);

        // When
        final IncomingRequestForPaymentResponse actual
                = interacMoneyRequestFacade.getIncomingRequestForPayment(moneyRequestContext);

        // Then
        assertThat(actual).isEqualTo(incomingRequestForPaymentResponse);
    }

    private MoneyRequestContext buildMoneyRequestContextWithClientType(ClientType clientType) {
        final ActivePartyIdentifier activePartyIdentifier = new ActivePartyIdentifier(INTERAC_USER_ID, clientType);
        return MoneyRequestContext.builder()
                .channelId(X_CHANNEL_ID)
                .channelType(X_CHANNEL_TYPE)
                .requestId(X_REQUEST_ID)
                .clientId(X_CLIENT_ID)
                .interacMoneyRequestId(INTERAC_MONEY_REQUEST_ID)
                .activePartyIdentifier(activePartyIdentifier)
                .build();
    }

    private MoneyRequestContext buildMoneyRequestContextWithChannelType(ChannelType channelType) {
        final ActivePartyIdentifier activePartyIdentifier = new ActivePartyIdentifier(INTERAC_USER_ID, ClientType.ORGANISATION);
        return MoneyRequestContext.builder()
                .channelId(X_CHANNEL_ID)
                .channelType(channelType)
                .requestId(X_REQUEST_ID)
                .clientId(X_CLIENT_ID)
                .interacMoneyRequestId(INTERAC_MONEY_REQUEST_ID)
                .activePartyIdentifier(activePartyIdentifier)
                .build();
    }

    private ChannelIndicator getExpectedChannelIndicator(ChannelType channelType) {
        return channelType == ChannelType.WEB ? ChannelIndicator.ONLINE : ChannelIndicator.MOBILE;
    }
}