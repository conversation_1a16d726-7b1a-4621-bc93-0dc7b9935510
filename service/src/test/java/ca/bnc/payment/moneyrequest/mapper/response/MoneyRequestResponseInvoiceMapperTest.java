package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.Document12;
import ca.bnc.payment.interac_money_request.generated.model.ExternalDocumentType1Code;
import ca.bnc.payment.moneyrequest.generated.model.Invoice;
import ca.bnc.payment.moneyrequest.generated.model.Type;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.OffsetDateTime;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MoneyRequestResponseInvoiceMapperTest {

    private MoneyRequestResponseInvoiceMapper moneyRequestResponseInvoiceMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseInvoiceMapper = new MoneyRequestResponseInvoiceMapper();
    }

    @Test
    void map() {
        final Document12 invoice = Instancio.create(Document12.class);
        final OffsetDateTime expectedIssueDate = invoice.getIssueDate();
        final String expectedIdentification = invoice.getIdentification();
        final ExternalDocumentType1Code expectedCode = invoice.getType().getCode();

        final Invoice actual = moneyRequestResponseInvoiceMapper.map(invoice);

        assertThat(actual).isNotNull();
        assertThat(actual.getIssueDate()).isEqualTo(expectedIssueDate);
        assertThat(actual.getType()).isNotNull();
        assertThat(actual.getType().getIdentification()).isEqualTo(expectedIdentification);
        assertThat(actual.getType().getCode()).isEqualTo(Type.CodeEnum.fromValue(expectedCode.getValue()));
    }

}