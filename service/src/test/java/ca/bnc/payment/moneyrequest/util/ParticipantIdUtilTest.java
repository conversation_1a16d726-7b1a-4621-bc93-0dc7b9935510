package ca.bnc.payment.moneyrequest.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static org.assertj.core.api.Assertions.assertThat;

class ParticipantIdUtilTest {

    private static final String BNC_PARTICIPANT = "CA000006";
    private static final String OS_PARTICIPANT = "CA000612";
    private static final String UKNOWN_PARTICIPANT = "CA000007";
    private static final String INDIRECT_CONNECTOR_ID = "201720189";

    @Test
    void testGetIdentifier() {
        final ParticipantIdUtil participantIdUtil = new ParticipantIdUtil(BNC_PARTICIPANT, INDIRECT_CONNECTOR_ID);
        assertThat(participantIdUtil.getDirectParticipantIdentifier())
                .isEqualTo(BNC_PARTICIPANT);
        assertThat(participantIdUtil.getIndirectConnectorIdentifier())
                .isEqualTo(INDIRECT_CONNECTOR_ID);
    }

    @ParameterizedTest(name = "directParticipantId = {0}, isBncParticipant = {1}")
    @MethodSource("bncInputProvider")
    void testIsBncParticipant(final String directParticipantId, final boolean expectedResult) {

        final ParticipantIdUtil participantIdUtil = new ParticipantIdUtil(directParticipantId, INDIRECT_CONNECTOR_ID);
        assertThat(participantIdUtil.isBncParticipant()).isEqualTo(expectedResult);
        assertThat(participantIdUtil.getIndirectConnectorIdentifier())
                .isEqualTo(INDIRECT_CONNECTOR_ID);
    }

    @ParameterizedTest(name = "directParticipantId = {0}, isOsParticipant = {1}")
    @MethodSource("osInputProvider")
    void testIsOsParticipant(final String directParticipantId, final boolean expectedResult) {

        final ParticipantIdUtil participantIdUtil = new ParticipantIdUtil(directParticipantId, INDIRECT_CONNECTOR_ID);
        assertThat(participantIdUtil.isOsParticipant()).isEqualTo(expectedResult);
        assertThat(participantIdUtil.getIndirectConnectorIdentifier())
                .isEqualTo(INDIRECT_CONNECTOR_ID);
    }


    private static Stream<Arguments> bncInputProvider() {
        return Stream.of(
                Arguments.of(BNC_PARTICIPANT, TRUE),
                Arguments.of(OS_PARTICIPANT, FALSE),
                Arguments.of(UKNOWN_PARTICIPANT, FALSE)
        );
    }

    private static Stream<Arguments> osInputProvider() {
        return Stream.of(
                Arguments.of(OS_PARTICIPANT, TRUE),
                Arguments.of(BNC_PARTICIPANT, FALSE),
                Arguments.of(UKNOWN_PARTICIPANT, FALSE)
        );
    }
}