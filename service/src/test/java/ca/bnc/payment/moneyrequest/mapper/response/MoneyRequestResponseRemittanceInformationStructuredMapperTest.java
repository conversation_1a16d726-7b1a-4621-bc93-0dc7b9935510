package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.CreditorReferenceInformation2;
import ca.bnc.payment.moneyrequest.generated.model.PartyIdentification135;
import ca.bnc.payment.moneyrequest.generated.model.ReferredDocumentInformation7;
import ca.bnc.payment.moneyrequest.generated.model.RemittanceAmount2;
import ca.bnc.payment.moneyrequest.generated.model.StructuredRemittanceInformation16;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class MoneyRequestResponseRemittanceInformationStructuredMapperTest {

    private MoneyRequestResponseRemittanceInformationStructuredMapper moneyRequestResponseRemittanceInformationStructuredMapper;

    @Mock
    private MoneyRequestResponseReferredDocumentInformationMapper moneyRequestResponseReferredDocumentInformationMapper;
    
    @Mock
    private MoneyRequestResponseReferredDocumentAmountMapper moneyRequestResponseReferredDocumentAmountMapper;
    
    @Mock
    private MoneyRequestResponseCreditorReferenceInformationMapper moneyRequestResponseCreditorReferenceInformationMapper;
    
    @Mock
    private MoneyRequestResponseInvoicerMapper moneyRequestResponseInvoicerMapper;
    
    @Mock
    private MoneyRequestResponseInvoiceeMapper moneyRequestResponseInvoiceeMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseRemittanceInformationStructuredMapper = new MoneyRequestResponseRemittanceInformationStructuredMapper(
                moneyRequestResponseReferredDocumentInformationMapper,
                moneyRequestResponseReferredDocumentAmountMapper,
                moneyRequestResponseCreditorReferenceInformationMapper,
                moneyRequestResponseInvoicerMapper,
                moneyRequestResponseInvoiceeMapper
        );
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(
                moneyRequestResponseReferredDocumentInformationMapper,
                moneyRequestResponseReferredDocumentAmountMapper,
                moneyRequestResponseCreditorReferenceInformationMapper,
                moneyRequestResponseInvoicerMapper,
                moneyRequestResponseInvoiceeMapper
        );
    }

    @Test
    void map() {
        final ca.bnc.payment.interac_money_request.generated.model.StructuredRemittanceInformation16 structuredRemittanceInformation =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.StructuredRemittanceInformation16.class);
        final List<ca.bnc.payment.interac_money_request.generated.model.ReferredDocumentInformation7> referredDocumentInformationList
                = structuredRemittanceInformation.getReferredDocumentInformation();
        final ca.bnc.payment.interac_money_request.generated.model.ReferredDocumentInformation7 referredDocumentInformation = referredDocumentInformationList.get(0);
        final ReferredDocumentInformation7 expectedReferredDocumentInformation = Instancio.create(ReferredDocumentInformation7.class);
        final ca.bnc.payment.interac_money_request.generated.model.RemittanceAmount2 referredDocumentAmount = structuredRemittanceInformation.getReferredDocumentAmount();
        final RemittanceAmount2 expectedReferredDocumentAmount = Instancio.create(RemittanceAmount2.class);
        final ca.bnc.payment.interac_money_request.generated.model.CreditorReferenceInformation2 creditorReferenceInformation = structuredRemittanceInformation.getCreditorReferenceInformation();
        final CreditorReferenceInformation2 expectedCreditorReferenceInformation = Instancio.create(CreditorReferenceInformation2.class);
        final ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135 invoicer = structuredRemittanceInformation.getInvoicer();
        final PartyIdentification135 expectedInvoicer = Instancio.create(PartyIdentification135.class);
        final ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135 invoicee = structuredRemittanceInformation.getInvoicee();
        final PartyIdentification135 expectedInvoicee = Instancio.create(PartyIdentification135.class);
        final List<String> additionalRemittanceInformation = structuredRemittanceInformation.getAdditionalRemittanceInformation();
        final String expectedAdditionalRemittanceInformation = additionalRemittanceInformation.get(0);
        given(moneyRequestResponseReferredDocumentInformationMapper.map(referredDocumentInformation)).willReturn(expectedReferredDocumentInformation);
        given(moneyRequestResponseReferredDocumentAmountMapper.map(referredDocumentAmount)).willReturn(expectedReferredDocumentAmount);
        given(moneyRequestResponseCreditorReferenceInformationMapper.map(creditorReferenceInformation)).willReturn(expectedCreditorReferenceInformation);
        given(moneyRequestResponseInvoicerMapper.map(invoicer)).willReturn(expectedInvoicer);
        given(moneyRequestResponseInvoiceeMapper.map(invoicee)).willReturn(expectedInvoicee);

        final StructuredRemittanceInformation16 actual = moneyRequestResponseRemittanceInformationStructuredMapper.map(structuredRemittanceInformation);

        assertThat(actual).isNotNull();
        assertThat(actual.getReferredDocumentInformation()).isEqualTo(expectedReferredDocumentInformation);
        assertThat(actual.getReferredDocumentAmount()).isEqualTo(expectedReferredDocumentAmount);
        assertThat(actual.getCreditorReferenceInformation()).isEqualTo(expectedCreditorReferenceInformation);
        assertThat(actual.getInvoicer()).isEqualTo(expectedInvoicer);
        assertThat(actual.getInvoicee()).isEqualTo(expectedInvoicee);
        assertThat(actual.getAdditionalRemittanceInformation()).isEqualTo(expectedAdditionalRemittanceInformation);
        then(moneyRequestResponseReferredDocumentInformationMapper).should().map(referredDocumentInformation);
        then(moneyRequestResponseReferredDocumentAmountMapper).should().map(referredDocumentAmount);
        then(moneyRequestResponseCreditorReferenceInformationMapper).should().map(creditorReferenceInformation);
        then(moneyRequestResponseInvoicerMapper).should().map(invoicer);
        then(moneyRequestResponseInvoiceeMapper).should().map(invoicee);
    }

    @Test
    void map_withEmptyLists() {
        final ca.bnc.payment.interac_money_request.generated.model.StructuredRemittanceInformation16 structuredRemittanceInformation =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.StructuredRemittanceInformation16.class);
        structuredRemittanceInformation.setAdditionalRemittanceInformation(List.of());
        structuredRemittanceInformation.setReferredDocumentInformation(List.of());
        final ca.bnc.payment.interac_money_request.generated.model.RemittanceAmount2 referredDocumentAmount = structuredRemittanceInformation.getReferredDocumentAmount();
        final RemittanceAmount2 expectedReferredDocumentAmount = Instancio.create(RemittanceAmount2.class);
        final ca.bnc.payment.interac_money_request.generated.model.CreditorReferenceInformation2 creditorReferenceInformation = structuredRemittanceInformation.getCreditorReferenceInformation();
        final CreditorReferenceInformation2 expectedCreditorReferenceInformation = Instancio.create(CreditorReferenceInformation2.class);
        final ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135 invoicer = structuredRemittanceInformation.getInvoicer();
        final PartyIdentification135 expectedInvoicer = Instancio.create(PartyIdentification135.class);
        final ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135 invoicee = structuredRemittanceInformation.getInvoicee();
        final PartyIdentification135 expectedInvoicee = Instancio.create(PartyIdentification135.class);
        given(moneyRequestResponseReferredDocumentAmountMapper.map(referredDocumentAmount)).willReturn(expectedReferredDocumentAmount);
        given(moneyRequestResponseCreditorReferenceInformationMapper.map(creditorReferenceInformation)).willReturn(expectedCreditorReferenceInformation);
        given(moneyRequestResponseInvoicerMapper.map(invoicer)).willReturn(expectedInvoicer);
        given(moneyRequestResponseInvoiceeMapper.map(invoicee)).willReturn(expectedInvoicee);

        final StructuredRemittanceInformation16 actual = moneyRequestResponseRemittanceInformationStructuredMapper.map(structuredRemittanceInformation);

        assertThat(actual).isNotNull();
        assertThat(actual.getReferredDocumentInformation()).isNull();
        assertThat(actual.getReferredDocumentAmount()).isEqualTo(expectedReferredDocumentAmount);
        assertThat(actual.getCreditorReferenceInformation()).isEqualTo(expectedCreditorReferenceInformation);
        assertThat(actual.getInvoicer()).isEqualTo(expectedInvoicer);
        assertThat(actual.getInvoicee()).isEqualTo(expectedInvoicee);
        assertThat(actual.getAdditionalRemittanceInformation()).isNull();
        then(moneyRequestResponseReferredDocumentAmountMapper).should().map(referredDocumentAmount);
        then(moneyRequestResponseCreditorReferenceInformationMapper).should().map(creditorReferenceInformation);
        then(moneyRequestResponseInvoicerMapper).should().map(invoicer);
        then(moneyRequestResponseInvoiceeMapper).should().map(invoicee);
    }

}