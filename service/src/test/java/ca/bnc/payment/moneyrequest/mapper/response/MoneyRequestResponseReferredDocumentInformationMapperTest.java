package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.ReferredDocumentInformation7;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.instancio.Select.field;

class MoneyRequestResponseReferredDocumentInformationMapperTest {

    private MoneyRequestResponseReferredDocumentInformationMapper moneyRequestResponseReferredDocumentInformationMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseReferredDocumentInformationMapper = new MoneyRequestResponseReferredDocumentInformationMapper();
    }

    @Test
    void map() {
        final ca.bnc.payment.interac_money_request.generated.model.ReferredDocumentInformation7 referredDocumentInformation =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.ReferredDocumentInformation7.class);
        final String expectedNumber = referredDocumentInformation.getNumber();
        final ca.bnc.payment.interac_money_request.generated.model.ReferredDocumentType4 type = referredDocumentInformation.getType();
        final String expectedCodeValue = type.getCodeOrProprietary().getCode().getValue();

        final ReferredDocumentInformation7 actual = moneyRequestResponseReferredDocumentInformationMapper.map(referredDocumentInformation);

        assertThat(actual).isNotNull();
        assertThat(actual.getNumber()).isEqualTo(expectedNumber);
        assertThat(actual.getType()).isNotNull();
        assertThat(actual.getType().getCodeOrProprietary()).isNotNull();
        assertThat(actual.getType().getCodeOrProprietary().getCode()).isNotNull();
        assertThat(actual.getType().getCodeOrProprietary().getCode().getValue()).isEqualTo(expectedCodeValue);
    }

    @Test
    void map_withNullType() {
        final ca.bnc.payment.interac_money_request.generated.model.ReferredDocumentInformation7 referredDocumentInformation =
                Instancio.of(ca.bnc.payment.interac_money_request.generated.model.ReferredDocumentInformation7.class)
                        .set(field("type"), null)
                        .create();
        final String number = referredDocumentInformation.getNumber();

        final ReferredDocumentInformation7 actual = moneyRequestResponseReferredDocumentInformationMapper.map(referredDocumentInformation);

        assertThat(actual).isNotNull();
        assertThat(actual.getNumber()).isEqualTo(number);
        assertThat(actual.getType()).isNotNull();
        assertThat(actual.getType().getCodeOrProprietary()).isNotNull();
        assertThat(actual.getType().getCodeOrProprietary().getCode()).isNull();
    }
}