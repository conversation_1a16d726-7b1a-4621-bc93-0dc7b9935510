package ca.bnc.payment.moneyrequest.client.interceptor;

import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.Error;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import ca.nbc.payment.pmt_security_library.utils.OktaUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static ca.bnc.payment.moneyrequest.constant.Constant.TOKEN_GENERATION_FAILED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(MockitoExtension.class)
class ConfigTokenProviderTest {

    private static final String TOKEN_CONFIG_NAME = "testConfig";
    private static final String VALID_TOKEN = "valid-token";

    @Mock
    private OktaClientTokenManager oktaClientTokenManager;

    @Test
    void whenTokenAvailable_thenReturnToken() {
        try (MockedStatic<OktaUtil> oktaUtil = mockStatic(OktaUtil.class)) {
            oktaUtil.when(() -> OktaUtil.getOktaToken(oktaClientTokenManager, TOKEN_CONFIG_NAME))
                    .thenReturn(VALID_TOKEN);

            final ConfigTokenProvider tokenProvider = new ConfigTokenProvider(oktaClientTokenManager, TOKEN_CONFIG_NAME);
            final String actual = tokenProvider.getToken();

            assertThat(actual).isEqualTo(VALID_TOKEN);
        }
    }

    @Test
    void whenTokenNotAvailable_thenThrowOktaException() {
        try (MockedStatic<OktaUtil> oktaUtil = mockStatic(OktaUtil.class)) {
            oktaUtil.when(() -> OktaUtil.getOktaToken(oktaClientTokenManager, TOKEN_CONFIG_NAME))
                    .thenReturn(null);

            final ConfigTokenProvider tokenProvider = new ConfigTokenProvider(oktaClientTokenManager, TOKEN_CONFIG_NAME);
            final Error expectedError = new Error()
                    .code(TECHNICAL_ERROR_CODE)
                    .text(TOKEN_GENERATION_FAILED)
                    .origin(SERVICE_ORIGIN)
                    .rule(NA);

            assertThatThrownBy(tokenProvider::getToken)
                    .isInstanceOfSatisfying(MoneyRequestException.class, e -> assertThat(e.getErrorInfo().error()).isEqualTo(expectedError));
        }
    }
}