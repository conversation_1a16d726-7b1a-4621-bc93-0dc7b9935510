package ca.bnc.payment.moneyrequest.exception;

import org.instancio.Instancio;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static ca.bnc.payment.moneyrequest.constant.Constant.NO_REASON_PROVIDED;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.instancio.Select.field;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;

class MoneyRequestExceptionTest {

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("provideErrorInfo")
    void shouldTestErrorInfo(final ErrorInfo errorInfo, final String expectedErrorText) {
        MoneyRequestException exception = new MoneyRequestException(errorInfo);
        assertThat(exception)
                .returns(errorInfo,MoneyRequestException::getErrorInfo)
                .returns(expectedErrorText, MoneyRequestException::getMessage);
    }

    private static Stream<Arguments> provideErrorInfo() {
        final ErrorInfo nonEmptyError = Instancio.create(ErrorInfo.class);
        final ErrorInfo emptyError = Instancio.of(ErrorInfo.class)
                .set(field(ErrorInfo::error),null)
                .create();

        return Stream.of(
                Arguments.argumentSet("Error is not null", nonEmptyError, nonEmptyError.error().getText()),
                Arguments.argumentSet("Error is null", emptyError, NO_REASON_PROVIDED)
        );
    }
}