package ca.bnc.payment.moneyrequest.client.party;

import ca.bnc.payment.moneyrequest.client.interceptor.InterceptorFactory;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PartyApiOktaInterceptorTest {

    private static final String PARTY_TOKEN_NAME = "partyScope";

    @Mock
    private RequestTemplate requestTemplate;

    @Mock
    private RequestInterceptor delegate;

    @Mock
    private InterceptorFactory interceptorFactory;

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(delegate, interceptorFactory);
    }

    @Test
    void shouldCreateCorrectDelegateWhenUsingInterceptorFactory() {
        given(interceptorFactory.createBncInterceptor(PARTY_TOKEN_NAME)).willReturn(delegate);

        PartyApiOktaInterceptor instance = new PartyApiOktaInterceptor(interceptorFactory);
        instance.apply(requestTemplate);

        then(interceptorFactory).should().createBncInterceptor(PARTY_TOKEN_NAME);
        then(delegate).should().apply(requestTemplate);
    }
  
}