package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.RequestForPaymentStatus;
import ca.bnc.payment.moneyrequest.generated.model.MoneyRequestStatus;
import ca.bnc.payment.moneyrequest.mapper.common.MoneyRequestStatusMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MoneyRequestStatusMapperTest {

    private MoneyRequestStatusMapper moneyRequestStatusMapper;

    @BeforeEach
    void setUp() {
        moneyRequestStatusMapper = new MoneyRequestStatusMapper();
    }

    @ParameterizedTest(name = "sourceStatus = {0}, expectedStatus = {1}")
    @CsvSource({
            "INITIATED, INITIATED",
            "AVAILABLE, AVAILABLE_TO_BE_FULFILLED",
            "FULFILLED, FULFILLED",
            "DECLINED, DECLINED",
            "CANCELLED, CANCELLED",
            "EXPIRED, EXPIRED",
            "DEPOSIT_FAILED, DEPOSIT_FAILED",
            "DEPOSIT_COMPLETE, DEPOSIT_COMPLETE",
    })
    void map(final String sourceStatus, final String expectedStatus) {
        final RequestForPaymentStatus status = RequestForPaymentStatus.valueOf(sourceStatus);

        final MoneyRequestStatus actual = moneyRequestStatusMapper.map(status);

        assertThat(actual).isEqualTo(MoneyRequestStatus.valueOf(expectedStatus));
    }

    @Test
    void shouldInteracPaymentStatus_sameSizeAs_MoneyRequestStatus() {
        assertThat(MoneyRequestStatus.values())
                .hasSize(RequestForPaymentStatus.values().length);
    }

}