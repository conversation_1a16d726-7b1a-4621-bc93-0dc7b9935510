package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.DeclineRequest;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.FIToFICustomerCreditTransfer;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.GroupHeader93;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PptdFIToFICustomerCreditTransferMapperTest {

    private PptdFIToFICustomerCreditTransferMapper pptdFIToFICustomerCreditTransferMapper;

    @Mock
    private PptdGroupHeaderMapper pptdGroupHeaderMapper;

    @Mock
    private PptdCreditTransferTransactionMapper pptdCreditTransferTransactionMapper;

    @BeforeEach
    void setup() {
        pptdFIToFICustomerCreditTransferMapper = new PptdFIToFICustomerCreditTransferMapper(pptdGroupHeaderMapper, pptdCreditTransferTransactionMapper);
    }

    @AfterEach
    public void verifyNoUndesiredInteractions() {
        verifyNoMoreInteractions(pptdGroupHeaderMapper, pptdCreditTransferTransactionMapper);
    }

    @Test
    void map() {
        final DeclineRequest declineRequest = Instancio.create(DeclineRequest.class);
        final IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.create(IncomingMoneyRequestEntity.class);
        final GroupHeader93 groupHeader = Instancio.create(GroupHeader93.class);
        final CreditTransferTransaction39 creditTransferTransaction = Instancio.create(CreditTransferTransaction39.class);
        given(pptdGroupHeaderMapper.map(declineRequest)).willReturn(groupHeader);
        given(pptdCreditTransferTransactionMapper.map(declineRequest, incomingMoneyRequestEntity)).willReturn(creditTransferTransaction);

        final FIToFICustomerCreditTransfer actual = pptdFIToFICustomerCreditTransferMapper.map(declineRequest, incomingMoneyRequestEntity);

        assertThat(actual.getGroupHeader()).isEqualTo(groupHeader);
        assertThat(actual.getCreditTransferTransactionInformation()).hasSize(1);
        assertThat(actual.getCreditTransferTransactionInformation().get(0)).isEqualTo(creditTransferTransaction);
    }

}
