package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.BranchAndFinancialInstitutionIdentification6;
import ca.bnc.payment.moneyrequest.generated.model.CreditorAgent;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class MoneyRequestResponseCreditorAgentMapperTest {

    private MoneyRequestResponseCreditorAgentMapper moneyRequestResponseCreditorAgentMapper;

    private static final String MEMBER_IDENTIFICATION = "TEST_MEMBER";

    @BeforeEach
    void setUp() {
        moneyRequestResponseCreditorAgentMapper = new MoneyRequestResponseCreditorAgentMapper();
    }

    @Test
    void map() {
        final BranchAndFinancialInstitutionIdentification6 source = Instancio.create(BranchAndFinancialInstitutionIdentification6.class);
        source.getFinancialInstitutionIdentification()
                .getClearingSystemMemberIdentification()
                .setMemberIdentification(MEMBER_IDENTIFICATION);

        final CreditorAgent actual = moneyRequestResponseCreditorAgentMapper.map(source);

        assertThat(actual)
                .isNotNull()
                .usingRecursiveComparison()
                .isEqualTo(source);
    }

}