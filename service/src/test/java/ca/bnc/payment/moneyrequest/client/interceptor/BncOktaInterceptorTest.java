package ca.bnc.payment.moneyrequest.client.interceptor;

import ca.bnc.payment.moneyrequest.util.ChannelIdUtil;
import ca.bnc.payment.moneyrequest.util.ParticipantIdUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BncOktaInterceptorTest {

    private static final String X_CHANNEL_ID = "x-channel-id";
    private static final String CHANNEL_ID = "test-channel";

    @Mock
    private RequestInterceptor delegate;
    @Mock
    private ParticipantIdUtil participantIdUtil;
    @Mock
    private ChannelIdUtil channelIdUtil;
    @Mock
    private RequestTemplate requestTemplate;

    private BncOktaInterceptor testee;

    @BeforeEach
    void setUp() {
        testee = new BncOktaInterceptor(delegate, participantIdUtil, channelIdUtil);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(
                delegate,
                participantIdUtil,
                channelIdUtil,
                requestTemplate
        );
    }

    @Test
    void givenBncParticipantAndBncChannel_whenApply_thenDelegateApplied() {
        Map<String, Collection<String>> headers = Map.of(X_CHANNEL_ID, Collections.singletonList(CHANNEL_ID));
        given(requestTemplate.headers()).willReturn(headers);
        given(participantIdUtil.isBncParticipant()).willReturn(true);
        given(channelIdUtil.isBncChannelId(CHANNEL_ID)).willReturn(true);

        testee.apply(requestTemplate);

        then(delegate).should().apply(requestTemplate);
        then(channelIdUtil).should().isBncChannelId(CHANNEL_ID);
        then(requestTemplate).should().headers();
    }

    @Test
    void givenNonBncParticipant_whenApply_thenDelegateNotApplied() {
        Map<String, Collection<String>> headers = Map.of(X_CHANNEL_ID, Collections.singletonList(CHANNEL_ID));
        given(requestTemplate.headers()).willReturn(headers);
        given(participantIdUtil.isBncParticipant()).willReturn(false);

        testee.apply(requestTemplate);

        then(requestTemplate).should().headers();
    }

    @Test
    void givenNonBncChannel_whenApply_thenDelegateNotApplied() {
        Map<String, Collection<String>> headers = Map.of(X_CHANNEL_ID, Collections.singletonList(CHANNEL_ID));
        given(requestTemplate.headers()).willReturn(headers);
        given(participantIdUtil.isBncParticipant()).willReturn(true);
        given(channelIdUtil.isBncChannelId(CHANNEL_ID)).willReturn(false);

        testee.apply(requestTemplate);

        then(channelIdUtil).should().isBncChannelId(CHANNEL_ID);
        then(requestTemplate).should().headers();
    }

    @Test
    void givenNoChannelId_whenApply_thenDelegateNotApplied() {
        given(requestTemplate.headers()).willReturn(Collections.emptyMap());
        given(participantIdUtil.isBncParticipant()).willReturn(true);
        given(channelIdUtil.isBncChannelId(null)).willReturn(false);

        testee.apply(requestTemplate);

        then(channelIdUtil).should().isBncChannelId(null);
        then(requestTemplate).should().headers();
    }
}