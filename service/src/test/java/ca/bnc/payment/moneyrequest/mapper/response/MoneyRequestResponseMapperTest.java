package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.GroupHeader78;
import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.moneyrequest.generated.model.DomesticFulfillmentMoneyRequest;
import ca.bnc.payment.moneyrequest.generated.model.GroupHeader;
import ca.bnc.payment.moneyrequest.generated.model.PaymentInformation;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class MoneyRequestResponseMapperTest {

    private MoneyRequestResponseMapper moneyRequestResponseMapper;

    @Mock
    private MoneyRequestResponseGroupHeaderMapper moneyRequestResponseGroupHeaderMapper;

    @Mock
    private MoneyRequestResponsePaymentInformationMapper moneyRequestResponsePaymentInformationMapper;


    @BeforeEach
    void setUp() {
        moneyRequestResponseMapper = new MoneyRequestResponseMapper(moneyRequestResponseGroupHeaderMapper, moneyRequestResponsePaymentInformationMapper);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(moneyRequestResponseGroupHeaderMapper, moneyRequestResponsePaymentInformationMapper);
    }

    @Test
    void map() {
        final IncomingRequestForPaymentResponse incomingRequestForPaymentResponse = Instancio.create(IncomingRequestForPaymentResponse.class);
        final GroupHeader78 groupHeader = incomingRequestForPaymentResponse.getCreditorPaymentActivationRequest().getGroupHeader();
        final GroupHeader expectedMappedGroupHeader = Instancio.create(GroupHeader.class);
        final PaymentInformation expectedMappedPaymentInformation = Instancio.create(PaymentInformation.class);
        given(moneyRequestResponseGroupHeaderMapper.map(groupHeader)).willReturn(expectedMappedGroupHeader);
        given(moneyRequestResponsePaymentInformationMapper.map(incomingRequestForPaymentResponse)).willReturn(expectedMappedPaymentInformation);

        final DomesticFulfillmentMoneyRequest actual = moneyRequestResponseMapper.map(incomingRequestForPaymentResponse);

        assertThat(actual)
                .isNotNull()
                .extracting(DomesticFulfillmentMoneyRequest::getCreditorPaymentActivationRequest)
                .isNotNull()
                .satisfies(request -> {
                    assertThat(request.getGroupHeader()).isEqualTo(expectedMappedGroupHeader);
                    assertThat(request.getPaymentInformation()).isEqualTo(expectedMappedPaymentInformation);
                });
        then(moneyRequestResponseGroupHeaderMapper).should().map(groupHeader);
        then(moneyRequestResponsePaymentInformationMapper).should().map(incomingRequestForPaymentResponse);
    }

}