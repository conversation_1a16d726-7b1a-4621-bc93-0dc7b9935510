package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.moneyrequest.generated.model.Contact4;
import ca.bnc.payment.moneyrequest.generated.model.Party38Choice;
import ca.bnc.payment.moneyrequest.generated.model.PartyIdentification135;
import ca.bnc.payment.moneyrequest.generated.model.PostalAddress24;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class MoneyRequestResponseInvoiceeMapperTest {

    private MoneyRequestResponseInvoiceeMapper moneyRequestResponseInvoiceeMapper;

    @Mock
    private MoneyRequestResponsePostalAddressMapper moneyRequestResponsePostalAddressMapper;

    @Mock
    private MoneyRequestResponsePartyIdentificationMapper moneyRequestResponsePartyIdentificationMapper;

    @Mock
    private MoneyRequestResponseContactDetailsMapper moneyRequestResponseContactDetailsMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseInvoiceeMapper = new MoneyRequestResponseInvoiceeMapper(
                moneyRequestResponsePostalAddressMapper,
                moneyRequestResponsePartyIdentificationMapper,
                moneyRequestResponseContactDetailsMapper
        );
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(
                moneyRequestResponsePostalAddressMapper,
                moneyRequestResponsePartyIdentificationMapper,
                moneyRequestResponseContactDetailsMapper
        );
    }

    @Test
    void map() {
        final ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135 invoicee =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135.class);
        final String expectedName = invoicee.getName();
        final ca.bnc.payment.interac_money_request.generated.model.PostalAddress24 postalAddress = invoicee.getPostalAddress();
        final PostalAddress24 expectedPostalAddress = Instancio.create(PostalAddress24.class);
        final ca.bnc.payment.interac_money_request.generated.model.Party38Choice identification = invoicee.getIdentification();
        final Party38Choice expectedIdentification = Instancio.create(Party38Choice.class);
        final ca.bnc.payment.interac_money_request.generated.model.Contact4 contactDetails = invoicee.getContactDetails();
        final Contact4 expectedContactDetails = Instancio.create(Contact4.class);
        given(moneyRequestResponsePostalAddressMapper.map(postalAddress)).willReturn(expectedPostalAddress);
        given(moneyRequestResponsePartyIdentificationMapper.map(identification)).willReturn(expectedIdentification);
        given(moneyRequestResponseContactDetailsMapper.map(contactDetails)).willReturn(expectedContactDetails);

        final PartyIdentification135 actual = moneyRequestResponseInvoiceeMapper.map(invoicee);

        assertThat(actual).isNotNull();
        assertThat(actual.getName()).isEqualTo(expectedName);
        assertThat(actual.getPostalAddress()).isEqualTo(expectedPostalAddress);
        assertThat(actual.getIdentification()).isEqualTo(expectedIdentification);
        assertThat(actual.getContactDetails()).isEqualTo(expectedContactDetails);
        then(moneyRequestResponsePostalAddressMapper).should().map(postalAddress);
        then(moneyRequestResponsePartyIdentificationMapper).should().map(identification);
        then(moneyRequestResponseContactDetailsMapper).should().map(contactDetails);
    }

}