package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.generated.model.PaymentIdentification7;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class PptdPaymentIdentificationMapperTest {

    private PptdPaymentIdentificationMapper pptdPaymentIdentificationMapper;

    @BeforeEach
    void setup() {
        pptdPaymentIdentificationMapper = new PptdPaymentIdentificationMapper();
    }

    @Test
    void map() {
        final PaymentIdentification7 paymentIdentification = Instancio.create(PaymentIdentification7.class);

        final ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.PaymentIdentification7 actual = pptdPaymentIdentificationMapper.map(paymentIdentification);

        assertThat(actual.getInstructionIdentification()).isEqualTo(paymentIdentification.getInstructionIdentification());
        assertThat(actual.getEndToEndIdentification()).isEqualTo(paymentIdentification.getEndToEndIdentification());
    }

}
