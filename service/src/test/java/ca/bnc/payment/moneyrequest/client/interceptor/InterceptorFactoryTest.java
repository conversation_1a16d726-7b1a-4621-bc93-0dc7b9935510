package ca.bnc.payment.moneyrequest.client.interceptor;

import ca.bnc.payment.moneyrequest.util.ChannelIdUtil;
import ca.bnc.payment.moneyrequest.util.ParticipantIdUtil;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import feign.RequestInterceptor;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InterceptorFactoryTest {

    @Mock
    private OktaClientTokenManager oktaClientTokenManager;

    @Mock
    private ParticipantIdUtil participantIdUtil;

    @Mock
    private ChannelIdUtil channelIdUtil;

    private InterceptorFactory interceptorFactory;

    @BeforeEach
    void setUp() {
        interceptorFactory = new InterceptorFactory(oktaClientTokenManager, participantIdUtil, channelIdUtil);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(oktaClientTokenManager, participantIdUtil, channelIdUtil);
    }


    @Test
    @DisplayName("should return bnc okta interceptor when createBncInterceptor is called")
    void shouldReturnBncOktaInterceptorWhenCreateBncInterceptorIsCalled() {
        final RequestInterceptor actual = interceptorFactory.createBncInterceptor("tokenConfigName");
        assertThat(actual).isInstanceOf(BncOktaInterceptor.class);
    }
}