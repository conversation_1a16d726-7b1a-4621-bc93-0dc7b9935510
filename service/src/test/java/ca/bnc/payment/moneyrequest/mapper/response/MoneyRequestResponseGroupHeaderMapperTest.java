package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.GroupHeader78;
import ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135;
import ca.bnc.payment.moneyrequest.generated.model.GroupHeader;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;

class MoneyRequestResponseGroupHeaderMapperTest {

    private MoneyRequestResponseGroupHeaderMapper moneyRequestResponseGroupHeaderMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseGroupHeaderMapper = new MoneyRequestResponseGroupHeaderMapper();
    }

    @Test
    void map() {
        final GroupHeader78 groupHeader = Instancio.create(GroupHeader78.class);

        final GroupHeader actual = moneyRequestResponseGroupHeaderMapper.map(groupHeader);

        assertThat(actual).isNotNull();
        assertThat(actual.getMessageIdentification()).isEqualTo(groupHeader.getMessageIdentification());
        assertThat(actual.getCreationDateTime()).isEqualTo(groupHeader.getCreationDatetime());
        assertThat(actual.getNumberOfTransactions()).isEqualTo(1);
        assertThat(actual.getInitiatingParty().getName()).isEqualTo(groupHeader.getInitiatingParty().getName());
    }

    @Test
    void map_withNullInitiatingPartyName() {
        final PartyIdentification135 partyIdentification = Instancio.of(PartyIdentification135.class)
                .set(field("name"), null)
                .create();
        final GroupHeader78 groupHeader = Instancio.of(GroupHeader78.class)
                .set(field("initiatingParty"), partyIdentification)
                .create();

        final GroupHeader actual = moneyRequestResponseGroupHeaderMapper.map(groupHeader);

        assertThat(actual).isNotNull();
        assertThat(actual.getMessageIdentification()).isEqualTo(groupHeader.getMessageIdentification());
        assertThat(actual.getCreationDateTime()).isEqualTo(groupHeader.getCreationDatetime());
        assertThat(actual.getNumberOfTransactions()).isEqualTo(1);
        assertThat(actual.getInitiatingParty().getName()).isEqualTo("NOTPROVIDED");
    }

}