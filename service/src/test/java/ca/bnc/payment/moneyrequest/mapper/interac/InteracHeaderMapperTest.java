package ca.bnc.payment.moneyrequest.mapper.interac;

import ca.bnc.payment.interac_money_request.generated.model.ChannelIndicator;
import ca.bnc.payment.interac_money_request.generated.model.DeclineRequestForPaymentRequest;
import ca.bnc.payment.interac_money_request.generated.model.SignatureType;
import ca.bnc.payment.moneyrequest.generated.model.ChannelType;
import ca.bnc.payment.moneyrequest.model.ActivePartyIdentifier;
import ca.bnc.payment.moneyrequest.model.InteracHeader;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.moneyrequest.util.ParticipantIdUtil;
import ca.bnc.payment.moneyrequest.util.TimeService;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ca.nbc.payment.pmt_security_library.interac.InteracEtransferClientTokenManager;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedInvocationConstants;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.OffsetDateTime;
import java.util.UUID;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.junit.jupiter.params.provider.Arguments.argumentSet;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracHeaderMapperTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(InteracHeaderMapper.class);

    private static final String PARTICIPANT_ID = "direct-id";
    private static final String PARTICIPANT_USER_ID = "activeIdentifier";
    private static final String INDIRECT_CONNECTOR_ID = "indirect-id";
    private static final String ACCESS_TOKEN = "access-token";
    private static final String CHANNEL_ID = "xChannelId";
    private static final String CLIENT_ID = "xClientId";
    private static final String INTERAC_MONEY_REQUEST_ID = "interacMoneyRequestId";
    private static final String ACTIVE_IDENTIFIER = "activeIdentifier";
    private static final UUID REQUEST_ID = UUID.randomUUID();
    private static final String HTTP_PAYLOAD = """
            {
              "decline_reason": "decline reason"
            }
            """;
    private static final String HTTP_PAYLOAD_SIGNATURE = "payload-signature";

    private static final OffsetDateTime NOW = OffsetDateTime.now();

    private InteracHeaderMapper interacHeaderMapper;

    @Mock
    private ParticipantIdUtil participantIdUtil;

    @Mock
    private InteracEtransferClientTokenManager interacEtransferClientTokenManager;

    @Mock
    private TimeService timeService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private LoggingFacade loggingFacade;

    @BeforeEach
    void setUp() {
        interacHeaderMapper = new InteracHeaderMapper(
                participantIdUtil,
                interacEtransferClientTokenManager,
                timeService,
                objectMapper,
                loggingFacade);
    }

    @AfterEach
    void tearDown() {
        verifyNoMoreInteractions(participantIdUtil, interacEtransferClientTokenManager, timeService, objectMapper, loggingFacade);
    }

    @ParameterizedTest(name = ParameterizedInvocationConstants.ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("bncInputProvider")
    void testMap_withChannelTypeWEB_ShouldReturnONLINE(final ChannelType channelType, final ChannelIndicator channelIndicator) {
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext(channelType, ClientType.INDIVIDUAL);
        final InteracHeader expectedHeader = createInteracHeader(null, channelIndicator, ACCESS_TOKEN);

        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(PARTICIPANT_ID);
        given(interacEtransferClientTokenManager.getTokenAccess(null, NOW)).willReturn(ACCESS_TOKEN);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);

        final InteracHeader actual = interacHeaderMapper.mapForGet(moneyRequestContext);

        assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expectedHeader);
    }

    @Test
    void shouldReturn_InteracConnectorId_WhenClientTypeIsOrganisation() {
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext(ChannelType.WEB, ClientType.ORGANISATION);
        final InteracHeader expected = createInteracHeader(INDIRECT_CONNECTOR_ID, ChannelIndicator.ONLINE, ACCESS_TOKEN);
        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(PARTICIPANT_ID);
        given(participantIdUtil.getIndirectConnectorIdentifier()).willReturn(INDIRECT_CONNECTOR_ID);
        given(interacEtransferClientTokenManager.getTokenAccess(null, NOW)).willReturn(ACCESS_TOKEN);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);

        final InteracHeader actual = interacHeaderMapper.mapForGet(moneyRequestContext);

        assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expected);
    }

    @Test
    void shouldReturn_Null_WhenClientTypeIsIndividual() {
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext(ChannelType.WEB, ClientType.INDIVIDUAL);
        final InteracHeader expectedHeader = createInteracHeader(null, ChannelIndicator.ONLINE, ACCESS_TOKEN);

        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(PARTICIPANT_ID);
        given(interacEtransferClientTokenManager.getTokenAccess(null, NOW)).willReturn(ACCESS_TOKEN);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);

        final InteracHeader actual = interacHeaderMapper.mapForGet(moneyRequestContext);

        assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expectedHeader);
    }

    @Test
    void shouldReturn_Space_onGetPayloadSignature_WhenGetTokenAccessIsNull() {
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext(ChannelType.WEB, ClientType.INDIVIDUAL);
        final InteracHeader expectedHeader = createInteracHeader(null, ChannelIndicator.ONLINE, StringUtils.SPACE);

        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(PARTICIPANT_ID);
        given(interacEtransferClientTokenManager.getTokenAccess(null, NOW)).willReturn(null);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);

        final InteracHeader actual = interacHeaderMapper.mapForGet(moneyRequestContext);

        assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expectedHeader);
    }

    @Test
    void shouldReturn_Space_onGetPayloadSignature_WhenObjectMapperThrowsException() throws JsonProcessingException {
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext(ChannelType.WEB, ClientType.INDIVIDUAL);
        final InteracHeader expectedHeader = createInteracHeader(null, ChannelIndicator.ONLINE, StringUtils.SPACE);
        final DeclineRequestForPaymentRequest declineRequest = Instancio.create(DeclineRequestForPaymentRequest.class);
        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(PARTICIPANT_ID);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        final JsonProcessingException exception = Instancio.create(JsonProcessingException.class);
        given(objectMapper.writeValueAsString(declineRequest)).willThrow(exception);
        final InteracHeader actual = interacHeaderMapper.mapForDecline(moneyRequestContext,declineRequest);
        assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expectedHeader);
        then(loggingFacade).should().error(LOGGER, exception.getMessage());
    }

    @Test
    void shouldReturn_SignatureText_onGetPayloadSignature_WhenHttpPayloadNotNull() throws JsonProcessingException {
        final MoneyRequestContext moneyRequestContext = buildMoneyRequestContext(ChannelType.WEB, ClientType.INDIVIDUAL);
        final InteracHeader expectedHeader = createInteracHeader(null, ChannelIndicator.ONLINE, HTTP_PAYLOAD_SIGNATURE);
        final DeclineRequestForPaymentRequest declineRequest = Instancio.create(DeclineRequestForPaymentRequest.class);
        given(participantIdUtil.getDirectParticipantIdentifier()).willReturn(PARTICIPANT_ID);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(interacEtransferClientTokenManager.getTokenAccess(HTTP_PAYLOAD, NOW)).willReturn(HTTP_PAYLOAD_SIGNATURE);
        given(objectMapper.writeValueAsString(declineRequest)).willReturn(HTTP_PAYLOAD);
        final InteracHeader actual = interacHeaderMapper.mapForDecline(moneyRequestContext, declineRequest);
        assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expectedHeader);
    }

    private   MoneyRequestContext buildMoneyRequestContext(
            final ChannelType channelType, final ClientType clientType
    ) {
            return Instancio.of(MoneyRequestContext.class)
                    .set(field("channelType"), channelType)
                    .set(field(ActivePartyIdentifier::clientType), clientType)
                    .create();

    }

    private InteracHeader createInteracHeader(
            final String indirectConnectorId,
            final ChannelIndicator channelIndicator,
            final String accessToken) {
        return Instancio.of(InteracHeader.class)
                .set(field("xEtIndirectConnectorId"), indirectConnectorId)
                .set(field("xEtChannelIndicator"), channelIndicator)
                .set(field("authorization"), null)
                .set(field("xEtApiSignature"), accessToken)
                .create();
    }

    private static Stream<Arguments> bncInputProvider() {
        return Stream.of(
                argumentSet("When Channel Type is WEB the channel indicator is Online", ChannelType.WEB, ChannelIndicator.ONLINE),
                argumentSet("When Channel Type is Mobile the channel indicator is Mobile", ChannelType.MOBILE, ChannelIndicator.MOBILE)
        );
    }

}