package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135;
import ca.bnc.payment.moneyrequest.generated.model.Debtor;
import org.instancio.Instancio;
import org.instancio.Select;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MoneyRequestResponseDebtorMapperTest {

    private MoneyRequestResponseDebtorMapper moneyRequestResponseDebtorMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseDebtorMapper = new MoneyRequestResponseDebtorMapper();
    }

    @Test
    void map() {
        final String expectedName = "Some name";
        final PartyIdentification135 partyIdentification = Instancio.of(PartyIdentification135.class)
                .set(Select.field(PartyIdentification135::getName), expectedName)
                .create();

        final Debtor actual = moneyRequestResponseDebtorMapper.map(partyIdentification);

        assertThat(actual)
                .isNotNull()
                .satisfies(debtor -> assertThat(debtor.getName()).isEqualTo(expectedName));
    }

    @Test
    void map_withNullDebtorName() {
        final PartyIdentification135 partyIdentification = Instancio.of(PartyIdentification135.class)
                .set(Select.field(PartyIdentification135::getName), null)
                .create();

        final Debtor actual = moneyRequestResponseDebtorMapper.map(partyIdentification);

        assertThat(actual.getName()).isEqualTo("NOTPROVIDED");
    }

}