package ca.bnc.payment.moneyrequest.adapter;

import ca.bnc.payment.interac_money_request.generated.model.ChannelIndicator;
import ca.bnc.payment.interac_money_request.generated.model.DeclineRequestForPaymentRequest;
import ca.bnc.payment.interac_money_request.generated.model.DeclineRequestForPaymentResponse;
import ca.bnc.payment.interac_money_request.generated.model.IncomingRequestForPaymentResponse;
import ca.bnc.payment.interac_money_request.generated.model.SignatureType;
import ca.bnc.payment.moneyrequest.client.interacmoneyrequest.InteracMoneyRequestApiClient;
import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.exception.RetryableMoneyRequestException;
import feign.RetryableException;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.time.OffsetDateTime;

import static ca.bnc.payment.moneyrequest.constant.Constant.INTERAC_API_UNAVAILABLE;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracMoneyRequestAdapterTest {

    private static final String X_ET_PARTICIPANT_ID = Instancio.create(String.class);
    private static final String X_ET_PARTICIPANT_USER_ID = Instancio.create(String.class);
    private static final String X_ET_INDIRECT_CONNECTOR_ID = Instancio.create(String.class);
    private static final String AUTHORIZATION_HEADER = Instancio.create(String.class);
    private static final String X_ET_REQUEST_ID = Instancio.create(String.class);
    private static final ChannelIndicator X_ET_CHANNEL_INDICATOR = Instancio.create(ChannelIndicator.class);
    private static final String X_ET_API_SIGNATURE = Instancio.create(String.class);
    private static final SignatureType X_ET_API_SIGNATURE_TYPE = Instancio.create(SignatureType.class);
    private static final OffsetDateTime X_ET_TRANSACTION_TIME = Instancio.create(OffsetDateTime.class);
    private static final String INTERAC_ID_PATH_VARIABLE = Instancio.create(String.class);
    private static final DeclineRequestForPaymentRequest DECLINE_REQUEST_FOR_PAYMENT_REQUEST = Instancio.create(DeclineRequestForPaymentRequest.class);
    private static final ResponseEntity<IncomingRequestForPaymentResponse> GET_RESPONSE = Instancio.create(new TypeToken<>() {
    });
    private static final ResponseEntity<DeclineRequestForPaymentResponse> DECLINE_RESPONSE = Instancio.create(new TypeToken<>() {
    });

    private InteracMoneyRequestAdapter instance;

    @Mock
    private InteracMoneyRequestApiClient interacMoneyRequestApiClient;

    @BeforeEach
    void setUp() {
        instance = new InteracMoneyRequestAdapter(interacMoneyRequestApiClient);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(interacMoneyRequestApiClient);
    }

    @Test
    void whenGetInteracIncomingMoneyRequest_thenReturnResponseBody() {
        given(interacMoneyRequestApiClient.getIncomingRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                INTERAC_ID_PATH_VARIABLE
        )).willReturn(GET_RESPONSE);

        final IncomingRequestForPaymentResponse result = instance.getIncomingRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                INTERAC_ID_PATH_VARIABLE
        );

        final IncomingRequestForPaymentResponse incomingMoneyRequest = GET_RESPONSE.getBody();
        assertThat(result).isEqualTo(incomingMoneyRequest);
    }

    @Test
    void whenInteracApiUnavailable_thenThrowException() {
        RetryableException retryableException = Instancio.create(RetryableException.class);
        MoneyRequestException expectedRetryableException = MoneyRequestException.internalServerError(
                TECHNICAL_ERROR_CODE,
                INTERAC_API_UNAVAILABLE,
                SERVICE_ORIGIN,
                NA
        );
        given(interacMoneyRequestApiClient.getIncomingRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                INTERAC_ID_PATH_VARIABLE
        )).willThrow(retryableException);

        assertThatThrownBy(() -> instance.getIncomingRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                INTERAC_ID_PATH_VARIABLE
        )).isInstanceOf(MoneyRequestException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedRetryableException);
    }

    @Test
    void whenDeclineInteracIncomingMoneyRequest_thenReturnNothing() {
        given(interacMoneyRequestApiClient.declineRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                INTERAC_ID_PATH_VARIABLE,
                DECLINE_REQUEST_FOR_PAYMENT_REQUEST
        )).willReturn(DECLINE_RESPONSE);

        instance.declineRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                INTERAC_ID_PATH_VARIABLE,
                DECLINE_REQUEST_FOR_PAYMENT_REQUEST
        );

        then(interacMoneyRequestApiClient).should().declineRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                INTERAC_ID_PATH_VARIABLE,
                DECLINE_REQUEST_FOR_PAYMENT_REQUEST
        );
    }

    @Test
    void whenDeclineInteracApiUnavailable_thenThrowException() {
        RetryableException retryableException = Instancio.create(RetryableException.class);
        MoneyRequestException expectedRetryableException = MoneyRequestException.internalServerError(
                TECHNICAL_ERROR_CODE,
                INTERAC_API_UNAVAILABLE,
                SERVICE_ORIGIN,
                NA
        );
        given(interacMoneyRequestApiClient.declineRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                INTERAC_ID_PATH_VARIABLE,
                DECLINE_REQUEST_FOR_PAYMENT_REQUEST
        )).willThrow(retryableException);

        assertThatThrownBy(() -> instance.declineRequestForPayment(
                X_ET_PARTICIPANT_ID,
                X_ET_PARTICIPANT_USER_ID,
                X_ET_INDIRECT_CONNECTOR_ID,
                AUTHORIZATION_HEADER,
                X_ET_REQUEST_ID,
                X_ET_CHANNEL_INDICATOR,
                X_ET_API_SIGNATURE,
                X_ET_API_SIGNATURE_TYPE,
                X_ET_TRANSACTION_TIME,
                INTERAC_ID_PATH_VARIABLE,
                DECLINE_REQUEST_FOR_PAYMENT_REQUEST
        )).isInstanceOf(RetryableMoneyRequestException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedRetryableException);
    }


}