package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.CountryCode;
import ca.bnc.payment.moneyrequest.generated.model.AddressType3Choice;
import ca.bnc.payment.moneyrequest.generated.model.PostalAddress24;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MoneyRequestResponsePostalAddressMapperTest {

    private MoneyRequestResponsePostalAddressMapper moneyRequestResponsePostalAddressMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponsePostalAddressMapper = new MoneyRequestResponsePostalAddressMapper();
    }

    @Test
    void map() {
        final ca.bnc.payment.interac_money_request.generated.model.PostalAddress24 postalAddress24 =
                Instancio.create(ca.bnc.payment.interac_money_request.generated.model.PostalAddress24.class);
        final ca.bnc.payment.interac_money_request.generated.model.AddressType3Choice addressType = postalAddress24.getAddressType();
        final CountryCode country = postalAddress24.getCountry();

        final PostalAddress24 actual = moneyRequestResponsePostalAddressMapper.map(postalAddress24);

        assertThat(actual)
                .isNotNull()
                .usingRecursiveComparison()
                .ignoringFields("addressType.code", "country")
                .isEqualTo(postalAddress24);
        assertThat(actual.getAddressType().getCode())
                .isEqualTo(AddressType3Choice.CodeEnum.fromValue(addressType.getCode().getValue()));
        assertThat(actual.getCountry())
                .isEqualTo(PostalAddress24.CountryEnum.fromValue(country.getValue()));

    }

}