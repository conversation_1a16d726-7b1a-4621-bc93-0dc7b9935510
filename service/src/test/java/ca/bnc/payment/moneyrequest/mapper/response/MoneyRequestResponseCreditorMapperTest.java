package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.Contact4;
import ca.bnc.payment.interac_money_request.generated.model.PartyIdentification135;
import ca.bnc.payment.moneyrequest.generated.model.Creditor;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MoneyRequestResponseCreditorMapperTest {

    private MoneyRequestResponseCreditorMapper moneyRequestResponseCreditorMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponseCreditorMapper = new MoneyRequestResponseCreditorMapper();
    }

    @Test
    void map() {
        final PartyIdentification135 creditor = Instancio.create(PartyIdentification135.class);
        final String expectedName = creditor.getName();
        final Contact4 contactDetails = creditor.getContactDetails();
        final String expectedMobileNumber = contactDetails.getMobileNumber();
        final String expectedEmailAddress = contactDetails.getEmailAddress();

        final Creditor actual = moneyRequestResponseCreditorMapper.map(creditor);

        assertThat(actual).isNotNull();
        assertThat(actual.getName()).isEqualTo(expectedName);
        assertThat(actual.getContactDetails()).isNotNull();
        assertThat(actual.getContactDetails().getMobileNumber()).isEqualTo(expectedMobileNumber);
        assertThat(actual.getContactDetails().getEmailAddress()).isEqualTo(expectedEmailAddress);
    }

}