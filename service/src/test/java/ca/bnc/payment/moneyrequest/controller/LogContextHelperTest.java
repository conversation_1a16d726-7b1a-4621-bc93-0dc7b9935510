package ca.bnc.payment.moneyrequest.controller;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

class LogContextHelperTest {

    private static final UUID REQUEST_ID = Instancio.create(UUID.class);
    private static final String CLIENT_ID = Instancio.create(String.class);
    private static final UUID BNC_BUSINESS_TRACE_ID = Instancio.create(UUID.class);
    private static final String INTERAC_MONEY_REQUEST_ID = Instancio.create(String.class);

    private LogContextHelper logContextHelper;

    @BeforeEach
    void setup() {
        logContextHelper = new LogContextHelper();
    }

    @Test
    void testContext() {
        Map<String, Object> actual = logContextHelper.contextFor(REQUEST_ID, CLIENT_ID, BNC_BUSINESS_TRACE_ID, INTERAC_MONEY_REQUEST_ID);
        assertThat(actual).containsExactlyInAnyOrderEntriesOf(Map.of(
                "requestId", REQUEST_ID.toString(),
                "clientId", CLIENT_ID,
                "bncbusinesstraceid", BNC_BUSINESS_TRACE_ID.toString(),
                "interacMoneyRequestId", INTERAC_MONEY_REQUEST_ID
        ));
    }

}
