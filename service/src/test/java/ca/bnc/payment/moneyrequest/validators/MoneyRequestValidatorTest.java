package ca.bnc.payment.moneyrequest.validators;

import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.exception.MoneyRequestException;
import ca.bnc.payment.moneyrequest.generated.model.MoneyRequestStatus;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static ca.bnc.payment.moneyrequest.constant.Constant.CANNOT_DUE_REQUEST_STATUS;
import static ca.bnc.payment.moneyrequest.constant.Constant.INVALID_MONEY_REQUEST_STATUS;
import static ca.bnc.payment.moneyrequest.constant.Constant.INVALID_REQUEST_STATUS_CODE;
import static ca.bnc.payment.moneyrequest.constant.Constant.NA;
import static ca.bnc.payment.moneyrequest.constant.Constant.SERVICE_ORIGIN;
import static ca.bnc.payment.moneyrequest.constant.Constant.TECHNICAL_ERROR_CODE;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class MoneyRequestValidatorTest {
    private MoneyRequestValidator validator;

    @BeforeEach
    void setUp() {
        validator = new MoneyRequestValidator();
    }

    @Test
    void validate_withValidStatus_shouldNotThrowException() {
        IncomingMoneyRequestEntity validEntity = mock(IncomingMoneyRequestEntity.class);
        when(validEntity.getMoneyRequestStatus()).thenReturn(MoneyRequestStatus.INITIATED.getValue());

        assertDoesNotThrow(() -> validator.validate(validEntity));
    }

    @Test
    void validate_withInvalidStatus_shouldThrowMoneyRequestException() {
        MoneyRequestException expectedException = MoneyRequestException.badRequest(
                INVALID_REQUEST_STATUS_CODE,
                CANNOT_DUE_REQUEST_STATUS,
                SERVICE_ORIGIN,
                NA);
        IncomingMoneyRequestEntity invalidEntity = mock(IncomingMoneyRequestEntity.class);
        when(invalidEntity.getMoneyRequestStatus()).thenReturn("CANCELLED");

        assertThatThrownBy(() -> validator.validate(invalidEntity))
                .isInstanceOf(MoneyRequestException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }

    @Test
    void validate_withNullStatus_shouldThrowMoneyRequestException() {
        MoneyRequestException expectedException = MoneyRequestException.internalServerError(
                TECHNICAL_ERROR_CODE,
                INVALID_MONEY_REQUEST_STATUS,
                SERVICE_ORIGIN,
                NA);
        IncomingMoneyRequestEntity invalidEntity = mock(IncomingMoneyRequestEntity.class);
        when(invalidEntity.getMoneyRequestStatus()).thenReturn(null);

        assertThatThrownBy(() -> validator.validate(invalidEntity))
                .isInstanceOf(MoneyRequestException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }

    @Test
    void validate_withNullEntity_shouldNotThrowException() {
        assertDoesNotThrow(() -> validator.validate(null));
    }

    @Test
    void validateNotEmpty_withNonNullEntity_shouldThrowException() {
        IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.of(IncomingMoneyRequestEntity.class)
                .set(field("moneyRequestStatus"), MoneyRequestStatus.INITIATED.getValue())
                .create();
        assertThatCode(() -> validator.checkIfEmptyAndValidate(incomingMoneyRequestEntity))
                .doesNotThrowAnyException();
    }

    @Test
    void isEmpty_withFulfilledStatus_shouldThrowException() {
        MoneyRequestException moneyRequestException = MoneyRequestException.badRequest(
                INVALID_REQUEST_STATUS_CODE,
                CANNOT_DUE_REQUEST_STATUS, SERVICE_ORIGIN, NA);
        IncomingMoneyRequestEntity incomingMoneyRequestEntity = Instancio.of(IncomingMoneyRequestEntity.class)
                .set(field("moneyRequestStatus"), MoneyRequestStatus.FULFILLED.getValue())
                .create();
        assertThatCode(() -> validator.checkIfEmptyAndValidate(incomingMoneyRequestEntity))
                .isInstanceOf(MoneyRequestException.class)
                .usingRecursiveComparison()
                .isEqualTo(moneyRequestException);
    }

    @Test
    void validateNotEmpty_withNullEntity_shouldThrowException() {
        MoneyRequestException expectedException =  MoneyRequestException.notFound(null);
        assertThatThrownBy(() -> validator.checkIfEmptyAndValidate(null))
                .isInstanceOf(MoneyRequestException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }
}