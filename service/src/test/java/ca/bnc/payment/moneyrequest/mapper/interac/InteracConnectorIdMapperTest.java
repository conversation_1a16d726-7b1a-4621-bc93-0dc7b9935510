package ca.bnc.payment.moneyrequest.mapper.interac;

import ca.bnc.payment.moneyrequest.util.ParticipantIdUtil;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracConnectorIdMapperTest {
    private static final String INDIRECT_CONNECTOR_ID = "indirect-connector-id";

    private InteracConnectorIdMapper interacConnectorIdMapper;

    @Mock
    private ParticipantIdUtil participantIdUtil;

    @BeforeEach
    void setup(){
        interacConnectorIdMapper = new InteracConnectorIdMapper(participantIdUtil);
    }

    @AfterEach
    void tearDown(){
        verifyNoMoreInteractions(participantIdUtil);
    }

    @Test
    void shouldReturn_InteracConnectorId_WhenClientTypeIsOrganisation() {
        given(participantIdUtil.getIndirectConnectorIdentifier()).willReturn(INDIRECT_CONNECTOR_ID);
        final String actualIndirectConnectorId = interacConnectorIdMapper.map(ClientType.ORGANISATION);
        assertThat(actualIndirectConnectorId).isEqualTo(INDIRECT_CONNECTOR_ID);
    }

    @Test
    void shouldReturn_Null_WhenClientTypeIsIndividual() {
        final String actualIndirectConnectorId = interacConnectorIdMapper.map(ClientType.INDIVIDUAL);
        assertThat(actualIndirectConnectorId).isNull();
    }
}