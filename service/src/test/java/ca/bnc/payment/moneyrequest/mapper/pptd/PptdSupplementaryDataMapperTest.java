package ca.bnc.payment.moneyrequest.mapper.pptd;

import ca.bnc.payment.moneyrequest.model.ActivePartyIdentifier;
import ca.bnc.payment.moneyrequest.model.MoneyRequestContext;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.ExternalPaymentTransactionStatus1Code;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.SupplementaryData;
import ca.nbc.payment.pmtpartnersparty.model.ClientType;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;

class PptdSupplementaryDataMapperTest {

    private PptdSupplementaryDataMapper pptdSupplementaryDataMapper;

    @BeforeEach
    void setup() {
        pptdSupplementaryDataMapper = new PptdSupplementaryDataMapper();
    }

    @Test
    void map() {
        final String interacMoneyRequestId = "test-money-request-id";
        final String interacUserId = "test-interac-user-id";
        final MoneyRequestContext moneyRequestContext = Instancio.of(MoneyRequestContext.class)
                .set(field("interacMoneyRequestId"), interacMoneyRequestId)
                .set(field("activePartyIdentifier"), new ActivePartyIdentifier(interacUserId, ClientType.ORGANISATION))
                .create();

        final SupplementaryData actual = pptdSupplementaryDataMapper.map(moneyRequestContext);

        assertThat(actual.getPaymentDirection()).isEqualTo(SupplementaryData.PaymentDirectionEnum.OUT);
        assertThat(actual.getTransactionStatus()).isEqualTo(ExternalPaymentTransactionStatus1Code.RJCT);
        assertThat(actual.getInteracMoneyRequestId()).isEqualTo(interacMoneyRequestId);
        assertThat(actual.getInteracUserId()).isEqualTo(interacUserId);
    }

}
