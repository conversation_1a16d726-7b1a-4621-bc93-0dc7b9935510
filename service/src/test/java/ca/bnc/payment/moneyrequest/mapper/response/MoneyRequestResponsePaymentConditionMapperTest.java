package ca.bnc.payment.moneyrequest.mapper.response;

import ca.bnc.payment.interac_money_request.generated.model.PaymentCondition1;
import ca.bnc.payment.moneyrequest.generated.model.PaymentCondition;
import org.instancio.Instancio;
import org.instancio.Select;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MoneyRequestResponsePaymentConditionMapperTest {

    private MoneyRequestResponsePaymentConditionMapper moneyRequestResponsePaymentConditionMapper;

    @BeforeEach
    void setUp() {
        moneyRequestResponsePaymentConditionMapper = new MoneyRequestResponsePaymentConditionMapper();
    }

    @ParameterizedTest(name = "amountModificationAllowed = {0}")
    @ValueSource(booleans = {true, false})
    void map(final boolean amountModificationAllowed) {
        final PaymentCondition1 paymentCondition = Instancio.of(PaymentCondition1.class)
                .set(Select.field(PaymentCondition1::getAmountModificationAllowed), amountModificationAllowed)
                .create();

        final PaymentCondition actual = moneyRequestResponsePaymentConditionMapper.map(paymentCondition);

        assertThat(actual)
                .isNotNull()
                .satisfies(condition ->
                        assertThat(condition.getAmountModificationAllowed()).isEqualTo(amountModificationAllowed)
                );
    }

}