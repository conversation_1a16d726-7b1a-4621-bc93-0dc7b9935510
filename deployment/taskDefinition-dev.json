{"containerDefinitions": [{"name": "payments-et-pmt-proc-dev-pmt-incoming-money-request-api", "cpu": 896, "memory": 1536, "portMappings": [{"containerPort": 8080, "hostPort": 8080, "protocol": "tcp"}], "essential": true, "environment": [{"name": "APP_LOG_LEVEL", "value": "DEBUG"}, {"name": "APP_NAME", "value": "pmt-incoming-money-request-api"}, {"name": "APPLICATION_DIRECTPARTICIPANTIDENTIFIER", "value": "CA000612"}, {"name": "AUTHORIZATIONSERVERID", "value": "unused"}, {"name": "OKTACLIENTID", "value": "unused"}, {"name": "OKTAPRIVATEKEY", "value": "unused"}, {"name": "JWKSURL", "value": "unused"}, {"name": "DD_ENV", "value": "payments-et-pmt-proc-dev"}, {"name": "APPLICATION_ASYNC_THREAD_POOL_AWAIT_TERMINATION_SECONDS", "value": "60"}, {"name": "APPLICATION_ASYNC_THREAD_POOL_CORE_SIZE", "value": "20"}, {"name": "APPLICATION_ASYNC_THREAD_POOL_MAX_SIZE", "value": "20"}, {"name": "DD_HTTP_CLIENT_TAG_QUERY_STRING", "value": "true"}, {"name": "DD_PROFILING_ENABLED", "value": "false"}, {"name": "DD_SERVICE", "value": "pmt-incoming-money-request-api"}, {"name": "DD_TRACE_REPORT_HOSTNAME", "value": "true"}, {"name": "DYNAMO_DB_REGION", "value": "ca-central-1"}, {"name": "DYNAMO_DB_TABLE_NAME", "value": "payments-et-pmt-proc-dev-INCOMING_MONEY_REQUEST_TABLE"}, {"name": "INDIRECT_CONNECTOR_ID", "value": "202305293"}, {"name": "INTERACMONEYREQUEST_CONNECTION_TIMEOUT", "value": "10000"}, {"name": "INTERACMONEYREQUEST_READ_TIMEOUT", "value": "10000"}, {"name": "INTERACMONEYREQUEST_RETRY_ATTEMPTS", "value": "3"}, {"name": "INTERACMONEYREQUEST_RETRY_BACKOFF", "value": "100"}, {"name": "INTERACMONEYREQUEST_URL", "value": "https://pmt-etransfer-simulator.et-pmt-proc.cluster.dev.payments.npr.osfin.internal:8443"}, {"name": "INTERAC_SIGNING_ENABLED", "value": "false"}, {"name": "KAFKA_PAYMENTNOTIFICATION_TOPIC", "value": "6256-npr-dev-pmt.notification"}, {"name": "KAFKA_PPTD_TOPIC", "value": "payments-dev-pmt-pptd-tpc.etrf.trx.v1.0"}, {"name": "KAFKA_RETRY_BACKOFF_PERIOD", "value": "500"}, {"name": "KAFKA_RETRY_MAX_ATTEMPTS", "value": "3"}, {"name": "KAFKA_SECURITY_PROTOCOL", "value": "SASL_SSL"}, {"name": "KAFKA_SASL_MECHANISM", "value": "SCRAM-SHA-512"}, {"name": "PARTY_CONNECTION_TIMEOUT", "value": "10000"}, {"name": "PARTY_READ_TIMEOUT", "value": "10000"}, {"name": "PARTY_RETRY_ATTEMPTS", "value": "3"}, {"name": "PARTY_RETRY_BACKOFF", "value": "100"}, {"name": "PARTY_URL", "value": "https://pmt-etransfer-simulator.et-pmt-proc.cluster.dev.payments.npr.osfin.internal:8443"}, {"name": "SPRING_PROFILE_INCLUDE", "value": "logging-full,okta-default"}, {"name": "TENANT", "value": "OS"}, {"name": "THRESHOLD_HEAP_PERCENT", "value": "95"}], "secrets": [{"name": "KAFKA_BOOTSTRAP_SERVERS", "valueFrom": "arn:aws:ssm:ca-central-1:923113508564:parameter/payments-et-pmt-proc-dev/kafka_scram_sasl"}, {"name": "KAFKA_USERNAME", "valueFrom": "arn:aws:secretsmanager:ca-central-1:891377281260:secret:AmazonMSK_payments_npr_et-pmt-proc:username::"}, {"name": "KAFKA_PASSWORD", "valueFrom": "arn:aws:secretsmanager:ca-central-1:891377281260:secret:AmazonMSK_payments_npr_et-pmt-proc:password::"}, {"name": "TRUST_STORE_CERTIFICATE", "valueFrom": "arn:aws:secretsmanager:ca-central-1:923113508564:secret:payments/et-pmt-proc/dev/ca:CACHAIN_B64::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "ecs/payments/dev/et-pmt-proc/pmt-incoming-money-request-api", "awslogs-region": "ca-central-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:9999/actuator/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 90}}, {"name": "datadog-agent", "image": "public.ecr.aws/datadog/agent:latest", "cpu": 64, "memory": 256, "portMappings": [{"containerPort": 8126, "hostPort": 8126, "protocol": "tcp"}], "essential": true, "environment": [{"name": "ECS_FARGATE", "value": "true"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:ca-central-1:923113508564:secret:payments/et-pmt-proc/dev/datadog:DD_API_KEY::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "ecs/payments/dev/et-pmt-proc/pmt-incoming-money-request-api", "awslogs-region": "ca-central-1", "awslogs-stream-prefix": "datadog"}}}, {"name": "pmt-incoming-money-request-api-sidecar", "image": "860646818048.dkr.ecr.ca-central-1.amazonaws.com/payments/os-payments-envoy-sidecar:latest", "cpu": 64, "memory": 256, "portMappings": [{"containerPort": 8443, "hostPort": 8443, "protocol": "tcp"}], "essential": true, "environment": [{"name": "ENV", "value": "8443"}, {"name": "ENVOY_UPSTREAM_PORT", "value": "8080"}], "secrets": [{"name": "ENVOY_LISTENER_CERTIFICATE_BODY", "valueFrom": "arn:aws:secretsmanager:ca-central-1:923113508564:secret:payments/et-pmt-proc/api/certs/pmt-incoming-money-request-api:Certificate_B64::"}, {"name": "ENVOY_LISTENER_CERTIFICATE_CHAIN", "valueFrom": "arn:aws:secretsmanager:ca-central-1:923113508564:secret:payments/et-pmt-proc/api/certs/pmt-incoming-money-request-api:CertificateChain_B64::"}, {"name": "ENVOY_LISTENER_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ca-central-1:923113508564:secret:payments/et-pmt-proc/api/certs/pmt-incoming-money-request-api:PrivateKey_B64::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "ecs/payments/dev/et-pmt-proc/pmt-incoming-money-request-api", "awslogs-region": "ca-central-1", "awslogs-stream-prefix": "envoy"}}}], "family": "payments-et-pmt-proc-dev-pmt-incoming-money-request-api", "taskRoleArn": "arn:aws:iam::923113508564:role/payments-et-pmt-proc-dev-out-pmt-mgt-task-role", "executionRoleArn": "arn:aws:iam::923113508564:role/payments-et-pmt-proc-dev-execution-secrets-task-role", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "tags": [{"key": "business_domain", "value": "payments"}, {"key": "environment", "value": "dev"}, {"key": "bounded_context", "value": "et-pmt-proc"}, {"key": "region", "value": "ca-central-1"}, {"key": "api_type", "value": "business"}, {"key": "tenant", "value": "OS"}]}