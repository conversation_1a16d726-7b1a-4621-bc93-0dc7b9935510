{"sequence": 44, "timestamp": "2025-08-01T15:38:08.581", "dd.trace_id": "1229559994704618332", "dd.span_id": "5830998276244746378", "version": "0.1.4", "application.name": "pmt-incoming-money-request-api", "logger_name": "ca.bnc.payment.moneyrequest.controller.advice.MoneyRequestControllerAdvice", "thread_name": "http-nio-8080-exec-2", "level": "ERROR", "message": "INCOMING_MONEY_REQUEST", "stack_trace": "<#555cad94> ca.bnc.payment.moneyrequest.exception.RetryableMoneyRequestException: The Interac Payments API is unreachable\n\tat ca.bnc.payment.moneyrequest.adapter.InteracMoneyRequestAdapter.getIncomingRequestForPayment(InteracMoneyRequestAdapter.java:57)\n\tat jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Unknown Source)\n\tat jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)\n\tat jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)\n\t... 4 frames excluded\n\tat org.springframework.retry.interceptor.RetryOperationsInterceptor$1.doWithRetry(RetryOperationsInterceptor.java:114)\n\tat org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)\n\tat org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:246)\n\tat org.springframework.retry.interceptor.RetryOperationsInterceptor.invoke(RetryOperationsInterceptor.java:135)\n\tat org.springframework.retry.annotation.AnnotationAwareRetryOperationsInterceptor.invoke(AnnotationAwareRetryOperationsInterceptor.java:162)\n\t... 2 frames excluded\n\tat ca.bnc.payment.moneyrequest.adapter.InteracMoneyRequestAdapter$$SpringCGLIB$$0.getIncomingRequestForPayment(<generated>)\n\tat ca.bnc.payment.moneyrequest.facade.InteracMoneyRequestFacade.getIncomingRequestForPayment(InteracMoneyRequestFacade.java:27)\n\tat ca.bnc.payment.moneyrequest.service.MoneyRequestService.processGetIncomingMoneyRequest(MoneyRequestService.java:58)\n\tat ca.bnc.payment.moneyrequest.controller.MoneyRequestController.lambda$getIncomingMoneyRequest$0(MoneyRequestController.java:43)\n\tat ca.nbc.payment.lib.service.logging.LogContextHolder.runWithContextNoReset(LogContextHolder.java:130)\n\tat ca.bnc.payment.moneyrequest.controller.MoneyRequestController.getIncomingMoneyRequest(MoneyRequestController.java:39)\n\tat jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Unknown Source)\n\tat jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)\n\tat jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)\n\t... 7 frames excluded\n\tat ca.bnc.payment.moneyrequest.controller.MoneyRequestController$$SpringCGLIB$$0.getIncomingMoneyRequest(<generated>)\n\tat jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Unknown Source)\n\tat jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)\n\tat jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)\n\tat java.lang.reflect.Method.invoke(Unknown Source)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)\n\tat o.s.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)\n\tat o.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)\n\tat o.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)\n\t... 73 frames truncated\n", "clientId": "BD2E5746C6013778F4CD84D4533FAE6CF80C8C6F52C686691562F452D5FAZ0", "requestId": "3ee01483-2b49-467c-8cde-c48d1a1fd1c0", "loggingMessage": "code: TECHNICAL_ERROR | text: The Interac Payments API is unreachable | origin: pmt-incoming-money-request-api | rule: NA", "bncbusinesstraceid": "d3028133-3488-4e92-9496-f331f4be9eb8", "interacMoneyRequestId": "123456789"}