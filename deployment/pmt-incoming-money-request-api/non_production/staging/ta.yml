---
api:
  name: pmt-incoming-money-request-api
  service_type: business
  app_id: 6256
  helm_version: "0.2.2-rc-3"

metadata:
  appversion: $ARTIFACT_VERSION
  tool_stack_id: 318a4238-5493-4beb-97d5-950b9ef4d933
  gateways:
    - 6084-nonproduction-common-gateway/6084-nonproduction-staging-ta-ingressgw
  hostnames:
    - pmt-ta.apis.bngf.local
    - 6084-npr-staging-ta.15001.npr.aws.bngf.local

virtualService:
  hosts:
    - "pmt-ta.apis.bngf.local"
    - "6084-npr-staging-ta.15001.npr.aws.bngf.local"
  gateways:
    - "6084-nonproduction-common-gateway/6084-nonproduction-staging-ta-ingressgw"
  http:
    - match:
        - uri:
            prefix: "/et_pmt_proc/incoming-money-request"
      route:
        - destination:
            host: "pmt-incoming-money-request-api.6256-nonproduction-staging-ta.svc.cluster.local"
            port:
              number: 8080

deployment:
  name: pmt-incoming-money-request-api
  api_operation_tools: customer-application
  environment: non_production
  stage: ta
  replica_count: 1
  serviceAccountRoleDetails:
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/Custom6256PmtIncomingMoneyRequestRoleTA"
    eks.amazonaws.com/sts-regional-endpoints: "true"
  imagePullSecrets:
    - name: 318a4238-5493-4beb-97d5-950b9ef4d933.artifact-management
  k8s:
    podsecuritycontext:
      runAsUser: 1001
    annotations_pod:
      sidecar.istio.io/proxyCPU: "100m"
      sidecar.istio.io/proxyCPULimit: "2000m"
      sidecar.istio.io/proxyMemory: "192Mi"
      sidecar.istio.io/proxyMemoryLimit: "2048Mi"
  hpa:
    min_replicas: 1
    max_replicas: 1
    target_cpu_utilization_percentage: 100

containers:
  - name: pmt-incoming-money-request-api
    image:
      name: $DEPLOY_IMAGE_FULL_NAME
      pullPolicy: Always
    configuration_required: true
    ports:
      - container_port: 8080
        exposed_port: 8080
        name: HTTP
        protocol: TCP
        service_mapping:
          enabled: true
    k8s:
      resources:
        limits:
          cpu: 2000m
          memory: 2000Mi
        requests:
          cpu: 300m
          memory: 1000Mi
      startupProbe:
        initialDelaySeconds: 15
        timeoutSeconds: 5
        periodSeconds: 5
        successThreshold: 1
        failureThreshold: 15
        http:
          port: 9999
          path: /actuator/health/liveness
      livenessProbe:
        initialDelaySeconds: 15
        timeoutSeconds: 5
        periodSeconds: 5
        successThreshold: 1
        failureThreshold: 15
        http:
          port: 9999
          path: /actuator/health/liveness
      readinessProbe:
        initialDelaySeconds: 15
        timeoutSeconds: 5
        periodSeconds: 5
        successThreshold: 1
        failureThreshold: 15
        http:
          port: 9999
          path: /actuator/health/readiness

configurations:
  datadog: true
  env:
    ## Application
    - key: APP_ENVIRONMENT
      value: "ta"
    - key: APP_LOG_LEVEL
      value: "DEBUG"
    - key: APP_NAME
      value: "pmt-incoming-money-request-api"
    - key: APP_PLATFORM
      value: "aws"
    - key: APPLICATION_ASYNC_THREAD_POOL_AWAIT_TERMINATION_SECONDS
      value: "60"
    - key: APPLICATION_ASYNC_THREAD_POOL_CORE_SIZE
      value: "20"
    - key: APPLICATION_ASYNC_THREAD_POOL_MAX_SIZE
      value: "20"
    - key: APPLICATION_DIRECTPARTICIPANTIDENTIFIER
      value: "CA000006"
    - key: DYNAMO_DB_REGION
      value: "ca-central-1"
    - key: DYNAMO_DB_TABLE_NAME
      value: "INCOMING_MONEY_REQUEST_TABLE_TA"
    - key: INDIRECT_CONNECTOR_ID
      value: "201720189"
    - key: INTERACMONEYREQUEST_RETRY_ATTEMPTS
      value: "3"
    - key: INTERACMONEYREQUEST_RETRY_BACKOFF
      value: "100"
    - key: INTERACMONEYREQUEST_URL
      value: "https://int-gateway-b-ta.npr.bngf.local:31443/InteracPayments/request-api/v3.5.0"
    - key: INTERACMONEYREQUEST_CONNECTION_TIMEOUT
      value: "10000"
    - key: INTERACMONEYREQUEST_READ_TIMEOUT
      value: "10000"
    - key: INTERAC_SIGNING_ENABLED
      value: "true"
    - key: KAFKA_BOOTSTRAP_SERVERS
      value: "lkc-6kyrm8-pr212g.ca-central-1.aws.glb.confluent.cloud:9092"
    - key: KAFKA_PPTD_TOPIC
      value: "6997.pptd.ta.tpc.etrf-trx.v1.0"
    - key: KAFKA_RETRY_BACKOFF_PERIOD
      value: "500"
    - key: KAFKA_RETRY_MAX_ATTEMPTS
      value: "3"
    - key: KAFKA_SECURITY_PROTOCOL
      value: "SASL_SSL"
    - key: KAFKA_SASL_MECHANISM
      value: "PLAIN"
    - key: PARTY_CONNECTION_TIMEOUT
      value: "10000"
    - key: PARTY_READ_TIMEOUT
      value: "10000"
    - key: PARTY_RETRY_ATTEMPTS
      value: "3"
    - key: PARTY_RETRY_BACKOFF
      value: "100"
    - key: PARTY_URL
      value: "http://pmt-partners-party-api.6866-nonproduction-staging-ta.svc.cluster.local:8080"
    - key: SPRING_PROFILE_INCLUDE
      value: "logging-full,okta-default"
    - key: THRESHOLD_HEAP_PERCENT
      value: "95"

    ## Datadog
    - key: DD_SERVICE_NAME
      value: "pmt-incoming-money-request-api"
    - key: DD_TRACE_ENABLED
      value: "true"
    - key: DD_LOGS_INJECTION
      value: "true"
    - key: DD_PROFILING_ENABLED
      value: "true"
    - key: DD_TRACE_DEBUG
      value: "false"
    - key: DD_SERVICE_MAPPING
      value: "jms:pmt-incoming-money-request-api"
    - key: DD_TRACE_ANALYTICS_ENABLED
      value: "true"
    - key: DD_JMXFETCH_ENABLED
      value: "true"
    - key: DD_JMS_ANALYTICS_ENABLED
      value: "true"
    - key: DD_HTTP_SERVER_TAG_QUERY_STRING
      value: "true"
    - key: DD_HTTP_CLIENT_TAG_QUERY_STRING
      value: "true"
    - key: DD_TRACE_REPORT_HOSTNAME
      value: "true"
    - key: DD_ENV
      value: "app6256-payment-ta"
    - key: DD_TRACE_GLOBAL_TAGS
      value: "env:app6256-payment-ta"
    - key: DD_TRACE_SPAN_TAGS
      value: "service:pmt-incoming-money-request-api"
    - key: TENANT
      value: "BNC"

  externalSecrets:
    - target:
        name: pmt-money-request-api-certificates
      source: secret-management.9b4eb9c6-dd35-4954-9c8f-b5ca71f60c91
      destination:
        type: env
      data:
        - secretKey: AUTHORIZATIONSERVERID
          remoteRef:
            key: applications/6256/non_production/staging/ta/pmt-incoming-money-request-api
            property: AUTHORIZATIONSERVERID
        - secretKey: JWKSURL
          remoteRef:
            key: applications/6256/non_production/staging/ta/pmt-incoming-money-request-api
            property: JWKSURL
        - secretKey: OKTACLIENTID
          remoteRef:
            key: applications/6256/non_production/staging/ta/pmt-incoming-money-request-api
            property: OKTACLIENTID
        - secretKey: OKTAPRIVATEKEY
          remoteRef:
            key: applications/6256/non_production/staging/ta/pmt-incoming-money-request-api
            property: OKTAPRIVATEKEY

        - secretKey: KAFKA_USERNAME
          remoteRef:
            key: applications/6367/non_production/development/delegated/6256/confluent-cloud/service_accounts/6256.pmt.ta.usr.provide-payment-transaction-data
            property: product-manufacturing-processing-1.lkc-6kyrm8.APIKEY
        - secretKey: KAFKA_PASSWORD
          remoteRef:
            key: applications/6367/non_production/development/delegated/6256/confluent-cloud/service_accounts/6256.pmt.ta.usr.provide-payment-transaction-data
            property: product-manufacturing-processing-1.lkc-6kyrm8.SECRET

        - secretKey: KEYSTORE_PASSWORD
          remoteRef:
            key: applications/6256/non_production/staging/ta/pmt-incoming-money-request-keystore
            property: pwd

        - secretKey: TRUST_STORE_PASSWORD
          remoteRef:
            key: applications/6256/non_production/staging/ta/nbc-root-truststore
            property: pwd

        - secretKey: INTERAC_SIGNING_KEYSTORE_PASSWORD
          remoteRef:
            key: applications/6256/non_production/staging/ta/interac-keystore
            property: pwd

        - secretKey: INTERAC_SIGNING_KEY_ALIAS
          remoteRef:
            key: applications/6256/non_production/staging/ta/interac-keystore
            property: alias
    - target:
        name: server-truststore-certificate
      source: secret-management.9b4eb9c6-dd35-4954-9c8f-b5ca71f60c91
      destination:
        type: files
        path: /mnt/certs/truststore
      data:
        - secretKey: nbc-root-truststore.jks
          remoteRef:
            key: applications/6256/non_production/staging/ta/nbc-root-truststore
            property: jks
            decodingStrategy: Base64

    - target:
        name: keystore-cert
      data:
        - secretKey: keystore-cert.jks
          remoteRef:
            key: applications/6256/non_production/staging/ta/pmt-incoming-money-request-keystore
            property: jks
            decodingStrategy: Base64

        - secretKey: interac-keystore.jks
          remoteRef:
            key: applications/6256/non_production/staging/ta/interac-keystore
            property: jks
            decodingStrategy: Base64
      source: secret-management.9b4eb9c6-dd35-4954-9c8f-b5ca71f60c91
      destination:
        type: files
        path: /mnt/certs/keystore