# Changelog

## APP7873 - Domain: Payment - Application: pmt_dom_et-pmt-incoming-money-request-api

**Description**:  Service description

All notable changes will be documented in this file following [Changelog](https://keepachangelog.com/en/1.0.0/) standard
and [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

```Added``` - for new features.\
```Changed``` - for changes in existing functionality.\
```Deprecated``` - for soon-to-be removed features.\
```Removed``` - for now removed features.\
```Fixed``` - for any bug fixes.\
```Security``` - in case of vulnerabilities.

### **CAUTION
** Access to Git, Jira and Confluence are required ([To make a request](https://jira.bnc.ca/servicedesk/customer/portal/1/create/1849))

Deployment plan (see the 'stages' sections for more information):

- [CI](https://git.bnc.ca/projects/APP7873/repos/shared-aws-ci-pipeline-squad12/browse/vars/sharedPipeline.groovy)
- [CD](https://git.bnc.ca/projects/APP7873/repos/shared-aws-cd-pipeline-squad12/browse/vars/sharedPipeline.groovy)

Code quality and tests: [SonarQube](https://sonar.bnc.ca/dashboard?id=APP7873.ca.bnc.payment%3Apmt_dom_et-pmt-incoming-money-request-api)

## [[0.1.13]] TUE SEP 09 2025

- https://jira.bnc.ca/browse/PPET-73618

```Changed```

- Make jobs in release workflow sequential

## [[0.1.12]]

### - FRI SEP 5 2025

- https://jira.bnc.ca/browse/PPET-73977
- https://jira.bnc.ca/browse/PPET-74778

### ```FIXED```

- Fix mapping error for Interac Money Request status in Dynamo DB
- Fix operation order for decline in money request

## [[0.1.11]]

### - FRI AUG 29 2025

- https://jira.bnc.ca/browse/PPET-73977
- https://jira.bnc.ca/browse/PPET-72884

### ```ADDED```

- refactoring party active identifier data model

### ```FIXED```

- Fix mapping error for Interac Indirect ConnectorId
- Update logging config
- fix Interac Payload signature upon Decline

## [[0.1.10]]

### - FRI AUG 22 2025

- https://jira.bnc.ca/browse/PPET-73692
- https://jira.bnc.ca/browse/PPET-72884
- https://jira.bnc.ca/browse/PPET-73977

### ```FIXED```

- Fixing Controller advice to return HTTP-400 for Incorrect Client Payload instead of HTTP-500
- Update logging full config
- Fixing Mapping Errors For Interac

## [[0.1.8]]

### - WED AUG 20 2025

# https://jira.bnc.ca/browse/PPET-70894

#### ```Added```

- Add configuration for MTLS communication(Data Power Gateway) with Interac in TA

### ```FIXED```

- Fixing Kafka config for TA
- Fixing ThreadPool config for TA
- Fixing PPTD topic
- Fixing contract for accept header

## [[0.1.0]]

### - THU APR 10 2025

- https://jira.bnc.ca/browse/CD-29239
- https://jira.bnc.ca/browse/CD-29253
- https://jira.bnc.ca/browse/CD-29230
- https://jira.bnc.ca/browse/CD-29231
- https://jira.bnc.ca/browse/CD-29232
- https://jira.bnc.ca/browse/CD-29234
- https://jira.bnc.ca/browse/CD-29233
- https://jira.bnc.ca/browse/PPET-63229

#### ```Added```

- Add Initial Commit
- Add Party API resources
- Delete MCP call
- Add dynamoDB timeout error handling
- Add dybnamodb 400 error handling
- Add dynamoDB 500 error handling
- Add sonar config
- Add Money request status validator CTA4
- Split deploy workflow for BNC and OONA
