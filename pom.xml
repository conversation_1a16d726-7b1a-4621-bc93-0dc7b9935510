<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>ca.bnc.payment</groupId>
    <artifactId>pmt-incoming-money-request-api</artifactId>
    <name>pmt-incoming-money-request-api</name>
    <version>0.1.13-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>component-test</module>
        <module>interac-money-request-resources</module>
        <module>pptd-resources</module>
        <module>resources</module>
        <module>service</module>
    </modules>

    <properties>

        <default-package>ca.bnc.payment</default-package>
        <project.encoding>UTF-8</project.encoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>

        <!-- main -->
        <pmt-public-dependencies.version>1.40.7</pmt-public-dependencies.version>

        <!-- lib, keep alphabetized -->
        <pmt-configuration-factories-lib.version>1.7.0</pmt-configuration-factories-lib.version>
        <dd-java-agent.version>1.47.3</dd-java-agent.version>
        <pmt-dynamodb-library.version>1.0.4</pmt-dynamodb-library.version>
        <pmt-health-logger-lib.version>1.6.0</pmt-health-logger-lib.version>
        <pmt-logging-library.version>1.18.2</pmt-logging-library.version>
        <pmt-partners-party-contracts.version>1.0.0</pmt-partners-party-contracts.version>
        <pmt-providers-errors-normalization-lib.version>1.10.2</pmt-providers-errors-normalization-lib.version>
        <pmt-security-library-v2.version>1.30.0</pmt-security-library-v2.version>
        <pmt-selfhealing-indicators-lib.version>1.11.0</pmt-selfhealing-indicators-lib.version>

        <!-- plug-in, keep alphabetized -->
        <download-maven-plugin.version>1.13.0</download-maven-plugin.version>
        <jacoco-maven-plugin.version>0.8.12</jacoco-maven-plugin.version>
        <jib-maven-plugin.version>3.4.5</jib-maven-plugin.version>
        <maven-checkstyle-plugin.version>3.6.0</maven-checkstyle-plugin.version>
        <maven-compiler-plugin.version>3.14.0</maven-compiler-plugin.version>
        <maven-failsafe-plugin.version>3.5.2</maven-failsafe-plugin.version>
        <maven-remote-resources-plugin.version>3.3.0</maven-remote-resources-plugin.version>
        <maven-surefire-plugin.version>3.5.3</maven-surefire-plugin.version>
        <openapi-generator-maven-plugin.version>7.12.0</openapi-generator-maven-plugin.version>
        <pitest-junit5-plugin.version>1.2.2</pitest-junit5-plugin.version>
        <pitest-maven-plugin.version>1.19.0</pitest-maven-plugin.version>
        <spring-boot-maven-plugin.version>3.4.4</spring-boot-maven-plugin.version>
        <wiremock.version>3.12.1</wiremock.version>
        <sonar-maven-plugin.version>5.1.0.4751</sonar-maven-plugin.version>

        <!-- Datadog -->
        <agent-extraction-root>${project.build.directory}/jib-agents</agent-extraction-root>
        <datadog-agent-location>/opt/datadog</datadog-agent-location>
        <datadog-agent-url>
            https://nexus.bnc.ca/repository/maven-central/com/datadoghq/dd-java-agent/${dd-java-agent.version}/dd-java-agent-${dd-java-agent.version}.jar
        </datadog-agent-url>

    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>ca.bnc.payment</groupId>
                <artifactId>pmt-public-dependencies</artifactId>
                <version>${pmt-public-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib-maven-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>${sonar-maven-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

</project>
