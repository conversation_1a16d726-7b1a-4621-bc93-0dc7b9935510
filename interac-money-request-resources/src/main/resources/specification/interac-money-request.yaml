---
# Generated-By: OA3 <PERSON><PERSON> Tool
# Input-File: v3.5/request-for-payment-api-35.yaml
# Options: --encoding=utf-8; --log-level=ERROR; --oa3fix; --overwrite; --prune; --strip-vendor
# -----
openapi: 3.0.0
info:
  description: Request for Payment Services
  version: "3.5.0"
  title: Request for Payment Services
  contact:
    name: eTransfer Support
    url: https://www.interac.ca/en/contact-us-2.html
    email: <EMAIL>
  termsOfService: https://www.interac.ca/en/terms-and-conditions.html
  x-last-updated-date: "05-Oct-2023"
servers:
  - description: Production Environment
    url: https://etransfer-services.interac.ca/request-api/v3.5.0
  - description: FI Test Environment
    url: https://etransfer-services.fit.interac.ca/request-api/v3.5.0
  - description: Beta Test Environment
    url: https://etransfer-services.beta.interac.ca/request-api/v3.5.0
tags:
  - name: request for payment
    description: request for payment services
  - name: request for payment details and history
    description: request for payment  details and history services
  - name: fulfill request for payment
    description: fulfill request for payment services
paths:
  /requests:
    parameters:
      - $ref: "#/components/parameters/ParticipantId"
      - $ref: "#/components/parameters/ParticipantUserId"
      - $ref: "#/components/parameters/IndirectConnectorId"
      - $ref: "#/components/parameters/Authorization"
      - $ref: "#/components/parameters/RequestId"
      - $ref: "#/components/parameters/ChannelIndicator"
      - $ref: "#/components/parameters/Signature"
      - $ref: "#/components/parameters/SignatureType"
      - $ref: "#/components/parameters/TransactionTime"
      - $ref: "#/components/parameters/ProductCode"
    post:
      tags:
        - request for payment
      description: >-
        This service can be used by a Participant to send a new Request for Payment
        to an existing Recipient using an ISO 20022 pain.013 message format.
      summary: Send Request for Payment (ISO 20022 message)
      operationId: sendRequestForPayment
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendRequestForPaymentRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '201':
          description: Request for Payment sent successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendRequestForPaymentResponse'
    get:
      tags:
        - request for payment details and history
      description: >-
        This function is offered to participants so that requestors are able to see
        a list of all outgoing Request For Payments along with their corresponding
        status and details.
      summary: Get a List of Outgoing Request For Payments
      operationId: getOutgoingMoneyRequest
      parameters:
        - in: query
          name: from_date
          description: Start date/time in UTC. Present only if both request_reference_number
            and participantRequestId are absent.
          example: '2020-01-23T12:34:56.000Z'
          required: false
          schema:
            $ref: "#/components/schemas/CustomDateTime"
        - in: query
          name: to_date
          example: '2020-01-23T12:34:56.000Z'
          description: End date/time in UTC. End date/time in UTC. Present only if both
            request_reference_number and participantRequestId are absent, and defaulted
            to defaulted to current time if not provided.
          required: false
          schema:
            $ref: "#/components/schemas/CustomDateTime"
        - in: query
          name: participant_request_id
          description: Participant Request ID associated with the Send Request For Payment
            transaction.
          required: false
          schema:
            $ref: "#/components/schemas/InstructionId"
        - in: query
          name: schedule_reference
          description: Request For Payment Schedule Reference Number associated with
            the Request For Payments if the Request For Payment was created as a result
            of a Request For Payment schedule.
          required: false
          schema:
            $ref: "#/components/schemas/ScheduleReference"
        - in: query
          name: offset
          description: offset is starting point of outgoing Request For Payments filter;
            if offset is not provided it would be defaulted to zero.
          required: false
          schema:
            type: integer
        - in: query
          name: max_response_items
          description: Maximum number of response items to be returned. If fromLastDate
            is provided then maxResponse Items is required. All items are returned if
            this field is absent.
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 999
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Outgoing Request For Payment retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OutgoingRequestResponse'
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-page-size-limit:
              $ref: "#/components/headers/x-et-page-size-limit"
            x-et-page-total-records:
              $ref: "#/components/headers/x-et-page-total-records"
            x-et-page-next-offset:
              $ref: "#/components/headers/x-et-page-next-offset"
  /requests/{id}:
    parameters:
      - $ref: "#/components/parameters/ParticipantId"
      - $ref: "#/components/parameters/ParticipantUserId"
      - $ref: "#/components/parameters/IndirectConnectorId"
      - $ref: "#/components/parameters/Authorization"
      - $ref: "#/components/parameters/RequestId"
      - $ref: "#/components/parameters/ChannelIndicator"
      - $ref: "#/components/parameters/Signature"
      - $ref: "#/components/parameters/SignatureType"
      - $ref: "#/components/parameters/TransactionTime"
      - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    put:
      tags:
        - request for payment
      description: >-
        This function is used to update a request for transfer from an existing Recipient/Contact
        or from an one-time Recipient/Contact.
      summary: Update a Request For Payment
      operationId: updateMoneyRequest
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRequestForPayment'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Request for Payment updated successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
    get:
      tags:
        - request for payment details and history
      description: >-
        This function is offered to participants so that requestors are able to see
        a list of all outgoing Request For Payments along with their corresponding
        status and details.
      summary: Get an Outgoing Request For Payments by Id
      operationId: getOutgoingMoneyRequestById
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Outgoing Request For Payment retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestForPaymentResponse'
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
  /requests/{id}/decline:
    parameters:
      - $ref: "#/components/parameters/ParticipantId"
      - $ref: "#/components/parameters/ParticipantUserId"
      - $ref: "#/components/parameters/IndirectConnectorId"
      - $ref: "#/components/parameters/Authorization"
      - $ref: "#/components/parameters/RequestId"
      - $ref: "#/components/parameters/ChannelIndicator"
      - $ref: "#/components/parameters/Signature"
      - $ref: "#/components/parameters/SignatureType"
      - $ref: "#/components/parameters/TransactionTime"
      - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    post:
      tags:
        - fulfill request for payment
      description: This service is used by the Customer to decline a specific incoming
        Request For Payment.
      summary: Decline Request for Payment (ISO 20022 message)
      operationId: declineRequestForPayment
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeclineRequestForPaymentRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Request for Payment declined successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeclineRequestForPaymentResponse'
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /requests/{id}/reminder:
    parameters:
      - $ref: "#/components/parameters/ParticipantId"
      - $ref: "#/components/parameters/ParticipantUserId"
      - $ref: "#/components/parameters/IndirectConnectorId"
      - $ref: "#/components/parameters/Authorization"
      - $ref: "#/components/parameters/RequestId"
      - $ref: "#/components/parameters/ChannelIndicator"
      - $ref: "#/components/parameters/Signature"
      - $ref: "#/components/parameters/SignatureType"
      - $ref: "#/components/parameters/TransactionTime"
      - $ref: "#/components/parameters/ProductCode"
      - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    post:
      tags:
        - request for payment
      description: >-
        The service also offers a way for the Requestor to reissue the notification
        for a Request For Payment to the Responder (i.e. to send a reminder notice,
        in addition to reminders that are automatically generated by the system).
      summary: Send a Reminder for a Request For Payment
      operationId: reissueMoneyRequestNotification
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Request For Payment reminder sent successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /requests/{id}/cancel:
    parameters:
      - $ref: "#/components/parameters/ParticipantId"
      - $ref: "#/components/parameters/ParticipantUserId"
      - $ref: "#/components/parameters/IndirectConnectorId"
      - $ref: "#/components/parameters/Authorization"
      - $ref: "#/components/parameters/RequestId"
      - $ref: "#/components/parameters/ChannelIndicator"
      - $ref: "#/components/parameters/Signature"
      - $ref: "#/components/parameters/SignatureType"
      - $ref: "#/components/parameters/TransactionTime"
      - $ref: "#/components/parameters/ProductCode"
      - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    post:
      tags:
        - request for payment
      description: >-
        This service is used by the Customer to cancel an incoming Request For Payment.
      summary: Cancel a Request For Payment
      operationId: cancelMoneyRequest
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelRequestForPaymentRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Request For Payment cancelled successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"

  /requests/{id}/incoming:
    parameters:
      - $ref: "#/components/parameters/ParticipantId"
      - $ref: "#/components/parameters/ParticipantUserId"
      - $ref: "#/components/parameters/IndirectConnectorId"
      - $ref: "#/components/parameters/Authorization"
      - $ref: "#/components/parameters/RequestId"
      - $ref: "#/components/parameters/ChannelIndicator"
      - $ref: "#/components/parameters/Signature"
      - $ref: "#/components/parameters/SignatureType"
      - $ref: "#/components/parameters/TransactionTime"
      - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    get:
      tags:
        - fulfill request for payment
      description: >-
        This function is used to start the receive process for an incoming Request
        For Payment.  It returns the Request For Payment details as well as the requestors
        legal name.
      summary: Get Incoming Request for Payment (ISO 20022 message)
      operationId: getIncomingRequestForPayment
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Incoming Request For Payment retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IncomingRequestForPaymentResponse'
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  ## Request For Payment Custom History Services ##
  /requests/detail:
    parameters:
      - $ref: "#/components/parameters/ParticipantId"
      - $ref: "#/components/parameters/ParticipantUserId"
      - $ref: "#/components/parameters/IndirectConnectorId"
      - $ref: "#/components/parameters/Authorization"
      - $ref: "#/components/parameters/RequestId"
      - $ref: "#/components/parameters/ChannelIndicator"
      - $ref: "#/components/parameters/Signature"
      - $ref: "#/components/parameters/SignatureType"
      - $ref: "#/components/parameters/TransactionTime"
    get:
      tags:
        - request for payment details and history
      description: This service can be used by a Participant to retrieve the full
        details of a request for payment sent by one of their Customers (i.e. outgoing)
        or meant to be received by one of their Customers (i.e. incoming).
      summary: Get Request for Payment (ISO 20022 message)
      operationId: getRequestForPayment
      parameters:
        - in: query
          name: clearing_system_reference
          required: false
          schema:
            $ref: "#/components/schemas/ClearingSystemReference"
          description: Interac-generated request reference number.
        - in: query
          name: instruction_identification
          required: false
          schema:
            $ref: "#/components/schemas/InstructionId"
          description: Original InstructionIdentification.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Request for payment details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsoRequestForPaymentResponse'
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"

components:
  headers:
    x-et-page-next-offset:
      description: >
        Pagination parameter - the offset of the 1st record in the next requested
        record set
        against the total records in backend
      schema:
        type: integer
        example: 0
    x-et-page-size-limit:
      description: Pagination parameter - the number of record on each returned page
      schema:
        type: integer
        example: 10
    x-et-page-total-records:
      description: Pagination parameter - the total number of record in the backend
        system
      schema:
        type: integer
        example: 100
    x-et-rate-limit-ceiling:
      description: >
        The rate limit ceiling for that given endpoint in the given window.
      schema:
        type: integer
        format: int64
        example: 120
    x-et-rate-limit-remaining:
      description: >
        The number of requests left for the current window. Since the eTransfer is
        calculating the rate limit based on GMT in HOUR window, the timezones that
        have 30-minute shift should realize the remaining count is based on the clock
        in the hourly-shift zones.
      schema:
        type: integer
        format: int64
        example: 60
    x-et-rate-limit-window:
      description: >
        The rate limit gauging and resetting window. To simplify the complexity of
        the impact of timezone on the rate limiting, eTransfer will use GMT for rate
        limiting window. MINUTE is the default setting unless specified otherwise.
      schema:
        type: string
        enum: ['HOUR', 'MINUTE']
        x-example: MINUTE
    x-et-response-code:
      description: >
        A numeric response code specifying the outcome of the message. A successful
        call will
        return a response code of 0, along with any additional response data.
      schema:
        type: integer
        example: 0
  parameters:
    Authorization:
      in: header
      name: Authorization
      description: >-
        Standard HTTP Header used to implement OAuth 2.0 bearer scheme.
      schema:
        type: string
      required: false
      example: 12345
    ChannelIndicator:
      in: header
      name: x-et-channel-indicator
      description: see components/schemas/ChannelIndicator
      schema:
        $ref: '#/components/schemas/ChannelIndicator'
      required: true
      example: ONLINE
    ClearingSystemReferenceNumber:
      description: The Interac-generated Clearing System Reference Number.
      in: path
      name: id
      required: true
      schema:
        $ref: "#/components/schemas/ClearingSystemReference"
    IndirectConnectorId:
      in: header
      name: x-et-indirect-connector-id
      description: >-
        Financial Institution/Debtor Agent Identifier (not Direct Connector) as defined
        in e-Transfer system.
      schema:
        type: string
        minLength: 1
        maxLength: 35
      required: false
      example: 1
    ParticipantId:
      in: header
      name: x-et-participant-id
      description: >-
        Direct Participant Identifier as defined in e-Transfer system. Participant
        must ensure conformity to the following pattern before transmitting this data
        - CA000xxx where xxx is the Financial Institution Identifier as defined by
        the Canadian Payment Association. If customer's FI connects indirectly through
        a Participant, the participant-id field identifies the direct connector.
      schema:
        type: string
        minLength: 8
        maxLength: 8
      required: true
      example: CA000001
    ParticipantUserId:
      description: Present for all API calls initiated on behalf of a customer. Customer
        ID provided as defined in the Participant system and Customer must be registered
        in the e-Transfer system.
      in: header
      name: x-et-participant-user-id
      schema:
        type: string
        minLength: 1
        maxLength: 35
      required: true
      example: CA000001-user-123
    ProductCode:
      in: header
      name: x-et-product-code
      schema:
        $ref: '#/components/schemas/ProductCode'
    RequestId:
      in: header
      name: x-et-request-id
      description: >-
        Unique ID generated for each request used for message tracking purposes. In
        case of a request retry use the same ID as in the original message.
      schema:
        type: string
        minLength: 1
        maxLength: 36
      required: true
      example: 12345
    Signature:
      in: header
      name: x-et-api-signature
      description: >-
        JWS detached signature of the payload (body only), required for all API calls.
      schema:
        type: string
      required: true
      example: 12345
    SignatureType:
      in: header
      name: x-et-api-signature-type
      description: >-
        The type of the JWT. Required. Allowed values are 'PAYLOAD_DIGEST_SHA256'
      schema:
        $ref: '#/components/schemas/SignatureType'
      required: true
      example: PAYLOAD_DIGEST_SHA256
    TransactionTime:
      in: header
      name: x-et-transaction-time
      description: >-
        A particular point in the progression of time defined and expressed in either
        UTC time format (YYYY-MM-DDThh:mm:ss.sssZ).
      schema:
        $ref: '#/components/schemas/TransactionTime'
      required: true
      example: '2020-01-23T12:34:56.000Z'
  responses:
    400-bad-request:
      description: Bad Request - Validation Errors
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    401-unauthorized:
      description: Unauthorized
    403-forbidden:
      description: Forbidden
    404-not-found:
      description: Resources Not Found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    429-too-many-requests:
      description: Too many requests; blocked due to rate limiting.
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    500-internal-server-error:
      description: Internal Server Error
    503-service-unavailable:
      description: Service Unavailable - The server cannot handle the request for
        a service due to temporary maintenance.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
  schemas:
    AccountIdentification4Choice:
      type: object
      properties:
        other:
          $ref: '#/components/schemas/GenericAccountIdentification1'
      required:
        - other
    AccountSchemeName1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalAccountIdentification1Code'
        - type: object
          properties:
            proprietary:
              description: >-
                Proprietary scheme name used in the identification of the account. Accepted
                values are
                'ALIAS_ACCT_NO' - Identification scheme used by Interac to identify
                the Account alias registration number of an e-Transfer customer
                'BANK_ACCT_NO' - Unique and unambiguous assignment made by a specific
                bank or similar financial institution to identify a relationship as
                defined between the bank and its client
              type: string
              enum: ['ALIAS_ACCT_NO', 'BANK_ACCT_NO']
              x-example: ALIAS_ACCT_NO
    ActiveOrHistoricCurrencyAndAmount:
      type: object
      required:
        - amount
        - currency
      properties:
        amount:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount_SimpleType'
        currency:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyCode'
    ActiveOrHistoricCurrencyAndAmount_SimpleType:
      type: number
      #format: decimal  # fractionDigits=5, totalDigits=18, minInclusive=0
      minimum: 0
      example: 55.55
    ActiveOrHistoricCurrencyCode:
      type: string
      enum: ['CAD', 'USD']
      x-example: CAD
    AddressType2Code:
      type: string
      enum: ['ADDR', 'PBOX', 'HOME', 'BIZZ', 'MLTO', 'DLVY']
      x-example: 'ADDR'
    AddressType3Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/AddressType2Code'
      required:
        - code
    AmountType4Choice:
      type: object
      properties:
        instructed_amount:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
      required:
        - instructed_amount
    AmountWithCurrency:
      type: object
      required:
        - amount
        - currency
      properties:
        amount:
          description: Transaction amount, with the specified currency.
          type: number
          multipleOf: 0.01
          example: 66.66
        #maxLength: 19
        #format: decimal #totalDigits 18 fractionDigits 5
        currency:
          description: ISO 4217 currency code
          type: string
          enum: ['CAD', 'USD']
          x-example: CAD
    BICFIDec2014Identifier:
      type: string
      pattern: '[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}'
      example: AAAADEBBXXX
    BankAccountIdentifier:
      type: object
      required:
        - type
        - account
      properties:
        type:
          description: Bank Account Identifier (CANADIAN - Canadian bank account)
          type: string
          enum: ['CANADIAN']
          x-example: CANADIAN
        account:
          description: Bank account number. Canadian bank account format is aaa-bbbbb-cccccccccccccccccccc
            where 'aaa' is the Financial Institution Identifier 'bbbbb' is the Transit
            Number 'cccccccc...' is the Account Number.
          type: string
          minLength: 1
          maxLength: 34
          example: 123-12345-********90********90
    BranchAndFinancialInstitutionIdentification6:
      type: object
      required:
        - financial_institution_identification
      properties:
        financial_institution_identification:  # FinInstnId
          $ref: '#/components/schemas/FinancialInstitutionIdentification18'
    BusinessName:
      description: Business name. This is required for type 1 (small business) or
        2 (corporation)
      type: object
      properties:
        company_name:
          description: Business/company name, is required while creating customer
            profile.
          type: string
          minLength: 1
          maxLength: 100
          example: Interac
        trade_name:
          description: Trade name
          type: string
          minLength: 1
          maxLength: 100
          example: Interac
    CancelRequestForPaymentRequest:
      type: object
      properties:
        cancel_reason:
          $ref: '#/components/schemas/Reason'
    CashAccount38:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          $ref: '#/components/schemas/AccountIdentification4Choice'
        proxy: # Prxy
          $ref: '#/components/schemas/ProxyAccountIdentification1'
    CategoryPurpose1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalCategoryPurpose1Code'
      required:
        - code
    ChannelIndicator:
      description: |
        Method Sender accessed the service description. <br/>
        Identifies the channel that the customer is using when making the <br/>
        request if the request is initiated by the customer. For requests <br/>
        initiated by an e-Transfer system or a participants system component it <br/>
        identifies the system that makes the request. Values are: <br/>
        ONLINE = Customer online initiated transaction <br/>
        MOBILE = Customer mobile initiated transaction <br/>
        PARTICIPANT_BULK_PAYMENT = Participant payment system initiated Bulk file transaction (*) <br/>
        PARTICIPANT_ETRANSFER_SYSTEM = Participant payment system initiated transaction <br/>
        PARTICIPANT_FRAUD_SYSTEM = Participant fraud detection system initiated transaction <br/>
        ETRANSFER_SYSTEM = e-Transfer system initiated transaction (*) <br/>
        ETRANSFER_FRAUD = e-Transfer fraud detection system initiated transaction (*) <br/>
        EXTERNAL_APPS = External API initiated transaction (*) <br/>
        INTERAC_SDK = Interac Proximity SDK initiated transaction <br/>
        (*) - values not accepted through Participant initiated requests
      type: string
      enum: ['ONLINE', 'MOBILE', 'PARTICIPANT_BULK_PAYMENT', 'PARTICIPANT_ETRANSFER_SYSTEM',
             'PARTICIPANT_FRAUD_SYSTEM', 'ETRANSFER_SYSTEM', 'ETRANSFER_FRAUD', 'EXTERNAL_APPS',
             'INTERAC_SDK']
      x-example: ONLINE
    ChargeBearerType1Code:
      type: string
      description: This identifies which party(ies) will pay changer due for processing
        this transaction. Value is set to 'SLEV', which means Following Service Level
      enum: ['SLEV']
      x-example: SLEV
    ClearingSystemMemberIdentification2:
      type: object
      required:
        - member_identification
      properties:
        member_identification: # MmbId
          description: >-
            Identification of the Instructed/Instructing/Debtor/Creditor Agent.
            The value 'NOTPROVIDED' in the MemberIdentification element can be used
            when this information is not provided or is not available.
          type: string
          minLength: 1
          maxLength: 35
    ClearingSystemReference:
      description: The Interac-generated payment or request for payment reference
        numbers, also known as the clearing system reference in ISO 20022.
      type: string
      minLength: 8
      maxLength: 35
      example: ********
    Contact:
      description: Contact details
      type: object
      required:
        - contact_id
        - contact_name
      properties:
        id:
          description: Unique Recipient/Contact id at Interac, not required for POST
            request
          type: string
          minLength: 8
          maxLength: 35
        type:
          $ref: "#/components/schemas/ContactType"
        name:
          $ref: "#/components/schemas/ContactName"
        notification_preference:
          type: array
          items:
            $ref: "#/components/schemas/NotificationPreference"
    ## START OF COMMON ELEMENTS ##
    Contact4:
      type: object
      properties:
        phone_number: # PhneNb
          $ref: '#/components/schemas/PhoneNumber'
        mobile_number: # MobNb
          $ref: '#/components/schemas/PhoneNumber'
        fax_number: # FaxNb
          $ref: '#/components/schemas/PhoneNumber'
        email_address: # EmailAdr
          type: string
          minLength: 1
          maxLength: 256
          format: email
          example: <EMAIL>
        name: # Nm
          type: string
          minLength: 1
          maxLength: 140
    ContactName:
      description: Recipient's Name
      type: object
      required:
        - alias_name
      properties:
        alias_name:
          description: >-
            'Contact's Name/Alias, as known by the customer.'
          type: string
          minLength: 1
          maxLength: 80
        legal_name:
          $ref: "#/components/schemas/LegalName"
    ContactType:
      description: >-
        'Flag indicating whether the Contact is a business or not. Required if legal
        name is required. (e.g. for for e-Transfer International Remittance) <br/>
        retail <br/> small business <br/> corporation'
      type: string
      enum: ['RETAIL', 'SMALL_BUSINESS', 'CORPORATION']
      x-example: RETAIL
    CountryCode:
      type: string
      description: Only ISO 3166 Alpha-2 codes are allowed.
      enum: ['AA', 'AB', 'AD', 'AE', 'AF', 'AG', 'AI', 'AL', 'AM', 'AN', 'AO', 'AQ',
             'AR', 'AS', 'AT', 'AU', 'AW', 'AX', 'AZ', 'BA', 'BB', 'BD', 'BE', 'BF', 'BG',
             'BH', 'BI', 'BJ', 'BL', 'BM', 'BN', 'BO', 'BQ', 'BR', 'BS', 'BT', 'BV', 'BW',
             'BY', 'BZ', 'C2', 'CA', 'CC', 'CD', 'CF', 'CG', 'CH', 'CI', 'CK', 'CL', 'CM',
             'CN', 'CO', 'CR', 'CU', 'CV', 'CW', 'CX', 'CY', 'CZ', 'DE', 'DJ', 'DK', 'DM',
             'DO', 'DZ', 'EC', 'EE', 'EG', 'EH', 'ER', 'ES', 'ET', 'FI', 'FJ', 'FK', 'FM',
             'FO', 'FR', 'GA', 'GB', 'GD', 'GE', 'GF', 'GG', 'GH', 'GI', 'GL', 'GM', 'GN',
             'GP', 'GQ', 'GR', 'GS', 'GT', 'GU', 'GW', 'GY', 'HK', 'HM', 'HN', 'HR', 'HT',
             'HU', 'ID', 'IE', 'IL', 'IM', 'IN', 'IO', 'IQ', 'IR', 'IS', 'IT', 'JE', 'JM',
             'JO', 'JP', 'K1', 'KE', 'KG', 'KH', 'KI', 'KM', 'KN', 'KP', 'KR', 'KW', 'KY',
             'KZ', 'LA', 'LB', 'LC', 'LI', 'LK', 'LR', 'LS', 'LT', 'LU', 'LV', 'LY', 'MA',
             'MC', 'MD', 'ME', 'MF', 'MG', 'MH', 'MK', 'ML', 'MM', 'MN', 'MO', 'MP', 'MQ',
             'MR', 'MS', 'MT', 'MU', 'MV', 'MW', 'MX', 'MY', 'MZ', 'NA', 'NC', 'NE', 'NF',
             'NG', 'NI', 'NL', 'NO', 'NP', 'NR', 'NU', 'NZ', 'OM', 'PA', 'PE', 'PF', 'PG',
             'PH', 'PK', 'PL', 'PM', 'PN', 'PR', 'PS', 'PT', 'PW', 'PY', 'QA', 'QM', 'QN',
             'QO', 'QP', 'QQ', 'QR', 'QS', 'QT', 'QU', 'QV', 'QW', 'QX', 'QY', 'QZ', 'RE',
             'RO', 'RS', 'RU', 'RW', 'S1', 'SA', 'SB', 'SC', 'SD', 'SE', 'SG', 'SH', 'SI',
             'SJ', 'SK', 'SL', 'SM', 'SN', 'SO', 'SR', 'SS', 'ST', 'SV', 'SX', 'SY', 'SZ',
             'TC', 'TD', 'TF', 'TG', 'TH', 'TJ', 'TK', 'TL', 'TM', 'TN', 'TO', 'TP', 'TR',
             'TT', 'TV', 'TW', 'TZ', 'UA', 'UG', 'UM', 'US', 'UY', 'UZ', 'VA', 'VC', 'VE',
             'VG', 'VI', 'VN', 'VU', 'WF', 'WS', 'XA', 'XB', 'XD', 'XE', 'XF', 'XG', 'XN',
             'XP', 'XQ', 'XR', 'XS', 'XT', 'XU', 'XV', 'XW', 'XY', 'XZ', 'YE', 'YT', 'YU',
             'ZA', 'ZM', 'ZW']
      x-example: CA
    CreditDebitCode:
      type: string
      enum: ['CRDT', 'DBIT']
      x-example: 'CRDT'
    CreditTransferTransaction35:
      type: object
      required:
        - payment_identification
        - amount
        - charge_bearer
        - creditor_agent
        - creditor
      properties:
        payment_identification:  # PmtId
          $ref: '#/components/schemas/PaymentIdentification6'
        amount: # Amt
          $ref: '#/components/schemas/AmountType4Choice'
        charge_bearer: # ChrgBr
          $ref: '#/components/schemas/ChargeBearerType1Code'
        creditor_agent: # CdtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor: # Cdtr
          $ref: '#/components/schemas/PartyIdentification135'
        creditor_account: # CdtrAcct
          $ref: '#/components/schemas/CashAccount38'
        ultimate_creditor: # UltmtCdtr
          $ref: '#/components/schemas/PartyIdentification135'
        related_remittance_information: # RltdRmtInf
          description: >-
            Elements in this data block can be used to specify details related to
            the handling of the remittance information such as remittance location
            and/or
            the unique identification of the remittance information if it was sent
            separately from the payment instruction.
          type: array
          items:
            $ref: '#/components/schemas/RemittanceLocation7'
          maxItems: 1
        remittance_information: # RmtInf
          $ref: '#/components/schemas/RemittanceInformation16'
        enclosed_file: # NclsdFile
          type: array
          items:
            $ref: '#/components/schemas/Document12'
          description: >-
            Document enclosed in the notification.
          maxItems: 1
    CreditorName:
      description: Requesting customer's name (creditor name). If not present the
        creditor name from send request for payment or Legal Name of the Customer
        defined at Customer level will be used in respective order.
      type: string
      minLength: 1
      maxLength: 140
    CreditorPaymentActivationRequestStatusReportV07:
      type: object
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader87'
        original_group_information_and_status: # OrgnlGrpInfAndSts
          $ref: '#/components/schemas/OriginalGroupInformation30'
        original_payment_information_and_status: # OrgnlPmtInfAndSts
          type: array
          items:
            $ref: '#/components/schemas/OriginalPaymentInstruction31'
          description: >-
            Block(s) containing details about the payment requests from the original
            pain.013 message.
          maxItems: 1
      required:
        - group_header
        - original_group_information_and_status
        - original_payment_information_and_status
    CreditorPaymentActivationRequestV07:
      type: object
      required:
        - group_header
        - payment_information
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader78'
        payment_information: # PmtInf
          type: array
          items:
            $ref: '#/components/schemas/PaymentInstruction31'
          minItems: 1
          description: >-
            Payment information block containing the details of this payment request.
            Restriction - only one single occurrence of this data block is allowed.
          maxItems: 1
    CreditorReferenceInformation2:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/CreditorReferenceType2'
        reference: # Ref
          $ref: '#/components/schemas/Max35Text'
    CreditorReferenceType1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/DocumentType3Code'
      required:
        - code
    CreditorReferenceType2:
      type: object
      required:
        - code_or_proprietary
      properties:
        code_or_proprietary:  # CdOrPrtry
          $ref: '#/components/schemas/CreditorReferenceType1Choice'
    CustomDateTime:
      description: |
        A particular point in the progression of time defined and expressed in either UTC time format (YYYY-MM-DDThh:mm:ss.sssZ).
      type: string
      format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
      example: '2020-01-23T12:34:56.000Z'
    Customer:
      description: The customer in the Interac system.
      type: object
      required:
        - type
        - name
      properties:
        type:
          $ref: "#/components/schemas/CustomerType"
        name:
          $ref: '#/components/schemas/CustomerName'
        account:
          $ref: "#/components/schemas/CustomerAccount"
    CustomerAccount:
      type: object
      required:
        - account_holder_name
        - bank_account_identifier
      properties:
        fi_account_id:
          description: Unique FI Identifier for the customer account (tokenized account).
          type: string
          minLength: 1
          maxLength: 50
          example: FI Account
        account_holder_name:
          description: Account holder name.
          type: string
          minLength: 1
          maxLength: 80
          example: FI Account holder name
        bank_account_identifier:
          $ref: '#/components/schemas/BankAccountIdentifier'
    CustomerName:
      description: Customer's name
      type: object
      required:
        - creditor_name
        - registration_name
      properties:
        registration_name:
          description: Customer's primary registration name or alias
          type: string
          minLength: 1
          maxLength: 80
        creditor_name:
          $ref: '#/components/schemas/CreditorName'
    CustomerType:
      description: >-
        Identifies the customer type based on the following codes - <br/>  retail
        <br/> small business <br/> corporation
      type: string
      enum: ['RETAIL', 'SMALL_BUSINESS', 'CORPORATION']
      x-example: RETAIL
    DateAndDateTime2Choice:
      $ref: '#/components/schemas/ISODateTime'
    DeclineRequestForPaymentRequest:
      type: object
      properties:
        decline_reason:
          $ref: '#/components/schemas/Reason'
    DeclineRequestForPaymentResponse:
      type: object
      description: Wrapper element containing Pacs002 and custom data.
      required:
        - fi_to_fi_payment_status_report
      properties:
        fi_to_fi_payment_status_report:
          $ref: '#/components/schemas/FIToFIPaymentStatusReportV10'
    ## NON ISO Messages ##
    Document12:
      type: object
      required:
        - type
        - identification
        - issue_date
        - format
        - enclosure
      properties:
        type:  # Tp
          $ref: '#/components/schemas/DocumentType1Choice'
        identification: # Id
          $ref: '#/components/schemas/Max35Text'
        issue_date: # IsseDt
          $ref: '#/components/schemas/DateAndDateTime2Choice'
        name: # Nm
          $ref: '#/components/schemas/Max140Text'
        format: # Frmt
          $ref: '#/components/schemas/DocumentFormat1Choice'
        file_name: # FileNm
          $ref: '#/components/schemas/Max140Text'
        enclosure: # Nclsr
          $ref: '#/components/schemas/Max10MbBinary'
    DocumentAdjustment1:
      type: object
      required:
        - amount
      properties:
        amount:  # Amt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        credit_debit_indicator: # CdtDbtInd
          $ref: '#/components/schemas/CreditDebitCode'
        reason: # Rsn
          $ref: '#/components/schemas/Max4Text'
        additional_information: # AddtlInf
          $ref: '#/components/schemas/Max140Text'
    DocumentFormat1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalDocumentFormat1Code'
      required:
        - code
    DocumentType1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalDocumentType1Code'
      required:
        - code
    DocumentType3Code:
      type: string
      enum: ['RADM', 'RPIN', 'FXDR', 'DISP', 'PUOR', 'SCOR']
      x-example: 'RADM'
    DocumentType6Code:
      type: string
      enum: ['MSIN', 'CNFA', 'DNFA', 'CINV', 'CREN', 'DEBN', 'HIRI', 'SBIN', 'CMCN',
             'SOAC', 'DISP', 'BOLD', 'VCHR', 'AROI', 'TSUT', 'PUOR']
      x-example: 'MSIN'
    EditableFulfillAmount:
      description: Flag indicating if the transfer amount can be different from the
        requested amount.
      type: boolean
    EmailNotification:
      allOf:
        - $ref: '#/components/schemas/NotificationPreference'
        - type: object
          description: Notification via Email
          required:
            - email
          properties:
            email:
              type: string
              minLength: 1
              maxLength: 256
              format: email
              description: The email of the contact
              example: <EMAIL>
    ErrorModel:
      type: object
      required:
        - code
      properties:
        code:
          type: string
          description: |
            Error list for all the APIs, please refer to the example for API-specific error codes.
          maxLength: 80
          example: 999
        text:
          description: Short error explanation.
          type: string
          maxLength: 2000
          example: Unknown error
    ExternalAccountIdentification1Code:
      type: string
      enum: ['AIIN', 'BBAN', 'CUID', 'UPIC']
      x-example: 'AIIN'
    ExternalCategoryPurpose1Code:
      type: string
      description: This includes iso codes which are 4 letter characters and custom
        which are 3 digit numbers.
      enum: ['BONU', 'CASH', 'CBLK', 'CCRD', 'CORT', 'DCRD', 'DIVI', 'DVPM', 'EPAY',
             'FCOL', 'GOVT', 'HEDG', 'ICCP', 'IDCP', 'INTC', 'INTE', 'LOAN', 'MP2B', 'MP2P',
             'OTHR', 'PENS', 'RPRE', 'RRCT', 'RVPM', 'SALA', 'SECU', 'SSBE', 'SUPP', 'TAXS',
             'TRAD', 'TREA', 'VATX', 'WHLD', '240', '260', '330', '370', '400', '430',
             '460', '480', '452', '308', '311', '318']
      x-example: BONU
    ExternalDocumentFormat1Code:
      type: string
      enum: ['DPDF', 'DXML', 'SDSH', 'WORD', 'XSLT']
      x-example: 'DPDF'
    ExternalDocumentType1Code:
      type: string
      enum: ['CINV', 'CNFA', 'CONT', 'CREN', 'DEBN', 'DISP', 'DNFA', 'HIRI', 'INVS',
             'MSIN', 'PROF', 'PUOR', 'QUOT', 'SBIN', 'SPRR', 'TISH', 'USAR']
      x-example: 'CINV'
    ExternalFinancialInstitutionIdentification1Code:
      type: string
      minLength: 1
      maxLength: 4
      example: CUST
    ExternalOrganisationIdentification1Code:
      type: string
      enum: ['BANK', 'CBID', 'CHID', 'CINC', 'COID', 'CUST', 'DUNS', 'EMPL', 'GS1G',
             'SREN', 'SRET', 'TXID']
      x-example: CUST
    ExternalPaymentTransactionStatus1Code:
      type: string
      enum: ['ACTC', 'PDNG', 'ACSC', 'RJCT', 'ACCC']
      description: >-
        This indicates the transaction processing ISO status. Acceptable codes
        ACTC - AcceptedTechnicalValidation
        PDNG - Pending
        ACSP - Accepted Settlement In progress - indicates transaction was processed
        successfully,
        RJCT - indicates transaction processing has been cancelled
      x-example: ACTC
    ExternalStatusReason1Code:
      type: string
      enum: ['AB01', 'AB02', 'AB03', 'AB04', 'AB05', 'AB06', 'AB07', 'AB08', 'AB09',
             'AB10', 'AC01', 'AC02', 'AC03', 'AC04', 'AC05', 'AC06', 'AC07', 'AC08', 'AC09',
             'AC10', 'AC11', 'AC12', 'AC13', 'AC14', 'AC15', 'AG01', 'AG02', 'AG03', 'AG04',
             'AG05', 'AG06', 'AG07', 'AG08', 'AG09', 'AG10', 'AG11', 'AGNT', 'AM01', 'AM02',
             'AM03', 'AM04', 'AM05', 'AM06', 'AM07', 'AM09', 'AM10', 'AM11', 'AM12', 'AM13',
             'AM14', 'AM15', 'AM16', 'AM17', 'AM18', 'AM19', 'AM20', 'AM21', 'AM22', 'AM23',
             'BE01', 'BE04', 'BE05', 'BE06', 'BE07', 'BE08', 'BE09', 'BE10', 'BE11', 'BE12',
             'BE13', 'BE14', 'BE15', 'BE16', 'BE17', 'BE18', 'BE19', 'BE20', 'BE21', 'BE22',
             'CERI', 'CH03', 'CH04', 'CH07', 'CH09', 'CH10', 'CH11', 'CH12', 'CH13', 'CH14',
             'CH15', 'CH16', 'CH17', 'CH19', 'CH20', 'CH21', 'CH22', 'CNOR', 'CURR', 'CUST',
             'DNOR', 'DS01', 'DS02', 'DS03', 'DS04', 'DS05', 'DS06', 'DS07', 'DS08', 'DS09',
             'DS0A', 'DS0B', 'DS0C', 'DS0D', 'DS0E', 'DS0F', 'DS0G', 'DS0H', 'DS0K', 'DS10',
             'DS11', 'DS12', 'DS13', 'DS14', 'DS15', 'DS16', 'DS17', 'DS18', 'DS19', 'DS20',
             'DS21', 'DS22', 'DS23', 'DS24', 'DS25', 'DS26', 'DS27', 'DT01', 'DT02', 'DT03',
             'DT04', 'DT05', 'DT06', 'DU01', 'DU02', 'DU03', 'DU04', 'DU05', 'DUPL', 'ED01',
             'ED03', 'ED05', 'ED06', 'ERIN', 'FF01', 'FF02', 'FF03', 'FF04', 'FF05', 'FF06',
             'FF07', 'FF08', 'FF09', 'FF10', 'FF11', 'G000', 'G001', 'G002', 'G003', 'G004',
             'G005', 'G006', 'ID01', 'MD01', 'MD02', 'MD05', 'MD06', 'MD07', 'MS02', 'MS03',
             'NARR', 'NERI', 'RC01', 'RC02', 'RC03', 'RC04', 'RC05', 'RC06', 'RC07', 'RC08',
             'RC09', 'RC10', 'RC11', 'RC12', 'RCON', 'RF01', 'RR01', 'RR02', 'RR03', 'RR04',
             'RR05', 'RR06', 'RR07', 'RR08', 'RR09', 'RR10', 'RR11', 'RR12', 'S000', 'S001',
             'S002', 'S003', 'S004', 'SL01', 'SL02', 'SL11', 'SL12', 'SL13', 'SL14', 'TA01',
             'TD01', 'TD02', 'TD03', 'TM01', 'TS01', 'TS04']
      x-example: 'AB01'
    FIToFIPaymentStatusReportV10:
      type: object
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader91'
        transaction_information_and_status: # TxInfAndSts
          type: array
          items:
            $ref: '#/components/schemas/PaymentTransaction110'
          description: >-
            Data block that contains information concerning the original transaction,
            to which the response/status report message refers
            Usage - only one occurrence of this data block is allowed in this response
            message.
          maxItems: 1
      required:
        - group_header
        - transaction_information_and_status
    FinancialIdentificationSchemeName1Choice:
      type: object
      oneOf:
        - type: object
          properties:
            code:
              $ref: '#/components/schemas/ExternalFinancialInstitutionIdentification1Code'
        - type: object
          properties:
            proprietary:
              $ref: '#/components/schemas/Max35Text'
    FinancialInstitutionIdentification18:
      type: object
      properties:
        bicfi:  # BICFI
          $ref: '#/components/schemas/BICFIDec2014Identifier'
        clearing_system_member_identification: # ClrSysMmbId
          $ref: '#/components/schemas/ClearingSystemMemberIdentification2'
        name: # Nm
          $ref: '#/components/schemas/Max140Text'
        postal_address: # PstlAdr
          $ref: '#/components/schemas/PostalAddress24'
        other: # Othr
          $ref: '#/components/schemas/GenericFinancialIdentification1'
      required:
        - clearing_system_member_identification
    FraudCheckResult:
      description: Fraud check results
      type: object
      required:
        - action
      properties:
        score:
          description: Numeric value between 0 and 999 qualifying Fraud Detection
            System assessment of fraud risk.
          type: integer
          minimum: 0
          maximum: 999
          example: 10
        reason:
          description: Fraud Reason from Fraud Detection System
          type: string
          minLength: 1
          maxLength: 256
          example: Fraud reason
        action:
          description: Description of action to be performed to mitigate fraud.  (Allow
            Transfer, Block Transfer, Delay Transfer, User Input Required, No Check
            Performed)
          type: string
          enum: ['ALLOW', 'BLOCK', 'DELAY', 'USER_INPUT_REQUIRED', 'NO_CHECK_PERFORMED']
          x-example: ALLOW
    GenericAccountIdentification1:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          description: >-
            if scheme name is ALIAS_ACCT_NO then this element must contain the Account
            Alias Reference Number (i.e. Autodeposit reference number) generated by
            Interac
            if scheme name is BANK_ACCT_NO then this element must contain the actual
            bank account number. Valid format aaa-bbbbb-cccccccccccccccccccccccc where
              aaa is the Institution Id (fixed length 3 digits)
              bbbbb is the Transit Number (fixed length 5 digits)
              cccccccccccccccccccccccc is the bank account number (up to max 24 digits)
          type: string
          minLength: 1
          maxLength: 34
          example: 123-12345-********90********90
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/AccountSchemeName1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
    GenericFinancialIdentification1:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          $ref: '#/components/schemas/Max35Text'
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/FinancialIdentificationSchemeName1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
    GenericOrganisationIdentification1:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          description: participant user ID at their financial institution (creditor
            / debtor/ initiating party ).
          type: string
          minLength: 1
          maxLength: 35
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/OrganisationIdentificationSchemeName1Choice'
    GroupHeader78:
      type: object
      required:
        - message_identification
        - creation_datetime
        - number_of_transactions
        - initiating_party
      properties:
        message_identification:  # MsgId
          description: >-
            The reference number for this request message. It must be unique (within
            each FI / Interac system) for every request.
          type: string
          minLength: 1
          maxLength: 35
        creation_datetime: # CreDtTm
          $ref: '#/components/schemas/ISODateTime'
        number_of_transactions: # NbOfTxs
          $ref: '#/components/schemas/Max15NumericText'
        initiating_party: # InitgPty
          $ref: '#/components/schemas/PartyIdentification135'
    GroupHeader87:
      type: object
      required:
        - message_identification
        - creation_datetime
        - initiating_party
      properties:
        message_identification:  # MsgId
          description: >-
            The reference number for this request message. It must be unique (within
            each FI / Interac system) for every request.
          type: string
          minLength: 1
          maxLength: 35
        creation_datetime: # CreDtTm
          $ref: '#/components/schemas/ISODateTime'
        initiating_party: # InitgPty
          $ref: '#/components/schemas/PartyIdentification135'
    GroupHeader91:
      type: object
      properties:
        message_identification:  # MsgId
          description: >-
            The reference number for this request message. It must be unique (within
            each FI / Interac system) for every request.
          type: string
          minLength: 1
          maxLength: 35
        creation_datetime: # CreDtTm
          $ref: '#/components/schemas/ISODateTime'
        instructing_agent: # InstgAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        instructed_agent: # InstdAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
      required:
        - message_identification
        - creation_datetime
        - instructing_agent
        - instructed_agent
    ISODate:
      type: string
      format: date
      description: Requested execution date and time for payment request. A particular
        point in the progression of time in a calendar year expressed in the YYYY-MM-DD
        format. This representation is defined in XML Schema Part 2 Datatypes Second
        Edition - W3C Recommendation 28 October 2004 which is aligned with ISO 8601.
      example: '2020-01-23'
    ISODateTime:
      type: string
      description: >-
        "A particular point in the progression of time defined and expressed in either
        UTC time format (YYYY-MM-DDThh:mm:ss.sssZ), local time with UTC offset format
        (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm), or local time format (YYYY-MM- DDThh:mm:ss.sss).
        These representations are defined in XML Schema Part 2: Datatypes Second Edition
        - W3C Recommendation 28 October 2004 which is aligned with ISO 8601. "
      format: date-time
      example: '2020-01-23T12:34:56.000Z'
    IncomingRequestForPaymentResponse:
      type: object
      required:
        - creditor_payment_activation_request
        - request_for_payment_status
        - fraud_check_result
      properties:
        creditor_payment_activation_request:
          $ref: "#/components/schemas/CreditorPaymentActivationRequestV07"
        request_for_payment_status:
          $ref: '#/components/schemas/RequestForPaymentStatus'
        fraud_check_result:
          $ref: "#/components/schemas/FraudCheckResult"
    InstructionId:
      description: Must contain the instruction_id from the Initiate Payment request
        message.
      type: string
      minLength: 1
      maxLength: 35
      example: ********
    IsoRequestForPaymentResponse:
      type: object
      required:
        - creditor_payment_activation_request_status_report
        - request_for_payment_status
      properties:
        creditor_payment_activation_request_status_report:
          $ref: "#/components/schemas/CreditorPaymentActivationRequestStatusReportV07"
        request_for_payment_status:
          $ref: '#/components/schemas/RequestForPaymentStatus'
    Language:
      description: Recipient's Language Preference as defined by Sender
      type: string
      enum: ['EN', 'FR']
      x-example: EN
    LegalName:
      description: Legal name
      type: object
      oneOf:
        - type: object
          properties:
            retail_name:
              $ref: '#/components/schemas/RetailName'
        - type: object
          properties:
            business_name:
              $ref: '#/components/schemas/BusinessName'
    Max105Text:
      type: string
      minLength: 1
      maxLength: 105
    Max10MbBinary:
      type: string
      format: byte
      minLength: 1
      maxLength: 10485760
    Max140Text:
      type: string
      minLength: 1
      maxLength: 140
    Max15NumericText:
      type: string
      pattern: '[0-9]{1,15}'
    Max2048Text:
      type: string
      minLength: 1
      maxLength: 2048
    Max35Text:
      type: string
      minLength: 1
      maxLength: 35
    Max4Text:
      type: string
      minLength: 1
      maxLength: 4
    Max70Text:
      type: string
      minLength: 1
      maxLength: 70
    Memo:
      description: Message to Recipient/Sender
      type: string
      minLength: 1
      maxLength: 420
    NotificationActive:
      description: >-
        'false - Notifications will NOT be sent <br/> true -Notifications will be
        sent'
      type: boolean
      example: true
    NotificationPreference:
      description: List of customer's notifications. Not required for e-Transfer US
        Remittance recipient only customers. Sender's method to notify the recipient.
        At least one notification has to be defined and active for the contact to
        be eligible to receive e-Transfer domestic or e-Transfer US Remittance products.
        If the directDepositData field is present then the notificationPreference
        at contact level cannot be defined.
      type: object
      discriminator:
        propertyName: type
        mapping:
          EMAIL: '#/components/schemas/EmailNotification'
          SMS: '#/components/schemas/SMSNotification'
          RN: '#/components/schemas/RoutedNotification'
      required:
        - type
        - active
      properties:
        type:
          $ref: '#/components/schemas/NotificationType'
        active:
          $ref: '#/components/schemas/NotificationActive'
    NotificationStatus:
      description: >-
        Notification Status shows the state of notification sent to the recipient/contact.
        It is returned if the transfer had a notification sent to the recipient/contact
        to complete the transfer (regular domestic eTransfer). <br/>
        SENT = Sent <br/>
        PENDING  = Pending <br/>
        SEND_FAILURE  = Send Failure <br/>
        DELIVERY_FAILURE = Bounced back
      type: string
      enum: ['SENT', 'PENDING', 'SEND_FAILURE', 'DELIVERY_FAILURE']
      x-example: SENT
    NotificationType:
      type: string
      description: Email <br/> SMS <br/> RN ( Participant_Routed_Notification ) channel
      enum: ['EMAIL', 'SMS', 'RN']
      x-example: EMAIL
    OrganisationIdentification29:
      type: object
      properties:
        other: # Othr
          type: array
          items:
            $ref: '#/components/schemas/GenericOrganisationIdentification1'
      required:
        - other
    OrganisationIdentificationSchemeName1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalOrganisationIdentification1Code'
      required:
        - code
    OriginalGroupInformation29:
      type: object
      required:
        - original_message_identification
        - original_message_name_identification
      properties:
        original_message_identification:  # OrgnlMsgId
          $ref: '#/components/schemas/Max35Text'
        original_message_name_identification: # OrgnlMsgNmId
          $ref: '#/components/schemas/Max35Text'
    OriginalGroupInformation30:
      type: object
      required:
        - original_message_identification
        - original_message_name_identification
      properties:
        original_message_identification:  # OrgnlMsgId
          description: >-
            Cross-reference back to MsgId from the Group Header of the original pain.013
            request message.
          type: string
          minLength: 1
          maxLength: 35
        original_message_name_identification: # OrgnlMsgNmId
          description: >-
            Cross-reference to original message. Value is set to 'pain.013.001.07'.
          type: string
          minLength: 1
          maxLength: 35
        original_creation_datetime: # OrgnlCreDtTm
          $ref: '#/components/schemas/ISODateTime'
    OriginalPaymentInstruction31:
      type: object
      properties:
        original_payment_information_identification:  # OrgnlPmtInfId
          $ref: '#/components/schemas/Max35Text'
        transaction_information_and_status: # TxInfAndSts
          type: array
          items:
            $ref: '#/components/schemas/PaymentTransaction104'
          description: >-
            Block(s) containing details of the payment request.
          maxItems: 1
      required:
        - original_payment_information_identification
        - transaction_information_and_status
    OriginalTransactionReference28:
      type: object
      properties:
        amount: # Amt
          $ref: '#/components/schemas/AmountType4Choice'
        remittance_information: # RmtInf
          $ref: '#/components/schemas/RemittanceInformation16'
        ultimate_debtor: # UltmtDbtr
          $ref: '#/components/schemas/Party40Choice'
        debtor: # Dbtr
          $ref: '#/components/schemas/Party40Choice'
        debtor_account: # DbtrAcct
          $ref: '#/components/schemas/CashAccount38'
        debtor_agent: # DbtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor_agent: # CdtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor: # Cdtr
          $ref: '#/components/schemas/Party40Choice'
        creditor_account: # CdtrAcct
          $ref: '#/components/schemas/CashAccount38'
        ultimate_creditor: # UltmtCdtr
          $ref: '#/components/schemas/Party40Choice'
    OriginalTransactionReference29:
      type: object
      properties:
        amount:  # Amt
          $ref: '#/components/schemas/AmountType4Choice'
        requested_execution_date: # ReqdExctnDt
          $ref: '#/components/schemas/DateAndDateTime2Choice'
        expiry_date: # XpryDt
          $ref: '#/components/schemas/DateAndDateTime2Choice'
        payment_condition: # PmtCond
          $ref: '#/components/schemas/PaymentCondition1'
        payment_type_information: # PmtTpInf
          $ref: '#/components/schemas/PaymentTypeInformation26'
        remittance_information: # RmtInf
          $ref: '#/components/schemas/RemittanceInformation16'
        enclosed_file: # NclsdFile
          type: array
          items:
            $ref: '#/components/schemas/Document12'
          description: >-
            Document enclosed in the notification.
          maxItems: 1
        ultimate_debtor: # UltmtDbtr
          $ref: '#/components/schemas/PartyIdentification135'
        debtor: # Dbtr
          $ref: '#/components/schemas/PartyIdentification135'
        debtor_account: # DbtrAcct
          $ref: '#/components/schemas/CashAccount38'
        debtor_agent: # DbtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor_agent: # CdtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor: # Cdtr
          $ref: '#/components/schemas/PartyIdentification135'
        creditor_account: # CdtrAcct
          $ref: '#/components/schemas/CashAccount38'
        ultimate_creditor: # UltmtCdtr
          $ref: '#/components/schemas/PartyIdentification135'
      required:
        - creditor_agent
        - creditor
        - amount
        - requested_execution_date
        - expiry_date
        - payment_condition
    # CAMT 056 #
    OutgoingRequestResponse:
      type: object
      properties:
        #  description: List of requests returned.
        outgoing_requests:
          type: array
          items:
            $ref: '#/components/schemas/RequestForPaymentResponse'
    Party38Choice:
      type: object
      properties:
        organisation_identification:
          $ref: '#/components/schemas/OrganisationIdentification29'
      required:
        - organisation_identification
    Party40Choice:
      type: object
      properties:
        party:
          $ref: '#/components/schemas/PartyIdentification135'
    # PACS 028 #
    PartyIdentification135:
      type: object
      properties:
        postal_address: # PstlAdr
          $ref: '#/components/schemas/PostalAddress24'
        identification: # Id
          $ref: '#/components/schemas/Party38Choice'
        country_of_residence: # CtryOfRes
          $ref: '#/components/schemas/CountryCode'
        contact_details: # CtctDtls
          $ref: '#/components/schemas/Contact4'
        name:  # Nm
          type: string
          minLength: 1
          maxLength: 140
    PaymentCondition1:
      type: object
      required:
        - amount_modification_allowed
        - early_payment_allowed
        - guaranteed_payment_requested
      properties:
        amount_modification_allowed:  # AmtModAllwd
          description: >-
            Flag indicating if the request for payment can be fulfilled by a paying
            with a different amount than the requested amount. Valid values - True
            or False.
          type: boolean
        early_payment_allowed: # EarlyPmtAllwd
          description: >-
            Indicates if the debtor is allowed to pay before the requested execution
            date. Valid values - True or False.
            Usage- Value within this element will be ignored during processing.
          type: boolean
        guaranteed_payment_requested: # GrntedPmtReqd
          description: >-
            Indicates if a payment guarantee is requested, assuming a payment guarantee
            contract exists between the different actors.
            Usage - Value within this element will be ignored during processing
          type: boolean
    PaymentIdentification6:
      type: object
      properties:
        instruction_identification:  # InstrId
          description: >-
            Creditor FI reference number for this payment request, as set up by Creditor's
            FI.
          type: string
          minLength: 1
          maxLength: 35
        end_to_end_identification: # EndToEndId
          description: >-
            Creditor/Originating Party payment request reference number as set up
            by the Creditor/Originating Party.
            If a reference is not provided by the Creditor then this element should
            be set to 'NOTPROVIDED'
          type: string
          minLength: 1
          maxLength: 35
      required:
        - instruction_identification
        - end_to_end_identification
    PaymentInstruction31:
      type: object
      required:
        - payment_condition
        - expiry_date
        - payment_information_identification
        - payment_method
        - requested_execution_date
        - debtor
        - debtor_agent
        - credit_transfer_transaction
      properties:
        payment_information_identification:  # PmtInfId
          description: >-
            Unique id that can identify this payment information block.
          type: string
          minLength: 1
          maxLength: 35
        payment_method: # PmtMtd
          $ref: '#/components/schemas/PaymentMethod7Code'
        payment_type_information: # PmtTpInf
          $ref: '#/components/schemas/PaymentTypeInformation26'
        requested_execution_date: # ReqdExctnDt
          $ref: '#/components/schemas/DateAndDateTime2Choice'
        expiry_date: # XpryDt
          $ref: '#/components/schemas/DateAndDateTime2Choice'
        payment_condition: # PmtCond
          $ref: '#/components/schemas/PaymentCondition1'
        debtor: # Dbtr
          $ref: '#/components/schemas/PartyIdentification135'
        debtor_agent: # DbtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        ultimate_debtor: # UltmtDbtr
          $ref: '#/components/schemas/PartyIdentification135'
        credit_transfer_transaction: # CdtTrfTx
          type: array
          items:
            $ref: '#/components/schemas/CreditTransferTransaction35'
          minItems: 1
          description: >-
            This block contains specific information about this request such as amount
            and reference ids.
            Restriction - only one single occurrence of this data block is allowed.
          maxItems: 1
    PaymentMethod7Code:
      type: string
      description: This value must be set to TRF (meaning credit transfer).
      enum: ['TRF']
      x-example: TRF
    PaymentTransaction104:
      type: object
      properties:
        original_instruction_identification: # OrgnlInstrId
          description: >-
            Cross-reference back to the instruction_identification from the original
            pain.013 request message.
          type: string
          minLength: 1
          maxLength: 35
        original_end_to_end_identification: # OrgnlEndToEndId
          description: >-
            Cross-reference back to the end_to_end_identification from the original
            pain.013 request message.
          type: string
          minLength: 1
          maxLength: 35
        transaction_status: # TxSts
          $ref: '#/components/schemas/ExternalPaymentTransactionStatus1Code'
        status_reason_information: # StsRsnInf
          type: array
          items:
            $ref: '#/components/schemas/StatusReasonInformation12'
          description: >-
            Wrapper element for data block that contains the Interac-defined response
            code for this message.
            Usage - only one occurrence of this data block is allowed in this response
            message.
          maxItems: 1
        acceptance_datetime: # AccptncDtTm
          $ref: '#/components/schemas/ISODateTime'
        clearing_system_reference: # ClrSysRef
          $ref: '#/components/schemas/Max35Text'
        original_transaction_reference: # OrgnlTxRef
          $ref: '#/components/schemas/OriginalTransactionReference29'
      required:
        - original_instruction_identification
        - original_end_to_end_identification
        - transaction_status
    PaymentTransaction110:
      type: object
      properties:
        original_group_information: # OrgnlGrpInf
          $ref: '#/components/schemas/OriginalGroupInformation29'
        original_instruction_identification: # OrgnlInstrId
          description: >-
            InstructionIdentification (if available) for the original transaction,
            to which the status request message refers.
          type: string
          minLength: 1
          maxLength: 35
        original_end_to_end_identification: # OrgnlEndToEndId
          description: >-
            EndToEndIdentification for the original transaction, to which the status
            request message refers.
          type: string
          minLength: 1
          maxLength: 35
        original_transaction_identification: # OrgnlTxId
          description: >-
            TransactionIdentification for the original transaction, to which the status
            request message refers.
          type: string
          minLength: 1
          maxLength: 35
        transaction_status: # TxSts
          $ref: '#/components/schemas/ExternalPaymentTransactionStatus1Code'
        status_reason_information: # StsRsnInf
          type: array
          items:
            $ref: '#/components/schemas/StatusReasonInformation12'
          description: >-
            Wrapper element for data block that contains the Interac-defined response
            code for this message.
            Usage - only one occurrence of this data block is allowed in this response
            message.
          maxItems: 1
        acceptance_datetime: # AccptncDtTm
          $ref: '#/components/schemas/ISODateTime'
        effective_interbank_settlement_date: # FctvIntrBkSttlmDt
          $ref: '#/components/schemas/DateAndDateTime2Choice'
        account_servicer_reference: # AcctSvcrRef
          $ref: '#/components/schemas/Max35Text'
        clearing_system_reference: # ClrSysRef
          description: >-
            Interac-generated transfer reference number. Present if the request was
            processed successfully and the transaction was found.
          type: string
          minLength: 1
          maxLength: 35
        original_transaction_reference: # OrgnlTxRef
          $ref: '#/components/schemas/OriginalTransactionReference28'
      required:
        - clearing_system_reference
        - original_group_information
        - original_end_to_end_identification
    PaymentTypeInformation26:
      type: object
      properties:
        category_purpose: # CtgyPurp
          $ref: '#/components/schemas/CategoryPurpose1Choice'
    PhoneNumber:
      description: >-
        The collection of information which identifies a specific phone or FAX number
        as defined by telecom services.
        It consists of a '+' followed by the country code (from 1 to 3 characters)
        then a '-' and finally, any combination of numbers, '(', ')', '+' and '-'
        (up to 30 characters).
      type: string
      minLength: 1
      maxLength: 30
      pattern: \+[0-9]{1,3}-[0-9()+\-]{1,30}
      example: ******-555-1212
    PostalAddress24:
      type: object
      properties:
        address_type:  # AdrTp
          $ref: '#/components/schemas/AddressType3Choice'
        department: # Dept
          $ref: '#/components/schemas/Max70Text'
        sub_department: # SubDept
          $ref: '#/components/schemas/Max70Text'
        street_name: # StrtNm
          type: string
          minLength: 1
          maxLength: 70
        building_number: # BldgNb
          type: string
          minLength: 1
          maxLength: 16
        post_code: # PstCd
          type: string
          minLength: 1
          maxLength: 16
        town_name: # TwnNm
          type: string
          minLength: 1
          maxLength: 35
        country_sub_division: # CtrySubDvsn
          $ref: '#/components/schemas/Max35Text'
        country: # Ctry
          $ref: '#/components/schemas/CountryCode'
        address_line: # AdrLine
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 70
          maxItems: 7
    ProductCode:
      description: >-
        'Product for which the transaction belongs. Product Code: <br/> DOMESTIC =
        e-Transfer domestic <br/> INTERNATIONAL = e-Transfer International Remittance'
      type: string
      enum: ['DOMESTIC']
      x-example: DOMESTIC
    ProxyAccountIdentification1:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/ProxyAccountType1Choice'
        identification: # Id
          description: >-
            Depending on the value indicated in the the Proxy.Type/Proprietary above,
            this element contains the actual email, UUID or mobile phone number respectively,
            that was used to register the account alias.
          type: string
          minLength: 1
          maxLength: 256
      description: >-
        The elements in this data block can be used to specify the handle type and
        the handle value that were used during the account alias registration.
        Conditional, If PaymentTypeInformation.LocalInstrument.Proprietary is ACCOUNT_ALIAS_PAYMENT
        or REALTIME_ACCOUNT_ALIAS_PAYMENT , then this block is mandatory.
        In all other cases this block is not required
      required:
        - identification
        - type
    ProxyAccountType1Choice:
      type: object
      properties:
        proprietary:
          description: >-
            Proprietary proxy handle type used in the identification of the creditor
            account. The following values are currently supported and accepted - EMAIL,
            UUID or PHONE
          type: string
          enum: ['EMAIL', 'UUID', 'PHONE']
          x-example: EMAIL
      required:
        - proprietary
    Reason:
      description: Reason for action performed ( cancel, decline, disable etc...)
      type: string
      minLength: 1
      maxLength: 400
    ReferredDocumentInformation7:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/ReferredDocumentType4'
        number: # Nb
          $ref: '#/components/schemas/Max35Text'
        related_date: # RltdDt
          $ref: '#/components/schemas/ISODate'
    ReferredDocumentType3Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/DocumentType6Code'
      required:
        - code
    ReferredDocumentType4:
      type: object
      required:
        - code_or_proprietary
      properties:
        code_or_proprietary:  # CdOrPrtry
          $ref: '#/components/schemas/ReferredDocumentType3Choice'
    RemittanceAmount2:
      type: object
      properties:
        due_payable_amount:  # DuePyblAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        adjustment_amount_and_reason: # AdjstmntAmtAndRsn
          type: array
          items:
            $ref: '#/components/schemas/DocumentAdjustment1'
          description: >-
            Wrapper element for detailed information on the adjusted amount and reason
            of the adjustment.
          maxItems: 1
        remitted_amount: # RmtdAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
    RemittanceInformation16:
      type: object
      properties:
        unstructured:  # Ustrd
          type: array
          items:
            $ref: '#/components/schemas/Max140Text'
          description: Remittance information in an unstructured form. Up to 3 elements
            allowed for a total of 420 characters.
          maxItems: 3
        structured: # Strd
          type: array
          items:
            $ref: '#/components/schemas/StructuredRemittanceInformation16'
          description: Remittance information in a structured form. Up to 5 block
            allowed.
          maxItems: 5
    RemittanceLocation7:
      type: object
      properties:
        remittance_identification:  # RmtId
          description: >-
            Unique identification, assigned by the originator, to unambiguously identify
            the remittance information within the message.
          type: string
          minLength: 1
          maxLength: 35
        remittance_location_details: # RmtLctnDtls
          type: array
          items:
            $ref: '#/components/schemas/RemittanceLocationData1'
          description: Set of elements used to provide information on the location
            and/or delivery of the remittance information.
          maxItems: 1
    RemittanceLocationData1:
      type: object
      properties:
        method:  # Mtd
          $ref: '#/components/schemas/RemittanceLocationMethod2Code'
        electronic_address: # ElctrncAdr
          $ref: '#/components/schemas/Max2048Text'
      required:
        - method
        - electronic_address
    RemittanceLocationMethod2Code:
      type: string
      description: >-
        Method used to deliver the remittance advice information. Restriction - only
        value URID is accepted.
      enum: ['URID']
      x-example: URID
    RequestForPayment:
      type: object
      properties:
        request_reference:
          description: Unique request reference number assigned by Interac.
          type: string
          minLength: 8
          maxLength: 35
        participant_request_id:
          description: Participant Request ID, it has to be unique.
          type: string
          minLength: 1
          maxLength: 35
        request_amount:
          $ref: "#/components/schemas/AmountWithCurrency"
        fulfill_amount:
          #description: Fulfilled Amount. Present if the Request For Payment is in Deposit Complete status.
          $ref: "#/components/schemas/AmountWithCurrency"
        partial_fulfillment_allowed:
          $ref: "#/components/schemas/EditableFulfillAmount"
        request_date:
          $ref: "#/components/schemas/CustomDateTime"
        expiry_date:
          $ref: "#/components/schemas/CustomDateTime"
        request_status:
          $ref: "#/components/schemas/RequestForPaymentStatus"
        notification_status:
          $ref: "#/components/schemas/NotificationStatus"
        schedule_reference:
          description: Request For Payment Schedule Reference Number associated with
            the Request For Payment if the Request For Payment was created as a result
            of a Request For Payment schedule.
          type: string
          minLength: 8
          maxLength: 35
        sender_memo:
          #description: Message to Recipient/Contact
          $ref: '#/components/schemas/Memo'
        responder_memo:
          #description: Recipient's/Contact's rejection message to Customer
          $ref: '#/components/schemas/Memo'
        suppress_responder_notifications:
          description: >-
            'Default value  False <br/> If True - no notification will be generated
            and sent to the Recipient/Contact.'
          type: boolean
        originating_channel_indicator:
          #description: Method Sender accessed the service
          $ref: "#/components/schemas/ChannelIndicator"
        responder_channel_indicator:
          #description: Method responder accessed the service
          $ref: "#/components/schemas/ChannelIndicator"
        gateway_url:
          #description: Gateway Url to be presented to the Recipient/Contact.
          $ref: '#/components/schemas/URL'
        return_url:
          #description:
          $ref: '#/components/schemas/URL'
    RequestForPaymentResponse:
      allOf:
        - $ref: '#/components/schemas/RequestForPayment'
        - type: object
          required:
            - request_reference
            - participant_request_id
            - originating_channel_indicator
            - request_amount
            - partial_fulfillment_allowed
            - gateway_url
            - expiry_date
            - request_date
            - request_status
            - suppress_responder_notifications
            - customer
            - contact
          properties:
            contact:
              $ref: "#/components/schemas/Contact"
            customer:
              $ref: "#/components/schemas/Customer"
            additional_remittance_info:
              description: >-
                This element indicates if there is additional remittance information
                available for this transfer, as follows,
                NOT_PROVIDED - indicates that there is no additional remittance information
                available for this transaction other than the senderMemo (unstructured
                remittance)
                ADVICE_DETAILS - indicates that there is additional structured remittance
                information available for this transaction in the form or remittance
                advice
                LOCATION_DETAILS - indicates that there is additional structured remittance
                information available for this transaction in the form or remittance
                location
                ADVICE_AND_LOCATION_DETAILS - indicates that there is additional remittance
                information available for this transaction in the form of remittance
                advice and remittance location.
              type: string
              enum: ['NOT_PROVIDED', 'ADVICE_DETAILS', 'LOCATION_DETAILS', 'ADVICE_AND_LOCATION_DETAILS']
              x-example: NOT_PROVIDED
    RequestForPaymentStatus:
      type: string
      description: >-
        'Interac defined request to payment status listed <br/> INITIATED <br/> AVAILABLE_TO_BE_FULFILLED
        <br/> FULFILLED <br/>  DECLINED <br/> CANCELLED<br/> EXPIRED <br/> DEPOSIT_FAILED
        <br/> DEPOSIT_COMPLETE'
      enum: ['INITIATED', 'AVAILABLE', 'FULFILLED', 'DECLINED', 'CANCELLED', 'EXPIRED',
             'DEPOSIT_FAILED', 'DEPOSIT_COMPLETE']
      x-example: INITIATED
    ## END OF COMMON ELEMENTS ##
    RetailName:
      description: Retail name. This is required for type 0 (retail)
      type: object
      required:
        - first_name
        - last_name
      properties:
        first_name:
          description: First name
          type: string
          minLength: 1
          maxLength: 100
          example: first_name
        middle_name:
          description: Middle name
          type: string
          minLength: 1
          maxLength: 100
          example: middle_name
        last_name:
          description: Last name
          type: string
          minLength: 1
          maxLength: 100
          example: last_name
    RoutedNotification:
      allOf:
        - $ref: '#/components/schemas/NotificationPreference'
        - type: object
          description: Notification via Participant Routing system
          properties:
            handle:
              type: string
              minLength: 1
              maxLength: 64
              description: The handle of the notification registration at the participant.
              example: support
    # NotificationHandle used in case of registration #
    SMSNotification:
      allOf:
        - $ref: '#/components/schemas/NotificationPreference'
        - type: object
          description: Notification via SMS
          required:
            - phone_number
          properties:
            phone_number:
              $ref: '#/components/schemas/PhoneNumber'
    ScheduleReference:
      description: The Interac-generated schedule reference number, also known as
        the clearing system reference.
      type: string
      minLength: 8
      maxLength: 35
      example: ********
    SendRequestForPaymentRequest:
      description: Wrapper element containing header and Pain013 message
      type: object
      required:
        - creditor_payment_activation_request
        - account_holder_name
      properties:
        creditor_payment_activation_request:
          $ref: "#/components/schemas/CreditorPaymentActivationRequestV07"
        account_holder_name:
          description: Account holder name of the customer that is making the request
            for payment
          type: string
          format: Max80Text
          minLength: 1
          maxLength: 80
        fi_account_id:
          description: Unique FI Identifier for Customer's Account (i.e. Requestors/Creditor's
            account)
          type: string
          format: Max50Text
          minLength: 1
          maxLength: 50
        suppress_responder_notifications:
          description: Flag (true or false) that indicates whether or not Interac
            should suppress the notifications sent to the responder. If True then
            no notification will be generated and sent to the Recipient/Contact. If
            missing, then the default value is false.
          type: boolean
        return_url:
          #description: If present the Customer's (Responders) browser is redirected to the Return URL after the Customer (Responder) fulfills the Request For Payment
          $ref: '#/components/schemas/URL'
        language:
          $ref: "#/components/schemas/Language"
    SendRequestForPaymentResponse:
      description: Root element containing Pain.14 and custom data elements.
      type: object
      required:
        - creditor_payment_activation_request_status_report
        - fraud_check_result
        - gateway_url
      properties:
        creditor_payment_activation_request_status_report:
          $ref: "#/components/schemas/CreditorPaymentActivationRequestStatusReportV07"
        fraud_check_result:
          $ref: "#/components/schemas/FraudCheckResult"
        gateway_url:
          #description: URL for the Gateway page where the Request For Payment can be fulfilled or declined. (Typically used in conjunction with suppressResponderNotifications).
          $ref: '#/components/schemas/URL'
    SignatureType:
      description: >-
        The type of the JWT. Required. Allowed values are 'PAYLOAD_DIGEST_SHA256'
      type: string
      enum: ['PAYLOAD_DIGEST_SHA256']
      x-example: PAYLOAD_DIGEST_SHA256
    StatusReason6Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalStatusReason1Code'
      required:
        - code
    StatusReasonInformation12:
      type: object
      properties:
        reason: # Rsn
          $ref: '#/components/schemas/StatusReason6Choice'
        additional_information: # AddtlInf
          description: |-
            Additional status reason information in a free text format,
            Conditional - if Reason.Code above is NARR then this element is mandatory. Otherwise this element it is optional.
          type: array
          items:
            $ref: '#/components/schemas/Max105Text'
          maxItems: 1
    StructuredRemittanceInformation16:
      type: object
      properties:
        referred_document_information:  # RfrdDocInf
          type: array
          items:
            $ref: '#/components/schemas/ReferredDocumentInformation7'
          description: >-
            Provides detailed information on the cancellation status reason.
          maxItems: 1
        referred_document_amount: # RfrdDocAmt
          $ref: '#/components/schemas/RemittanceAmount2'
        creditor_reference_information: # CdtrRefInf
          $ref: '#/components/schemas/CreditorReferenceInformation2'
        invoicer: # Invcr
          $ref: '#/components/schemas/PartyIdentification135'
        invoicee: # Invcee
          $ref: '#/components/schemas/PartyIdentification135'
        additional_remittance_information: # AddtlRmtInf
          description: >-
            This element is used to provide additional information, in free text form.
            (e.g. invoice description. etc.).
          type: array
          items:
            $ref: '#/components/schemas/Max140Text'
          maxItems: 1
    TransactionTime:
      description: |
        A particular point in the progression of time defined and expressed in either UTC time format (YYYY-MM-DDThh:mm:ss.ssssssZ).
      type: string
      format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
      example: '2020-01-23T12:34:56.000Z'
    URL:
      description: >-
        'URL definition'
      type: string
      minLength: 1
      maxLength: 2000
    UpdateRequestForPayment:
      type: object
      required:
        - request_amount
        - partial_fulfillment_allowed
        - customer_account
      properties:
        request_amount:
          $ref: "#/components/schemas/AmountWithCurrency"
        partial_fulfillment_allowed:
          $ref: '#/components/schemas/EditableFulfillAmount'
        sender_memo:
          $ref: '#/components/schemas/Memo'
        customer_account:
          $ref: "#/components/schemas/CustomerAccount"
        creditor_name:
          $ref: '#/components/schemas/CreditorName'
        suppress_responder_notifications:
          description: >-
            'Default value  False <br/> If True - no notification will be generated
            and sent to the Recipient/Contact.'
          type: boolean
        return_url:
          $ref: '#/components/schemas/URL'
