<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>ca.bnc.payment</groupId>
        <artifactId>pmt-incoming-money-request-api</artifactId>
        <version>0.1.13-SNAPSHOT</version>
    </parent>

    <artifactId>pmt-incoming-money-request-api-interac-money-request-resources</artifactId>
    <name>pmt-incoming-money-request-api (interac-money-request-resources)</name>

    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>pmt-incoming-money-request-api-interac-money-request-resources</finalName>

        <plugins>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi-generator-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/specification/interac-money-request.yaml
                            </inputSpec>
                            <generatorName>spring</generatorName>
                            <apiPackage>${default-package}.interac-money-request.generated.api</apiPackage>
                            <modelPackage>${default-package}.interac-money-request.generated.model</modelPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <configOptions>
                                <serializableModel>false</serializableModel>
                                <generateBuilders>true</generateBuilders>
                                <generatedConstructorWithRequiredArgs>false</generatedConstructorWithRequiredArgs>
                                <sortModelPropertiesByRequiredFlag>false</sortModelPropertiesByRequiredFlag>
                                <sortParamsByRequiredFlag>false</sortParamsByRequiredFlag>
                                <unhandledException>false</unhandledException>
                                <containerDefaultToNull>true</containerDefaultToNull>
                                <interfaceOnly>true</interfaceOnly>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <useSpringBoot3>true</useSpringBoot3>
                                <useOneOfInterfaces>false</useOneOfInterfaces>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-remote-resources-plugin</artifactId>
                <version>${maven-remote-resources-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>bundle</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includes>
                        <include>**/*.yaml</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib-maven-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
