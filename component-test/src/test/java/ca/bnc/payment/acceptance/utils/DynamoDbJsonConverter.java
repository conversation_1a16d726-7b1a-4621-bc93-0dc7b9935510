package ca.bnc.payment.acceptance.utils;

import com.amazonaws.services.dynamodbv2.document.ItemUtils;
import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class DynamoDbJsonConverter {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static Map<String, Object> mapToJson(final String fileName) throws FileNotFoundException, JSONException {
        Map<String, Object> payloadMap = new HashMap<>();
        Objects.requireNonNull(buildMap(fileName))
                .forEach((key, value) -> value
                        .forEach((key1, value1)
                                -> payloadMap.put(key, value1)));

        return payloadMap;
    }

    private static Map<String, Map<String, Object>> buildMap(final String fileName) throws FileNotFoundException, JSONException {
        String logString = FileToStringConverter.getContentString(fileName);
        JSONObject jsonObj = new JSONObject(logString);
        try {
            Map<String, Object> map = objectMapper.readValue(
                    jsonObj.toString(),
                    objectMapper.getTypeFactory().constructMapType(HashMap.class, String.class, Object.class)
            );
            AttributeValue attributeValue = ItemUtils.toAttributeValue(map);

            return ItemUtils.toSimpleMapValue(attributeValue.getM().get("Item").getM());
        } catch (IOException e) {
            throw new RuntimeException("Failed to parse JSON data", e);
        }
    }
}