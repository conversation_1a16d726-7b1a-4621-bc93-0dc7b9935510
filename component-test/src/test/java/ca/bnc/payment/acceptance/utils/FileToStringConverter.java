package ca.bnc.payment.acceptance.utils;

import ca.bnc.payment.lib.config.objectmapper.impl.CustomObjectMapperFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.HttpHeaders;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URL;
import java.util.Map;
import java.util.Scanner;

import static java.util.Objects.isNull;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

public class FileToStringConverter {

    private static final ObjectMapper objectMapper = new CustomObjectMapperFactory().create();

    public static String getContentString(String filename) throws FileNotFoundException {
        return convertMessage(filename);
    }

    private static String convertMessage(String paramFileName) throws FileNotFoundException {
        URL resource = FileToStringConverter.class
                .getClassLoader()
                .getResource(paramFileName);
        File source = new File(resource.getFile());
        return new Scanner(source)
                .useDelimiter("\\Z")
                .next();
    }

    public static HttpHeaders getContentAsHttpHeaders(String filename) throws IOException {
        final Map<String, String> headersMap = objectMapper.readValue(getContentString(filename), Map.class);
        final HttpHeaders httpHeaders = new HttpHeaders();
        headersMap.forEach(httpHeaders::add);
        httpHeaders.add(CONTENT_TYPE, "application/vnd.ca.bnc.pmt+json");
        return httpHeaders;
    }

    public static <T> T getContentObject(final String filename, final Class<T> clazz) throws IOException {
        final URL filePath = findResource(filename);
        if (isNull(filePath)) {
            throw new FileNotFoundException(String.format("File not found: %s", filename));
        }

        final File file = new File(filePath.getFile());
        return objectMapper.readValue(file, clazz);
    }

    @Nullable
    private static URL findResource(String filename) {
        return FileToStringConverter.class
                .getClassLoader()
                .getResource(filename);
    }
}
