package ca.bnc.payment.acceptance.component;

import ca.bnc.payment.acceptance.utils.DynamoDbJsonConverter;
import ca.bnc.payment.acceptance.utils.FileToStringConverter;
import ca.bnc.payment.acceptance.utils.LoggerListAppenderUtil;
import ca.bnc.payment.acceptance.utils.LoggingEventsUtil;
import ca.bnc.payment.moneyrequest.entity.IncomingMoneyRequestEntity;
import ca.bnc.payment.moneyrequest.generated.model.Errors;
import ca.bnc.payment.moneyrequest.util.IdGenerator;
import ca.bnc.payment.pmt_incoming_money_request_api_pptd_resources.generated.pptd.model.DomesticETransferEvent;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import com.amazonaws.services.dynamodbv2.exceptions.AWSExceptionFactory;
import com.amazonaws.services.dynamodbv2.exceptions.AmazonServiceExceptionType;
import com.amazonaws.services.dynamodbv2.local.server.LocalDynamoDBRequestHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.MappingBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.matching.RequestPatternBuilder;
import io.cucumber.java.After;
import io.cucumber.java.Before;
import io.cucumber.java.BeforeAll;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.TopicPartition;
import org.awaitility.Durations;
import org.json.JSONException;
import org.mockito.AdditionalAnswers;
import org.mockito.ArgumentCaptor;
import org.springframework.http.HttpHeaders;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.messaging.Message;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.kafka.KafkaException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.messaging.Message;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.services.dynamodb.model.ResourceNotFoundException;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.willAnswer;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.atMost;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RequiredArgsConstructor
public class CommonStepDefinitions {

    private static final int TIMEOUT = 10000;
    private final ListAppender<ILoggingEvent> loggingEventListAppender = LoggerListAppenderUtil.loggingEventListAppender;
    private static final String DATA_DIRECTORY_PATH = "payloads/component/";
    private static final String GET_INCOMING_MONEY_REQUEST_PATH = "/et_pmt_proc/incoming-money-request/%s";
    private static final String DECLINE_INCOMING_MONEY_REQUEST_PATH = "/et_pmt_proc/incoming-money-request/%s/decline";
    private static final Integer RESPONSE_DELAY_IN_MILLISECONDS = 4000;

    private static final String GET_PARTY_IDENTIFIERS = "/partners-party/%s/identifiers";
    private static final String GET_INTERAC_INCOMING_REQUEST_FOR_PAYMENT = "/requests/%s/incoming";
    private static final String DECLINE_INTERAC_INCOMING_REQUEST_FOR_PAYMENT = "/requests/%s/decline";
    private static final String CORRELATION_ID = "e985bb92-6743-459c-b6ff-c9f99366a95a";
    private static final String PPTD_TOPIC = "payments-dev-pmt-pptd-tpc.etrf.trx.v1.0";
    private static final int KAFKA_ASYNC_WAIT_MS = 500;


    private final MockMvc mockMvc;
    private final ObjectMapper objectMapper;
    private final LoggingEventsUtil loggingEventsUtil;
    private final Clock clock;

    private String interacMoneyRequestId;
    private HttpHeaders requestHeaders;
    private String requestBody;
    private ResultActions response;

    private static WireMockServer wireMockServer;
    private final LocalDynamoDBRequestHandler mockRequestHandler;
    private final DynamoDbTable<IncomingMoneyRequestEntity> incomingMoneyRequestTable;
    private final OktaClientTokenManager oktaClientTokenManagerMock;
    private static final String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9";
    private String interacClientId;
    private Map<String, String> interacHeaders;
    private final IdGenerator idGenerator;
    private final KafkaTemplate<String, Object> kafkaTemplateMock;
    private CompletableFuture<SendResult<String, Object>> future;
    private final ThreadPoolTaskExecutor workerThreadExecutor;

    @BeforeAll
    public static void setUp() {
        wireMockServer = new WireMockServer(WireMockConfiguration.options().bindAddress("127.0.0.1").port(8110));
        wireMockServer.start();
    }

    @Before
    public void setupDynamoDB() {
        try {
            incomingMoneyRequestTable.describeTable();
            future = new CompletableFuture<>();
            loggingEventListAppender.list.clear();
            reset(kafkaTemplateMock);
        } catch (ResourceNotFoundException e) {
            incomingMoneyRequestTable.createTable();
        }
    }

    @After
    public void tearDown() {
        wireMockServer.resetAll();
        incomingMoneyRequestTable.scan().forEach(
                incomingMoneyRequestPage -> incomingMoneyRequestPage.items().forEach(incomingMoneyRequestTable::deleteItem));
        reset(mockRequestHandler);
    }

    @Given("a Get incoming money request for Interac money request id {string} with headers {string}")
    public void incomingMoneyRequest(final String interacMoneyRequestId, final String headersPath) throws IOException {
        this.interacMoneyRequestId = interacMoneyRequestId;
        this.requestHeaders = FileToStringConverter.getContentAsHttpHeaders(DATA_DIRECTORY_PATH + headersPath);
    }

    @Given("the Party API Get Identifiers endpoint for clientId {string} called with headers {string} and query parameters {string} will respond {string}")
    public void thePartyApiGetIdentifiersEndpointCalledWithHeadersAndQueryParametersWillRespond(
            final String clientId,
            final String requestHeadersPath,
            final String requestQueryParametersPath,
            final String responseCodeAndResponseBody
    ) throws IOException {
        stubForPartyApi(clientId, requestHeadersPath, requestQueryParametersPath, responseCodeAndResponseBody);
    }

    @Given("the Interac API Get Incoming Request for Payment endpoint for clientId {string} called with headers {string} will respond {string}")
    public void theInteracApiGetIncomingRequestForPaymentWithHeadersWillResponse(
            final String clientId,
            final String requestHeadersPath,
            final String responseCodeAndResponseBody
    ) throws IOException {
        stubForInteracGetMoneyRequest(clientId, requestHeadersPath, responseCodeAndResponseBody);
    }

    @Given("the Interac API Decline Incoming Request for Payment endpoint for clientId {string} called with headers {string} and body {string} will respond {string}")
    public void theInteracApiDeclineIncomingRequestForPaymentWithHeadersWillResponse(
            final String clientId,
            final String requestHeadersPath,
            final String requestBodyPath,
            final String responseCodeAndResponseBody
    ) throws IOException {
        stubForInteracDeclineMoneyRequest(clientId, requestHeadersPath, requestBodyPath, responseCodeAndResponseBody);
    }

    @Given("DynamoDB responds with {string} for {string} operation")
    public void dynamoDBRespondsWithForOperation(final String response, final String operation) {
        switch (response) {
            case "TIMEOUT" -> {
                stubForTimeout(operation);
            }
            case "400" -> {
                stubFor400(operation);
            }
            case "500" -> {
                stubFor500(operation);
            }
            case "200" -> {
                // No action needed for success
            }
            default -> throw new IllegalArgumentException("Invalid response type");
        }
    }

    @Given("DynamoDB is populated with information from body {string}")
    public void dynamodbIsPopulatedWithInformationFromBody(final String fileName) throws FileNotFoundException, JSONException {
        IncomingMoneyRequestEntity incomingMoneyRequestEntity = objectMapper.convertValue(DynamoDbJsonConverter.mapToJson(DATA_DIRECTORY_PATH + fileName), IncomingMoneyRequestEntity.class);
        incomingMoneyRequestTable.putItem(incomingMoneyRequestEntity);
    }

    @Given("the system dates are set to")
    public void theSystemDatesAreSetTo(final List<String> systemDates) {
        final List<Instant> instants = systemDates.stream()
                .map(Instant::parse)
                .toList();
        if (instants.size() > 1) {
            when(clock.instant()).thenReturn(instants.get(0), instants.stream().skip(1).toArray(Instant[]::new));
        } else {
            when(clock.instant()).thenReturn(instants.get(0));
        }
    }

    @Given("a Interac API Decline incoming money request for Interac money request id {string} with headers {string} and body {string}")
    public void aDeclineIncomingMoneyRequestForInteracMoneyRequestIdWithHeadersAndBody(final String interacMoneyRequestId, final String headersPath, final String bodyPath) throws IOException {
        this.interacMoneyRequestId = interacMoneyRequestId;
        this.requestHeaders = FileToStringConverter.getContentAsHttpHeaders(DATA_DIRECTORY_PATH + headersPath);
        this.requestBody = FileToStringConverter.getContentString(DATA_DIRECTORY_PATH + bodyPath);
    }

    @Given("okta responds with scopes and statuses")
    public void oktaRespondsWithScopesAndStatuses(final Map<String, String> scopeStatusMap) {
        when(oktaClientTokenManagerMock.getAccessToken(any()))
                .thenAnswer(invocation -> {
                    String scope = invocation.getArgument(0, String.class);
                    String status = scopeStatusMap.getOrDefault(scope, "400");

                    if ("201".equals(status)) {
                        return token;
                    }
                    return null;
                });
    }

    @Given("the publication to PPTD is successful")
    public void successfulPublication() {
        when(idGenerator.generateCorrelationId()).thenReturn(CORRELATION_ID);
        when(kafkaTemplateMock.send(any(Message.class))).thenReturn(future);
    }

    @Given("there is an issue with Kafka")
    public void throwErrorInKafka() {
        when(idGenerator.generateCorrelationId()).thenReturn(CORRELATION_ID);
        when(kafkaTemplateMock.send(any(Message.class))).thenThrow(new KafkaException("Send failed"));
    }

    @When("the Get incoming money request is processed")
    public void theGetIncomingMoneyRequestIsProcessed() throws Exception {
        response = mockMvc.perform(MockMvcRequestBuilders
                .get(String.format(GET_INCOMING_MONEY_REQUEST_PATH, this.interacMoneyRequestId))
                .headers(requestHeaders)
                .contentType("application/vnd.ca.bnc.pmt+json")
                .accept("application/vnd.ca.bnc.pmt+json")
        );
    }

    @When("the Decline incoming money request is processed")
    public void theDeclineIncomingMoneyRequestIsProcessed() throws Exception {
        response = mockMvc.perform(MockMvcRequestBuilders
                .post(String.format(DECLINE_INCOMING_MONEY_REQUEST_PATH, this.interacMoneyRequestId))
                .headers(requestHeaders)
                .contentType("application/vnd.ca.bnc.pmt+json")
                .accept("application/vnd.ca.bnc.pmt+json")
                .content(requestBody));
        await().pollDelay(Duration.ofMillis(50)).atMost(Durations.ONE_MINUTE).until(() -> workerThreadExecutor.getActiveCount() == 0);
    }

    @Then("the okta call for scope {string} has {string}")
    public void weExpectOktaCallForScope(final String scope, final String result) {
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);
        verify(oktaClientTokenManagerMock, atLeastOnce()).getAccessToken(captor.capture());
        List<String> calledScopes = captor.getAllValues();
        assertThat(calledScopes).contains(scope);

        String returnedToken = oktaClientTokenManagerMock.getAccessToken(scope);

        switch (result) {
            case "succeeded" -> assertThat(returnedToken).isEqualTo(token);
            case "failed" -> assertThat(returnedToken).isNull();
            default -> throw new IllegalArgumentException(
                    "Wrong argument '%s' as result. Expect 'succeeded' or 'failed'".formatted(result));
        }
    }

    @Then("a {int} status is received with body {string}")
    public void aStatusIsReceivedWithBody(final int status, final String body) throws IOException {
        assertThat(response.andReturn().getResponse().getStatus()).isEqualTo(status);
        if (!"EMPTY".equals(body)) {
            String expectedBody = FileToStringConverter.getContentString(DATA_DIRECTORY_PATH + body);
            assertThat(objectMapper.readValue(response.andReturn().getResponse().getContentAsString(), Errors.class))
                    .isEqualTo(objectMapper.readValue(expectedBody, Errors.class));
        }

    }

    @Then("the log message contains information from file {string}")
    public void theLogMessageContainsInformationFromFile(final String fileName) {
        assertThat(loggingEventListAppender.list).anySatisfy(iLoggingEvent -> loggingEventsUtil.validateLogMessageContains(DATA_DIRECTORY_PATH, fileName));
    }

    @Then("the dynamoDB database is updated with {string}")
    public void theDynamoDBDatabaseIsUpdatedWith(final String dynamoDBFile) throws FileNotFoundException, JSONException {
        IncomingMoneyRequestEntity actualIncomingMoneyRequestEntity = incomingMoneyRequestTable.getItem(Key.builder()
                .partitionValue(interacMoneyRequestId)
                .build());
        IncomingMoneyRequestEntity expectedIncomingMoneyRequestEntity =
                objectMapper.convertValue(DynamoDbJsonConverter.mapToJson(DATA_DIRECTORY_PATH + dynamoDBFile),
                        IncomingMoneyRequestEntity.class);
        assertThat(actualIncomingMoneyRequestEntity).usingRecursiveComparison().isEqualTo(expectedIncomingMoneyRequestEntity);
    }

    @Then("Interac Get Incoming Money Request endpoint is called {int} times")
    public void interacGetIncomingMoneyRequestIsCalledNTimes(final int times) {
        verifyInteracEndpointCalled(times, getRequestedFor(urlPathEqualTo(GET_INTERAC_INCOMING_REQUEST_FOR_PAYMENT.formatted(interacClientId))));
    }

    @Then("Interac Decline Incoming Money Request endpoint is called {int} times")
    public void interacDeclineIncomingMoneyRequestIsCalledNTimes(final int times) {
        verifyInteracEndpointCalled(times, postRequestedFor(urlPathEqualTo(DECLINE_INTERAC_INCOMING_REQUEST_FOR_PAYMENT.formatted(interacClientId))));
    }

    @Then("verify the message {string} is sent to PPTD kafka topic")
    public void verifyTheMessageIsSentToPptdTopic(final String filename) throws IOException {
        verifyTheMessageIsSentToKafkaTopic(filename, PPTD_TOPIC, 1, DomesticETransferEvent.class);
    }

    @Then("there were {int} attempts to send the message to kafka topic")
    public void thereWereAttemptsToSendTheMessageToKafkaTopic(int times) {
        verify(kafkaTemplateMock, timeout(KAFKA_ASYNC_WAIT_MS).times(times)).send(any(Message.class));
    }

    private void verifyInteracEndpointCalled(final int times, final RequestPatternBuilder verification) {
        interacHeaders.forEach((key, value) -> verification.withHeader(key, equalTo(value)));
        wireMockServer.verify(times, verification);
    }

    private void stubForPartyApi(
            final String clientId,
            final String requestHeadersPath,
            final String requestQueryParametersPath,
            final String responseCodeAndPathToResponseBody
    ) throws IOException {
        final MappingBuilder getPartyIdentifiersRequest = WireMock.get(urlPathEqualTo(GET_PARTY_IDENTIFIERS.formatted(clientId)));

        if ("timeout".equals(responseCodeAndPathToResponseBody)) {
            handleTimeoutResponse(getPartyIdentifiersRequest, responseCodeAndPathToResponseBody);
            return;
        }

        applyHeaders(getPartyIdentifiersRequest, requestHeadersPath);

        final Map<String, String> partyQueryParameters = objectMapper.readValue(
                FileToStringConverter.getContentString(DATA_DIRECTORY_PATH + requestQueryParametersPath),
                new TypeReference<>() {
                }
        );
        partyQueryParameters.forEach((key, value) -> getPartyIdentifiersRequest.withQueryParam(key, equalTo(value)));

        createResponse(getPartyIdentifiersRequest, responseCodeAndPathToResponseBody);
    }

    private void stubForInteracGetMoneyRequest(
            final String clientId,
            final String requestHeadersPath,
            final String responseCodeAndPathToResponseBody
    ) throws IOException {
        interacClientId = clientId;
        final MappingBuilder getIncomingRequestForPaymentRequest =
                WireMock.get(urlPathEqualTo(GET_INTERAC_INCOMING_REQUEST_FOR_PAYMENT.formatted(interacClientId)));

        if ("timeout".equals(responseCodeAndPathToResponseBody)) {
            handleTimeoutResponse(getIncomingRequestForPaymentRequest, responseCodeAndPathToResponseBody);
            return;
        }

        applyHeaders(getIncomingRequestForPaymentRequest, requestHeadersPath);

        createResponse(getIncomingRequestForPaymentRequest, responseCodeAndPathToResponseBody);
    }

    private void stubForInteracDeclineMoneyRequest(
            final String clientId,
            final String requestHeadersPath,
            final String requestBodyPath,
            final String responseCodeAndPathToResponseBody
    ) throws IOException {
        interacClientId = clientId;
        final MappingBuilder declineIncomingRequestForPaymentRequest = WireMock
                .post(urlPathEqualTo(DECLINE_INTERAC_INCOMING_REQUEST_FOR_PAYMENT.formatted(interacClientId)))
                .withRequestBody(WireMock.equalToJson(FileToStringConverter.getContentString(DATA_DIRECTORY_PATH + requestBodyPath)));

        if ("timeout".equals(responseCodeAndPathToResponseBody)) {
            handleTimeoutResponse(declineIncomingRequestForPaymentRequest, responseCodeAndPathToResponseBody);
            return;
        }

        applyHeaders(declineIncomingRequestForPaymentRequest, requestHeadersPath);

        createResponse(declineIncomingRequestForPaymentRequest, responseCodeAndPathToResponseBody);
    }

    private void handleTimeoutResponse(final MappingBuilder requestBuilder, final String responseCodeAndPathToResponseBody) {
        if ("timeout".equals(responseCodeAndPathToResponseBody)) {
            wireMockServer.stubFor(
                    requestBuilder.willReturn(
                            WireMock.aResponse().withFixedDelay(RESPONSE_DELAY_IN_MILLISECONDS)
                    )
            );
        }
    }

    private void applyHeaders(final MappingBuilder requestBuilder, final String requestHeadersPath) throws IOException {
        if (requestHeadersPath != null) {
            final Map<String, String> headers = objectMapper.readValue(
                    FileToStringConverter.getContentString(DATA_DIRECTORY_PATH + requestHeadersPath),
                    new TypeReference<>() {
                    }
            );
            headers.forEach((key, value) -> requestBuilder.withHeader(key, equalTo(value)));
            this.interacHeaders = headers;
        }
    }

    private void createResponse(final MappingBuilder requestBuilder, final String responseCodeAndPathToResponseBody) throws IOException {
        final String[] codeAndPath = responseCodeAndPathToResponseBody.split(":", 2);
        final String responseCode = codeAndPath[0];
        final String responseBodyPath = codeAndPath.length > 1 ? codeAndPath[1] : null;
        final String responseBody = responseBodyPath == null
                ? null
                : FileToStringConverter.getContentString(DATA_DIRECTORY_PATH + responseBodyPath);

        wireMockServer.stubFor(
                requestBuilder.willReturn(
                        WireMock.aResponse()
                                .withStatus(Integer.parseInt(responseCode))
                                .withHeader("Content-Type", "application/json")
                                .withBody(responseBody)
                )
        );
    }

    private void stubFor500(final String operation) {
        if (operation.equals("GET")) {
            willThrow(AWSExceptionFactory.buildAWSException(AmazonServiceExceptionType.INTERNAL_SERVER_ERROR))
                    .given(mockRequestHandler)
                    .getItem(any(), any(), any());
        }
        if (operation.equals("PUT")) {
            willThrow(AWSExceptionFactory.buildAWSException(AmazonServiceExceptionType.INTERNAL_SERVER_ERROR))
                    .given(mockRequestHandler)
                    .putItem(any(), any(), any());
        }
    }

    private void stubFor400(final String operation) {
        if (operation.equals("GET")) {
            willThrow(AWSExceptionFactory.buildAWSException(AmazonServiceExceptionType.VALIDATION_EXCEPTION))
                    .given(mockRequestHandler)
                    .getItem(any(), any(), any());
        }

        if (operation.equals("PUT")) {
            willThrow(AWSExceptionFactory.buildAWSException(AmazonServiceExceptionType.VALIDATION_EXCEPTION))
                    .given(mockRequestHandler)
                    .putItem(any(), any(), any());
        }
    }

    private void stubForTimeout(final String operation) {
        if (operation.equals("GET")) {
            willAnswer(AdditionalAnswers.answersWithDelay(TIMEOUT, null))
                    .given(mockRequestHandler)
                    .getItem(any(), any(), any());
        }

        if (operation.equals("PUT")) {
            willAnswer(AdditionalAnswers.answersWithDelay(TIMEOUT, null))
                    .given(mockRequestHandler)
                    .putItem(any(), any(), any());
        }
    }

    public <T> void verifyTheMessageIsSentToKafkaTopic(
            final String filename,
            final String topic,
            final int maxKafkaSendCall,
            final Class<T> valueType
    ) throws IOException {
        sendKafkaKProducerResult();

        @SuppressWarnings("unchecked")
        ArgumentCaptor<Message<T>> messageArgumentCaptor = ArgumentCaptor.forClass(Message.class);

        verify(kafkaTemplateMock, timeout(KAFKA_ASYNC_WAIT_MS).atLeastOnce()).send(messageArgumentCaptor.capture());
        verify(kafkaTemplateMock, atMost(maxKafkaSendCall)).send(any(Message.class));

        T expected = objectMapper.readValue(FileToStringConverter.getContentString(DATA_DIRECTORY_PATH + filename), valueType);
        final List<Message<T>> messages = messageArgumentCaptor
                .getAllValues()
                .stream()
                .filter(event -> Objects.equals(event.getHeaders().get("kafka_topic"), topic))
                .toList();
        assertThat(messages)
                .singleElement()
                .satisfies(message ->
                        assertThat(message.getPayload())
                                .usingRecursiveComparison()
                                .isEqualTo(expected)
                );

    }

    public void sendKafkaKProducerResult() {
        ProducerRecord<String, Object> producerRecord = new ProducerRecord<>("test", "value");
        RecordMetadata recordMetadata = new RecordMetadata(new TopicPartition("test", 1),
                0L, 1, 2L, 2, 2);
        future = new CompletableFuture<>();
        future.complete(new SendResult<>(producerRecord, recordMetadata));
    }
}