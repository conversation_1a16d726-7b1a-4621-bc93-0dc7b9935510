package ca.bnc.payment.acceptance.commons;

import ca.bnc.payment.acceptance.utils.LoggingEventsUtil;
import ca.bnc.payment.moneyrequest.util.IdGenerator;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import com.amazonaws.services.dynamodbv2.local.main.ServerRunner;
import com.amazonaws.services.dynamodbv2.local.server.AbstractLocalDynamoDBServerHandler;
import com.amazonaws.services.dynamodbv2.local.server.DynamoDBProxyServer;
import com.amazonaws.services.dynamodbv2.local.server.LocalDynamoDBRequestHandler;
import org.mockito.AdditionalAnswers;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Clock;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;

@TestConfiguration
public class ComponentTestConfig {

    @Bean
    public LoggingEventsUtil loggingEventsUtil() {
        return new LoggingEventsUtil();
    }

    @Bean
    public DynamoDBProxyServer dynamoDBLocal() throws Exception {
        System.setProperty("sqlite4java.library.path", "native-libs");
        String port = "8001";
        DynamoDBProxyServer server = ServerRunner.createServerFromCommandLineArgs(new String[]{"-inMemory", "-port", port});
        server.start();
        return server;
    }

    @Bean
    public LocalDynamoDBRequestHandler mockRequestHandler(final DynamoDBProxyServer server) {
        final AbstractLocalDynamoDBServerHandler serverHandler =
                (AbstractLocalDynamoDBServerHandler) ReflectionTestUtils.getField(server, "serverHandler");

        final LocalDynamoDBRequestHandler reqHandler =
                (LocalDynamoDBRequestHandler) ReflectionTestUtils.getField(serverHandler, "primaryHandler");

        final LocalDynamoDBRequestHandler mockRequestHandler = spy(reqHandler);

        ReflectionTestUtils.setField(serverHandler, "primaryHandler", mockRequestHandler);

        return mockRequestHandler;
    }

    @Bean
    @Primary
    public OktaClientTokenManager oktaClientTokenManagerMock() {
        return mock(OktaClientTokenManager.class);
    }

    @Bean
    @Primary
    public Clock mockClock() {
        return mock(Clock.class, AdditionalAnswers.delegatesTo(Clock.systemUTC()));
    }

    @Primary
    @Bean
    @SuppressWarnings("unchecked")
    public KafkaTemplate<String, Object> kafkaTemplateMock() {
        return mock(KafkaTemplate.class);
    }

    @Primary
    @Bean
    public IdGenerator mockIdGenerator() {
        return mock(IdGenerator.class);
    }

    @SpyBean
    // Although deprecated, this works as we want, but @MockitoSpyBean does not. See https://github.com/spring-projects/spring-framework/issues/33934
    public ThreadPoolTaskExecutor workerThreadExecutor;
}
