package ca.bnc.payment.acceptance.utils;

import ca.bnc.payment.kafkaproducer.KafkaProducer;
import ca.bnc.payment.moneyrequest.controller.advice.MoneyRequestControllerAdvice;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import lombok.Getter;
import org.slf4j.LoggerFactory;
import org.springframework.util.ClassUtils;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URL;
import java.util.Arrays;
import java.util.Enumeration;

@Getter
public class LoggerListAppenderUtil {

    private static final String BASE_PACKAGE_NAME = "ca.bnc.payment";

    public static final ListAppender<ILoggingEvent> loggingEventListAppender = createLoggingEventListAppender();

    private static ListAppender<ILoggingEvent> createLoggingEventListAppender() {
        try {
            ListAppender<ILoggingEvent> appender = new ListAppender<>();
            appender.start();

            // Add to all classes
            addAppender(appender);

            // Running with <PERSON>ven, only component-test classes are processed above.
            // But from RunCukes ran from IDEA, all application classes are processed
            // Until a solution is found, we must manually configurer the logger of each necessary class.
            ((Logger) LoggerFactory.getLogger(MoneyRequestControllerAdvice.class)).addAppender(appender);
            ((Logger) LoggerFactory.getLogger(KafkaProducer.class)).addAppender(appender);

            return appender;

        } catch (Exception e) {
            throw new RuntimeException("Failed to create logging appender for CTs", e);
        }
    }

    private static void addAppender(final ListAppender<ILoggingEvent> loggingEventListAppender) throws IOException {
        ClassLoader classLoader = ClassUtils.getDefaultClassLoader();
        String path = ClassUtils.convertClassNameToResourcePath(BASE_PACKAGE_NAME);
        Enumeration<URL> resources = classLoader.getResources(path);
        while (resources.hasMoreElements()) {
            URL resource = resources.nextElement();
            addAppender(new File(resource.getFile()), BASE_PACKAGE_NAME, loggingEventListAppender);
        }
    }

    private static void addAppender(final File directory, final String packageName, final ListAppender<ILoggingEvent> loggingEventListAppender) {
        if (!directory.exists()) {
            return;
        }
        Arrays.stream(directory.listFiles())
                .forEach(file -> {
                    if (file.isDirectory()) {
                        addAppender(file, packageName + "." + file.getName(), loggingEventListAppender);
                    } else if (file.getName().endsWith(".class")) {
                        try {
                            String className = packageName + '.' + file.getName().substring(0, file.getName().length() - 6);
                            addAppender(Class.forName(className), loggingEventListAppender);
                        } catch (ClassNotFoundException e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
    }

    private static void addAppender(final Class<?> aClass, final ListAppender<ILoggingEvent> loggingEventListAppender) {
        Arrays.stream(aClass.getFields())
                .filter(field -> Logger.class.equals(field.getType()))
                .findFirst()
                .ifPresent(loggerField -> addAppender(aClass, loggingEventListAppender, loggerField));
    }

    private static void addAppender(final Class<?> aClass, final ListAppender<ILoggingEvent> loggingEventListAppender, final Field loggerField) {
        try {
            loggerField.setAccessible(true);
            Logger logger = (Logger) loggerField.get(aClass);
            logger.addAppender(loggingEventListAppender);
            loggerField.setAccessible(false);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
