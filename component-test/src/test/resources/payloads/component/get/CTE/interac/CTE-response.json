{"creditor_payment_activation_request": {"group_header": {"message_identification": "d04273e9014645c2b12e3ef18ef8589c", "creation_datetime": "2025-04-02T12:34:56.000Z", "number_of_transactions": "1", "initiating_party": {"name": "initiating<PERSON><PERSON><PERSON>"}}, "payment_information": [{"payment_information_identification": "AABB9988", "payment_method": "TRF", "payment_type_information": {"category_purpose": {"code": "BONU"}}, "requested_execution_date": "2025-04-02T12:34:55.000Z", "expiry_date": "2025-04-28T12:34:55.000Z", "payment_condition": {"amount_modification_allowed": true, "early_payment_allowed": true, "guaranteed_payment_requested": true}, "debtor": {"name": "debtor<PERSON>ame"}, "debtor_agent": {"financial_institution_identification": {"name": "debtorAgent"}}, "credit_transfer_transaction": [{"payment_identification": {"instruction_identification": "ZZZZ9999", "end_to_end_identification": "BBBB9999"}, "amount": {"instructed_amount": {"amount": 55.55, "currency": "CAD"}}, "charge_bearer": "SLEV", "creditor_agent": {"financial_institution_identification": {"clearing_system_member_identification": {"member_identification": "NOTPROVIDED"}}}, "creditor": {"contact_details": {"mobile_number": "******-555-1212", "email_address": "<EMAIL>", "name": "creditorName"}, "name": "creditorName"}, "remittance_information": {"unstructured": ["unstructured data", "this is for testing"], "structured": [{"additional_remittance_information": ["string"]}]}, "enclosed_file": [{"type": {"code": "CINV"}, "identification": "identification", "issue_date": "2025-04-01T12:34:55.000Z", "format": {"code": "DPDF"}, "enclosure": "ew0KICAibmFtZSI6ICJNYXgiLA0KICAiYWdlIjogNDINCn0="}]}]}]}, "request_for_payment_status": "AVAILABLE", "fraud_check_result": {"score": 10, "reason": "<PERSON><PERSON> reason", "action": "ALLOW"}}