{"creditorPaymentActivationRequest": {"groupHeader": {"messageIdentification": "d04273e9014645c2b12e3ef18ef8589c", "creationDateTime": "2025-04-02T12:34:56.000Z", "numberOfTransactions": "1", "initiatingParty": {"name": "initiating<PERSON><PERSON><PERSON>"}}, "paymentInformation": {"categoryPurpose": {"code": "ANN"}, "expiryDate": "2025-04-28T12:34:55.000Z", "moneyRequestStatus": "AVAILABLE_TO_BE_FULFILLED", "paymentCondition": {"amountModificationAllowed": true}, "debtor": {"name": "debtor<PERSON>ame"}, "creditTransferTransaction": {"payment_identification": {"instruction_identification": "ZZZZ9999", "end_to_end_identification": "BBBB9999"}, "amount": {"instructedAmount": 55.55, "currency": "CAD"}, "creditorAgent": {"financialInstitutionIdentification": {"clearingSystemMemberIdentification": {"memberIdentification": "NOTPROVIDED"}}}, "creditor": {"name": "creditorName", "contactDetails": {"mobileNumber": "******-555-1212", "emailAddress": "<EMAIL>"}}, "remittance_information": {"unstructured": ["unstructured data", "this is for testing"], "structured": [{"additional_remittance_information": ["string"]}]}, "invoice": {"type": {"code": "CINV", "identification": "identification"}, "issueDate": "2025-04-01T12:34:55.000Z"}}}}}