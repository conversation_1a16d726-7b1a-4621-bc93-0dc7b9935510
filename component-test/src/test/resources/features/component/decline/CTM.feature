Feature: Incoming money request - Component Tests for decline CTMx - Documentation: https://wiki.bnc.ca/x/aqJ5hQ

  Scenario: CTM1 - Contract is not respected by client application
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTM/CTM1-request-headers.json" and body "decline/CTM/CTM1-request-body.json"
    When the Decline incoming money request is processed
    Then a 400 status is received with body "decline/CTM/CTM1-response.json"
    And the log message contains information from file "decline/CTM/logging/CTM1_service_log.json"