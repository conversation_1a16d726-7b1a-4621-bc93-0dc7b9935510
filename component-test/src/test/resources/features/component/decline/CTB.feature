Feature: Incoming money request - Component Tests CTBx - Documentation: https://wiki.bnc.ca/x/L1TWgw

  Scenario: CTB1 - Party API is not reachable
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTB/CTB-request-headers.json" and body "decline/CTB/CTB-request-body.json"
    And DynamoDB is populated with information from body "decline/CTB/dynamoDB/CTB_prepopulated.json"
    And ok<PERSON> responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTB/party/CTB-request-headers.json" and query parameters "decline/CTB/party/CTB-query-params.json" will respond "timeout"
    When the Decline incoming money request is processed
    Then a 500 status is received with body "decline/CTB/CTB1-response.json"
    And the okta call for scope "partyScope" has "succeeded"
    And the log message contains information from file "get/CTB/logging/CTB1_service_log.json"

  Scenario Outline: <CTB> - Party API returns <scenario>
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTB/CTB-request-headers.json" and body "decline/CTB/CTB-request-body.json"
    And DynamoDB is populated with information from body "decline/CTB/dynamoDB/CTB_prepopulated.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTB/party/CTB-request-headers.json" and query parameters "decline/CTB/party/CTB-query-params.json" will respond "<party_response_code>:decline/CTB/party/<CTB>-response.json"
    When the Decline incoming money request is processed
    Then a <money_request_response_code> status is received with body "decline/CTB/<CTB>-response.json"
    And the okta call for scope "partyScope" has "succeeded"
    And the log message contains information from file "decline/CTB/logging/<CTB>_service_log.json"
    Examples:
      | CTB  | scenario                       | party_response_code | money_request_response_code |
      | CTB2 | HTTP 400                       | 400                 | 500                         |
      | CTB3 | HTTP 500                       | 500                 | 500                         |
      | CTB5 | HTTP 200 but no participant id | 200                 | 400                         |

  Scenario: CTB4 - Party API returns HTTP 503
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTB/CTB-request-headers.json" and body "decline/CTB/CTB-request-body.json"
    And DynamoDB is populated with information from body "decline/CTB/dynamoDB/CTB_prepopulated.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTB/party/CTB-request-headers.json" and query parameters "get/CTB/party/CTB-query-params.json" will respond "503"
    When the Decline incoming money request is processed
    Then a 500 status is received with body "decline/CTB/CTB4-response.json"
    And the okta call for scope "partyScope" has "succeeded"
    And the log message contains information from file "decline/CTB/logging/CTB4_service_log.json"

  Scenario Outline: <CTB> - Okta returns with <okta_status>
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTB/CTB-request-headers.json" and body "decline/CTB/CTB-request-body.json"
    And DynamoDB is populated with information from body "decline/CTB/dynamoDB/CTB_prepopulated.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | <okta_status> |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTB/party/CTB-request-headers.json" and query parameters "decline/CTB/party/CTB-query-params.json" will respond "503"
    When the Decline incoming money request is processed
    Then a 500 status is received with body "decline/CTB/CTB-response.json"
    And the okta call for scope "partyScope" has "failed"
    And the log message contains information from file "decline/CTB/logging/CTB_service_log.json"
    Examples:
      | CTB    | okta_status |
      | CTB6.1 | 401         |
      | CTB6.2 | 400         |