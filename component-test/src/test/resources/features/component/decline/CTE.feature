Feature: Incoming money request - Component Tests CTEx - Documentation: https://wiki.bnc.ca/x/B15ihg

  Scenario: CTE1 - Happy Path - Send PPTD message with success
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTE/CTE-request-headers.json" and body "decline/CTE/CTE-request-body.json"
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And <PERSON><PERSON> responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And DynamoDB is populated with information from body "decline/CTE/dynamoDB/CTE_prepopulated.json"
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTE/party/CTE-request-headers.json" and query parameters "decline/CTE/party/CTE-query-params.json" will respond "200:decline/CTE/party/CTE-response.json"
    And the Interac API Decline Incoming Request for Payment endpoint for clientId "123456789" called with headers "decline/CTE/interac/CTE-request-headers.json" and body "decline/CTE/interac/CTE-request-body.json" will respond "200:decline/CTE/interac/CTE-response.json"
    And DynamoDB responds with "200" for "PUT" operation
    And the publication to PPTD is successful
    When the Decline incoming money request is processed
    Then a 204 status is received with body "EMPTY"
    And the dynamoDB database is updated with "decline/CTE/dynamoDB/CTE1_dynamoDB_State.json"
    And verify the message "decline/CTE/CTE_kafka.json" is sent to PPTD kafka topic

  Scenario: CTE2 - Happy Path but could not send kafka message
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTE/CTE-request-headers.json" and body "decline/CTE/CTE-request-body.json"
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And DynamoDB is populated with information from body "decline/CTE/dynamoDB/CTE_prepopulated.json"
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTE/party/CTE-request-headers.json" and query parameters "decline/CTE/party/CTE-query-params.json" will respond "200:decline/CTE/party/CTE-response.json"
    And the Interac API Decline Incoming Request for Payment endpoint for clientId "123456789" called with headers "decline/CTE/interac/CTE-request-headers.json" and body "decline/CTE/interac/CTE-request-body.json" will respond "200:decline/CTE/interac/CTE-response.json"
    And DynamoDB responds with "200" for "PUT" operation
    And there is an issue with Kafka
    When the Decline incoming money request is processed
    Then a 204 status is received with body "EMPTY"
    And the dynamoDB database is updated with "decline/CTE/dynamoDB/CTE1_dynamoDB_State.json"
    And there were 3 attempts to send the message to kafka topic
    And the log message contains information from file "decline/CTE/logging/CTE2_kafka_log.json"