Feature: Incoming money request - Component Tests CTDx - Documentation: https://wiki.bnc.ca/x/ELJ5hQ

  Scenario Outline: <CTD> - DynamoDB UpdateItem operation returns error
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTD/CTD-request-headers.json" and body "decline/CTD/CTD-request-body.json"
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And <PERSON><PERSON> responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And DynamoDB is populated with information from body "decline/CTD/dynamoDB/CTD_prepopulated.json"
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTD/party/CTD-request-headers.json" and query parameters "decline/CTD/party/CTD-query-params.json" will respond "200:decline/CTD/party/CTD-response.json"
    And the Interac API Decline Incoming Request for Payment endpoint for clientId "123456789" called with headers "decline/CTD/interac/CTD-request-headers.json" and body "decline/CTD/interac/CTD-request-body.json" will respond "200:decline/CTD/interac/CTD-response.json"
    And DynamoDB responds with "<dynamodb_response>" for "PUT" operation
    When the Decline incoming money request is processed
    Then a 500 status is received with body "<response_body>"
    And the log message contains information from file "<log_file>"
    Examples:
      | CTD  | dynamodb_response | response_body                  | log_file                                  |
      | CTD1 | TIMEOUT           | decline/CTD/CTD1-response.json | decline/CTD/logging/CTD1_service_log.json |
      | CTD2 | 400               | decline/CTD/CTD2-response.json | decline/CTD/logging/CTD2_service_log.json |
      | CTD3 | 500               | decline/CTD/CTD3-response.json | decline/CTD/logging/CTD3_service_log.json |

  Scenario: CTD4 - Happy Path - DynamoDB UpdateItem operation returns HTTP 200
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTD/CTD-request-headers.json" and body "decline/CTD/CTD-request-body.json"
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And DynamoDB is populated with information from body "decline/CTD/dynamoDB/CTD_prepopulated.json"
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTD/party/CTD-request-headers.json" and query parameters "decline/CTD/party/CTD-query-params.json" will respond "200:decline/CTD/party/CTD-response.json"
    And the Interac API Decline Incoming Request for Payment endpoint for clientId "123456789" called with headers "decline/CTD/interac/CTD-request-headers.json" and body "decline/CTD/interac/CTD-request-body.json" will respond "200:decline/CTD/interac/CTD-response.json"
    And DynamoDB responds with "200" for "PUT" operation
    When the Decline incoming money request is processed
    Then a 204 status is received with body "EMPTY"
    And the dynamoDB database is updated with "decline/CTD/dynamoDB/CTD4_dynamoDB_State.json"