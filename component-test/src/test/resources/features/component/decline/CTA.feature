Feature: Incoming money request - Component Tests CTAx - Documentation - https://wiki.bnc.ca/x/UQZ5h

  Scenario: CTA1 - DynamoDB does not reply
    Given a Interac API Decline incoming money request for Interac money request id "*********" with headers "decline/CTA/CTA-request-headers.json" and body "decline/CTA/CTA-request-body.json"
    And DynamoDB responds with "TIMEOUT" for "GET" operation
    When the Decline incoming money request is processed
    Then a 500 status is received with body "decline/CTA/CTA1-response.json"
    And the log message contains information from file "decline/CTA/logging/CTA1_service_log.json"

  Scenario: CTA2 - <PERSON>D<PERSON> failed with HTTP-400 error
    Given a Interac API Decline incoming money request for Interac money request id "*********" with headers "decline/CTA/CTA-request-headers.json" and body "decline/CTA/CTA-request-body.json"
    And DynamoDB responds with "400" for "GET" operation
    When the Decline incoming money request is processed
    Then a 500 status is received with body "decline/CTA/CTA2-response.json"
    And the log message contains information from file "decline/CTA/logging/CTA2_service_log.json"

  Scenario: CTA3 - <PERSON>DB failed with HTTP-500 error
    Given a Interac API Decline incoming money request for Interac money request id "*********" with headers "decline/CTA/CTA-request-headers.json" and body "decline/CTA/CTA-request-body.json"
    And DynamoDB responds with "500" for "GET" operation
    When the Decline incoming money request is processed
    Then a 500 status is received with body "decline/CTA/CTA3-response.json"
    And the log message contains information from file "decline/CTA/logging/CTA3_service_log.json"

  Scenario: CTA4 - DynamoDB succeeded with HTTP-200 but response is empty
    Given a Interac API Decline incoming money request for Interac money request id "*********" with headers "decline/CTA/CTA-request-headers.json" and body "decline/CTA/CTA-request-body.json"
    When the Decline incoming money request is processed
    Then a 404 status is received with body "EMPTY"
    And the log message contains information from file "decline/CTA/logging/CTA4_service_log.json"

  Scenario: CTA5 - DynamoDB succeeded with HTTP-200 but entity is in an invalid status
    Given a Interac API Decline incoming money request for Interac money request id "*********" with headers "decline/CTA/CTA-request-headers.json" and body "decline/CTA/CTA-request-body.json"
    And DynamoDB is populated with information from body "decline/CTA/dynamoDB/CTA5_prepopulated.json"
    When the Decline incoming money request is processed
    Then a 400 status is received with body "decline/CTA/CTA5-response.json"
    And the log message contains information from file "decline/CTA/logging/CTA5_service_log.json"