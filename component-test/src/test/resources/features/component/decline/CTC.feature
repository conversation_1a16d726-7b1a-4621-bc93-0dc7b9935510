Feature: Incoming money request - Unsuccessful Component Tests CTCx - Documentation: https://wiki.bnc.ca/x/e6V5hQ

  Scenario: CTC1 - Interac API is not reachable
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTC/CTC-request-headers.json" and body "decline/CTC/CTC-request-body.json"
    And DynamoDB is populated with information from body "decline/CTC/dynamoDB/CTC_prepopulated.json"
    And <PERSON><PERSON> responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTC/party/CTC-request-headers.json" and query parameters "decline/CTC/party/CTC-query-params.json" will respond "200:decline/CTC/party/CTC-response.json"
    And the Interac API Decline Incoming Request for Payment endpoint for clientId "123456789" called with headers "decline/CTC/interac/CTC-request-headers.json" and body "decline/CTC/interac/CTC-request-body.json" will respond "timeout"
    When the Decline incoming money request is processed
    Then a 500 status is received with body "decline/CTC/CTC1-response.json"
    And the log message contains information from file "decline/CTC/logging/CTC1_service_log.json"

  Scenario: CTC2 - Interac API returns HTTP 400
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTC/CTC-request-headers.json" and body "decline/CTC/CTC-request-body.json"
    And DynamoDB is populated with information from body "decline/CTC/dynamoDB/CTC_prepopulated.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTC/party/CTC-request-headers.json" and query parameters "decline/CTC/party/CTC-query-params.json" will respond "200:decline/CTC/party/CTC-response.json"
    And the Interac API Decline Incoming Request for Payment endpoint for clientId "123456789" called with headers "decline/CTC/interac/CTC-request-headers.json" and body "decline/CTC/interac/CTC-request-body.json" will respond "400:decline/CTC/interac/CTC2-response.json"
    When the Decline incoming money request is processed
    Then a 400 status is received with body "decline/CTC/CTC2-response.json"
    And the log message contains information from file "decline/CTC/logging/CTC2_service_log.json"

  Scenario Outline: <CTC> - Interac API returns <scenario>
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTC/CTC-request-headers.json" and body "decline/CTC/CTC-request-body.json"
    And DynamoDB is populated with information from body "decline/CTC/dynamoDB/CTC_prepopulated.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTC/party/CTC-request-headers.json" and query parameters "decline/CTC/party/CTC-query-params.json" will respond "200:decline/CTC/party/CTC-response.json"
    And the Interac API Decline Incoming Request for Payment endpoint for clientId "123456789" called with headers "decline/CTC/interac/CTC-request-headers.json" and body "decline/CTC/interac/CTC-request-body.json" will respond "<interac_response_code>"
    When the Decline incoming money request is processed
    Then a <money_request_response_code> status is received with body "decline/CTC/<CTC>-response.json"
    And the log message contains information from file "decline/CTC/logging/<CTC>_service_log.json"
    Examples:
      | CTC  | scenario | interac_response_code | money_request_response_code |
      | CTC3 | HTTP 401 | 401                   | 500                         |
      | CTC4 | HTTP 403 | 403                   | 500                         |
      | CTC5 | HTTP 429 | 429                   | 500                         |
      | CTC6 | HTTP 500 | 500                   | 500                         |

  Scenario: CTC7 - Interac API returns HTTP 503
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTC/CTC-request-headers.json" and body "decline/CTC/CTC-request-body.json"
    And DynamoDB is populated with information from body "decline/CTC/dynamoDB/CTC_prepopulated.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTC/party/CTC-request-headers.json" and query parameters "decline/CTC/party/CTC-query-params.json" will respond "200:decline/CTC/party/CTC-response.json"
    And the Interac API Decline Incoming Request for Payment endpoint for clientId "123456789" called with headers "decline/CTC/interac/CTC-request-headers.json" and body "decline/CTC/interac/CTC-request-body.json" will respond "503:decline/CTC/interac/CTC7-response.json"
    When the Decline incoming money request is processed
    Then a 500 status is received with body "decline/CTC/CTC7-response.json"
    And Interac Decline Incoming Money Request endpoint is called 3 times
    And the log message contains information from file "decline/CTC/logging/CTC7_service_log.json"

  Scenario: CTC8 - Interac API returns HTTP 404
    Given a Interac API Decline incoming money request for Interac money request id "123456789" with headers "decline/CTC/CTC-request-headers.json" and body "decline/CTC/CTC-request-body.json"
    And DynamoDB is populated with information from body "decline/CTC/dynamoDB/CTC_prepopulated.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "decline/CTC/party/CTC-request-headers.json" and query parameters "decline/CTC/party/CTC-query-params.json" will respond "200:decline/CTC/party/CTC-response.json"
    And the Interac API Decline Incoming Request for Payment endpoint for clientId "123456789" called with headers "decline/CTC/interac/CTC-request-headers.json" and body "decline/CTC/interac/CTC-request-body.json" will respond "404:decline/CTC/interac/CTC8-response.json"
    When the Decline incoming money request is processed
    Then a 404 status is received with body "EMPTY"
    And the log message contains information from file "decline/CTC/logging/CTC8_service_log.json"