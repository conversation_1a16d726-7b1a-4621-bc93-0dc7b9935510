Feature: Incoming money request - Component Tests CTBx - Documentation: https://wiki.bnc.ca/x/L1TWgw

  Scenario: CTB1 - Party API is not reachable
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTB/CTB-request-headers.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTB/party/CTB-request-headers.json" and query parameters "get/CTB/party/CTB-query-params.json" will respond "timeout"
    When the Get incoming money request is processed
    Then a 500 status is received with body "get/CTB/CTB1-response.json"
    And the okta call for scope "partyScope" has "succeeded"
    And the log message contains information from file "get/CTB/logging/CTB1_service_log.json"

  Scenario Outline: <CTB> - Party API returns <scenario>
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTB/CTB-request-headers.json"
    And ok<PERSON> responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTB/party/CTB-request-headers.json" and query parameters "get/CTB/party/CTB-query-params.json" will respond "<party_response_code>:get/CTB/party/<CTB>-response.json"
    When the Get incoming money request is processed
    Then a <money_request_response_code> status is received with body "get/CTB/<CTB>-response.json"
    And the okta call for scope "partyScope" has "succeeded"
    And the log message contains information from file "get/CTB/logging/<CTB>_service_log.json"
    Examples:
      | CTB  | scenario                       | party_response_code | money_request_response_code |
      | CTB2 | HTTP 400                       | 400                 | 500                         |
      | CTB3 | HTTP 500                       | 500                 | 500                         |
      | CTB5 | HTTP 200 but no participant id | 200                 | 400                         |

  Scenario: CTB4 - Party API returns HTTP 503
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTB/CTB-request-headers.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTB/party/CTB-request-headers.json" and query parameters "get/CTB/party/CTB-query-params.json" will respond "503"
    When the Get incoming money request is processed
    Then a 500 status is received with body "get/CTB/CTB4-response.json"
    And the okta call for scope "partyScope" has "succeeded"
    And the log message contains information from file "get/CTB/logging/CTB4_service_log.json"

  Scenario: CTB6 - Okta responded with an error
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTB/CTB-request-headers.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 401    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTB/party/CTB-request-headers.json" and query parameters "get/CTB/party/CTB-query-params.json" will respond "503"
    When the Get incoming money request is processed
    Then a 500 status is received with body "get/CTB/CTB6-response.json"
    And the okta call for scope "partyScope" has "failed"
    And the log message contains information from file "get/CTB/logging/CTB6_service_log.json"