Feature: Incoming money request - Component Tests CTEx - Documentation: https://wiki.bnc.ca/x/fnvWgw

  Scenario Outline: CTE - save Item operation from AWS DynamoDB API returns error
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTE/CTE-request-headers.json"
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And <PERSON><PERSON> responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTE/party/CTE-request-headers.json" and query parameters "get/CTE/party/CTE-query-params.json" will respond "200:get/CTE/party/CTE-response.json"
    And the Interac API Get Incoming Request for Payment endpoint for clientId "123456789" called with headers "get/CTE/interac/CTE-request-headers.json" will respond "200:get/CTE/interac/CTE-response.json"
    And DynamoDB responds with "<dynamodb_response>" for "PUT" operation
    When the Get incoming money request is processed
    Then a 500 status is received with body "<response_body>"
    And the log message contains information from file "<log_file>"
    Examples:
      | dynamodb_response | response_body              | log_file                              |
      | TIMEOUT           | get/CTE/CTE1-response.json | get/CTE/logging/CTE1_service_log.json |
      | 400               | get/CTE/CTE2-response.json | get/CTE/logging/CTE2_service_log.json |
      | 500               | get/CTE/CTE3-response.json | get/CTE/logging/CTE3_service_log.json |

  Scenario: CTE4 - save Item operation from AWS DynamoDB API returns 200 for happy path
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTE/CTE-request-headers.json"
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTE/party/CTE-request-headers.json" and query parameters "get/CTE/party/CTE-query-params.json" will respond "200:get/CTE/party/CTE-response.json"
    And the Interac API Get Incoming Request for Payment endpoint for clientId "123456789" called with headers "get/CTE/interac/CTE-request-headers.json" will respond "200:get/CTE/interac/CTE-response.json"
    When the Get incoming money request is processed
    Then a 200 status is received with body "get/CTE/CTE4-response.json"
    And the dynamoDB database is updated with "get/CTE/dynamoDB/CTE4_dynamoDB_State.json"