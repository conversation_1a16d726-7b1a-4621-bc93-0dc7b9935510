Feature: Incoming money request - Component Tests CTAx - Documentation - https://wiki.bnc.ca/x/UQZ5h

  Scenario: CTA1 - DynamoDB does not reply
    Given a Get incoming money request for Interac money request id "*********" with headers "get/CTA/CTA-request-headers.json"
    And DynamoDB responds with "TIMEOUT" for "GET" operation
    When the Get incoming money request is processed
    Then a 500 status is received with body "get/CTA/CTA1-response.json"
    And the log message contains information from file "get/CTA/logging/CTA1_service_log.json"

  Scenario: CTA2 - <PERSON>DB failed with HTTP-400 error
    Given a Get incoming money request for Interac money request id "*********" with headers "get/CTA/CTA-request-headers.json"
    And DynamoDB responds with "400" for "GET" operation
    When the Get incoming money request is processed
    Then a 500 status is received with body "get/CTA/CTA2-response.json"
    And the log message contains information from file "get/CTA/logging/CTA2_service_log.json"

  Scenario: CTA3 - <PERSON>DB failed with HTTP-500 error
    Given a Get incoming money request for Interac money request id "*********" with headers "get/CTA/CTA-request-headers.json"
    And DynamoDB responds with "500" for "GET" operation
    When the Get incoming money request is processed
    Then a 500 status is received with body "get/CTA/CTA3-response.json"
    And the log message contains information from file "get/CTA/logging/CTA3_service_log.json"

  Scenario: CTA4 - DynamoDB succeeded with HTTP-200 but entity is in an invalid status
    Given a Get incoming money request for Interac money request id "*********" with headers "get/CTA/CTA-request-headers.json"
    And DynamoDB is populated with information from body "get/CTA/dynamoDB/CTA4_prepopulated.json"
    And DynamoDB responds with "200" for "GET" operation
    When the Get incoming money request is processed
    Then a 400 status is received with body "get/CTA/CTA4-response.json"
    And the log message contains information from file "get/CTA/logging/CTA4_service_log.json"