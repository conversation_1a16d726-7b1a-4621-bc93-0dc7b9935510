Feature: Incoming money request - Component Tests CTMx - Documentation: https://wiki.bnc.ca/x/blHWgw

  Scenario: CTM1 - Contract is not respected by client application
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTM/CTM1-request-headers.json"
    When the Get incoming money request is processed
    Then a 400 status is received with body "get/CTM/CTM1-response.json"
    And the log message contains information from file "get/CTM/logging/CTM1_service_log.json"