Feature: Incoming money request - Unsuccessful Component Tests CTDx - Documentation: https://wiki.bnc.ca/x/0F-Wgw

  Scenario: CTD1 - Interac API is not reachable
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTD/CTD-request-headers.json"
    And ok<PERSON> responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTD/party/CTD-request-headers.json" and query parameters "get/CTD/party/CTD-query-params.json" will respond "200:get/CTD/party/CTD-response.json"
    And the Interac API Get Incoming Request for Payment endpoint for clientId "123456789" called with headers "get/CTD/interac/CTD-request-headers.json" will respond "timeout"
    When the Get incoming money request is processed
    Then a 500 status is received with body "get/CTD/CTD1-response.json"
    And the log message contains information from file "get/CTD/logging/CTD1_service_log.json"

  Scenario: CTD2 - Interac API returns HTTP 400
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTD/CTD-request-headers.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTD/party/CTD-request-headers.json" and query parameters "get/CTD/party/CTD-query-params.json" will respond "200:get/CTD/party/CTD-response.json"
    And the Interac API Get Incoming Request for Payment endpoint for clientId "123456789" called with headers "get/CTD/interac/CTD-request-headers.json" will respond "400:get/CTD/interac/CTD2-response.json"
    When the Get incoming money request is processed
    Then a 400 status is received with body "get/CTD/CTD2-response.json"
    And the log message contains information from file "get/CTD/logging/CTD2_service_log.json"

  Scenario Outline: <CTD> - Interac API returns <scenario>
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTD/CTD-request-headers.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTD/party/CTD-request-headers.json" and query parameters "get/CTD/party/CTD-query-params.json" will respond "200:get/CTD/party/CTD-response.json"
    And the Interac API Get Incoming Request for Payment endpoint for clientId "123456789" called with headers "get/CTD/interac/CTD-request-headers.json" will respond "<interac_response_code>"
    When the Get incoming money request is processed
    Then a <money_request_response_code> status is received with body "get/CTD/<CTD>-response.json"
    And the log message contains information from file "get/CTD/logging/<CTD>_service_log.json"
    Examples:
      | CTD  | scenario | interac_response_code | money_request_response_code |
      | CTD3 | HTTP 401 | 401                   | 500                         |
      | CTD4 | HTTP 403 | 403                   | 500                         |
      | CTD5 | HTTP 429 | 429                   | 500                         |
      | CTD6 | HTTP 500 | 500                   | 500                         |

  Scenario: CTD7 - Interac API returns HTTP 503
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTD/CTD-request-headers.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTD/party/CTD-request-headers.json" and query parameters "get/CTD/party/CTD-query-params.json" will respond "200:get/CTD/party/CTD-response.json"
    And the Interac API Get Incoming Request for Payment endpoint for clientId "123456789" called with headers "get/CTD/interac/CTD-request-headers.json" will respond "503:get/CTD/interac/CTD7-response.json"
    When the Get incoming money request is processed
    Then a 500 status is received with body "get/CTD/CTD7-response.json"
    And Interac Get Incoming Money Request endpoint is called 3 times
    And the log message contains information from file "get/CTD/logging/CTD7_service_log.json"

  Scenario: CTD8 - Interac API returns HTTP 404
    Given a Get incoming money request for Interac money request id "123456789" with headers "get/CTD/CTD-request-headers.json"
    And okta responds with scopes and statuses
      | scope      | status |
      | partyScope | 201    |
    And the system dates are set to
      | 2025-04-01T22:21:30.292Z |
    And the Party API Get Identifiers endpoint for clientId "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0" called with headers "get/CTD/party/CTD-request-headers.json" and query parameters "get/CTD/party/CTD-query-params.json" will respond "200:get/CTD/party/CTD-response.json"
    And the Interac API Get Incoming Request for Payment endpoint for clientId "123456789" called with headers "get/CTD/interac/CTD-request-headers.json" will respond "404:get/CTD/interac/CTD8-response.json"
    When the Get incoming money request is processed
    Then a 404 status is received with body "EMPTY"
    And the log message contains information from file "get/CTD/logging/CTD8_service_log.json"