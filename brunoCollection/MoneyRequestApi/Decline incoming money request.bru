meta {
  name: Decline incoming money request
  type: http
  seq: 1
}

post {
  url: {{URL}}/{{interacMoneyRequestId}}/decline
  body: json
  auth: none
}

headers {
  Content-Type: application/vnd.ca.bnc.pmt+json
  X-Checkpoint-Key: {{checkpointKey}}
  x-channel-id: {{xChannelID}}
  x-channel-type: WEB
  x-request-id: {{xRequestId}}
  Accept: application/vnd.ca.bnc.pmt+json
  x-client-id: {{xClientId}}
  traceparent: {{traceparent}}
  bncbusinesstraceid: {{bncbusinesstraceid}}
  x-client-agent-id: {{clientAgentId}}
  tracestate: vendorname1=opaqueValue1
}

body:json {
  {
    "FIToFICustomerCreditTransferV08": {
      "groupHeader": {
        "messageIdentification": "d04273e9-0146-45c2-b12e-3ef18ef8589cc",
        "creationDateTime": "2019-05-05T17:29:12.123000Z"
      },
      "creditTransferTransactionInformation": {
        "paymentIdentification": {
          "instructionIdentification": "ec6e707c92024f42bb6be5ab33dedf2d",
          "endToEndIdentification": "a098bdb2-545e-4f0a-9055-a43723aef57"
        },
        "supplementaryData": {
          "declineReason": "decline reason"
        }
      }
    }
  }
}

script:pre-request {
  const { v4: uuidv4 } = require('uuid');
  bru.setEnvVar("bncbusinesstraceid", uuidv4());
}
