name: Pipeline

on: [ push ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false
jobs:
  CI:
    name: pipeline
    uses: NBC-Cards/apidc-common-workflows/.github/workflows/pipeline_java.yml@v3.0.15
    with:
      application-name: 'pmt-incoming-money-request-api'
      ecr_image_name: 'pmt-incoming-money-request-api'
      java_version: '17'
      java_distribution: 'temurin'
      publish_jar: true # set me to false if you don't want to package the jar
      publish_jib: true
      environment: "non_production"
      sonar_quality_gate: 'Sonar way BNC' # could be removed. Default value is 'Sonar way'
      sonar_quality_profile: 'java:Sonar way BNC'
      sonar_project_key: 'APP7873.ca.bnc.payment:pmt-incoming-money-request-api'
      sonar_project_name: 'pmt-incoming-money-request-api'
      skip-sanity-test: true
      skip-e2e-test: true
      jar_settings_repo_id: 'dev'
      component_test_target: 'component-test'
      #common
      bounded_context: 'et-pmt-proc'
      domain: 'payments'
      #debug
      deploy-from-feature-branch: false
      publish_from_feature_branch: false
      #Oona
      publish_ecr: true
      deployment-ecs-fargate: true # enable the automatic deployment on ecs
      #bnc
      deployment-shared-eks: true # enable the automatic deployment on eks
      prefix-path-helm-value-files: 'deployment'
      substage: 'tu'

    secrets: inherit