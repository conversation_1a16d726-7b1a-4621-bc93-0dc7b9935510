name: CD OONA (ECS)

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read  # This is required for actions/checkout

on:
  workflow_dispatch:
    inputs:
      artifact-version:
        description: 'version to deploy'
        required: true
        type: string
      environment:
        description: 'git environment'
        required: true
        type: choice
        options:
          - 'non_production'
          - 'production'
        default: 'non_production'
      env-ecs:
        description: 'ecs deployment stage'
        type: choice
        required: true
        options:
          - ''
          - 'dev'
          - 'test'
          - 'staging'
          - 'prod'
        default: ''

jobs:
  CD:
    name: CD OONA (ECS)
    uses: NBC-Cards/apidc-cd-workflows/.github/workflows/workflows-continuous-delivery.yml@v3.1.2
    with:
      application-name: 'pmt-incoming-money-request-api'
      artifact-version: ${{ inputs.artifact-version }}
      env-ecs: ${{ inputs.env-ecs }}
      environment: ${{ inputs.environment }}
      # enable the deployment on ecs (OONA)
      deployment-ecs-fargate: true
      domain: 'payments'
      bounded_context: 'et-pmt-proc'
      skip-sanity-test: true
      skip-e2e-test: true
      multi_deploy: false
    secrets: inherit